package com.rongchen.byh.webadmin.upms.service;

import com.rongchen.byh.webadmin.upms.dto.CreditDataWithApplyInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDate;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 授信数据查询优化测试类
 * 验证新的联合查询方法的功能和性能
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-XX
 */
@SpringBootTest
@ActiveProfiles("test")
@Slf4j
public class DisburseDataServiceOptimizationTest {

    @Autowired
    private DisburseDataService disburseDataService;

    /**
     * 测试新的联合查询方法
     */
    @Test
    public void testGetCreditDataWithApplyInfo() {
        // 使用一个测试日期
        LocalDate testDate = LocalDate.of(2025, 5, 2);
        
        log.info("开始测试授信数据与申请信息联合查询，测试日期: {}", testDate);
        
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        
        try {
            // 调用新的优化查询方法
            List<CreditDataWithApplyInfo> result = disburseDataService.getCreditDataWithApplyInfo(testDate);
            
            // 记录结束时间
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            log.info("查询完成，耗时: {} ms，返回记录数: {}", duration, result != null ? result.size() : 0);
            
            // 基本断言
            assertNotNull(result, "查询结果不应为null");
            
            // 如果有数据，验证数据结构
            if (!result.isEmpty()) {
                CreditDataWithApplyInfo firstRecord = result.get(0);
                
                // 验证必要字段不为空
                assertNotNull(firstRecord.getId(), "支用记录ID不应为null");
                assertNotNull(firstRecord.getUserId(), "用户ID不应为null");
                assertNotNull(firstRecord.getCreditStatus(), "授信状态不应为null");
                
                // 验证授信状态为500（还款中）
                assertEquals(Integer.valueOf(500), firstRecord.getCreditStatus(), "授信状态应为500（还款中）");
                
                // 验证是否包含API授信编号（这是优化的重点）
                log.info("第一条记录的API授信编号: {}", firstRecord.getApiCreditNo());
                log.info("第一条记录的授信流水号: {}", firstRecord.getCreditNo());
                log.info("第一条记录的申请类型: {}", firstRecord.getApplyType());
                log.info("第一条记录的线上线下类型: {}", firstRecord.getOnlineType());
                
                // 验证申请类型为0（初筛申请）
                if (firstRecord.getApplyType() != null) {
                    assertEquals(Integer.valueOf(0), firstRecord.getApplyType(), "申请类型应为0（初筛申请）");
                }
                
                // 验证线上线下类型在允许范围内
                if (firstRecord.getOnlineType() != null) {
                    assertTrue(
                        List.of(0, 1, 2, 3, 5, 6, 7).contains(firstRecord.getOnlineType()),
                        "线上线下类型应在允许范围内 (0,1,2,3,5,6,7)"
                    );
                }
            }
            
            log.info("测试通过：授信数据与申请信息联合查询功能正常");
            
        } catch (Exception e) {
            log.error("测试失败：{}", e.getMessage(), e);
            fail("查询过程中发生异常: " + e.getMessage());
        }
    }

    /**
     * 测试空结果情况
     */
    @Test
    public void testGetCreditDataWithApplyInfoEmptyResult() {
        // 使用一个不太可能有数据的未来日期
        LocalDate futureDate = LocalDate.of(2030, 1, 1);
        
        log.info("开始测试空结果情况，测试日期: {}", futureDate);
        
        try {
            List<CreditDataWithApplyInfo> result = disburseDataService.getCreditDataWithApplyInfo(futureDate);
            
            assertNotNull(result, "查询结果不应为null");
            log.info("空结果测试通过，返回记录数: {}", result.size());
            
        } catch (Exception e) {
            log.error("空结果测试失败：{}", e.getMessage(), e);
            fail("查询过程中发生异常: " + e.getMessage());
        }
    }

    /**
     * 性能对比测试（可选）
     * 注意：这个测试需要在有足够数据的环境中运行才有意义
     */
    @Test
    public void testPerformanceComparison() {
        LocalDate testDate = LocalDate.of(2025, 5, 2);
        
        log.info("开始性能对比测试，测试日期: {}", testDate);
        
        // 多次执行以获得更准确的性能数据
        int iterations = 5;
        long totalTime = 0;
        
        for (int i = 0; i < iterations; i++) {
            long startTime = System.currentTimeMillis();
            
            try {
                List<CreditDataWithApplyInfo> result = disburseDataService.getCreditDataWithApplyInfo(testDate);
                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;
                totalTime += duration;
                
                log.info("第 {} 次查询耗时: {} ms，记录数: {}", i + 1, duration, result.size());
                
            } catch (Exception e) {
                log.error("第 {} 次查询失败：{}", i + 1, e.getMessage(), e);
            }
        }
        
        double averageTime = (double) totalTime / iterations;
        log.info("平均查询耗时: {:.2f} ms", averageTime);
        
        // 性能断言（根据实际情况调整阈值）
        assertTrue(averageTime < 5000, "平均查询时间应小于5秒");
    }
}
