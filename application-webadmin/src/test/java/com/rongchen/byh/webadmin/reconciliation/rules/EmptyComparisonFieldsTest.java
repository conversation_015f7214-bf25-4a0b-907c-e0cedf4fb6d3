package com.rongchen.byh.webadmin.reconciliation.rules;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.rongchen.byh.webadmin.reconciliation.context.ReconciliationContext;
import com.rongchen.byh.webadmin.reconciliation.model.FieldDifferenceDetail;
import com.rongchen.byh.webadmin.reconciliation.model.NormalizedCreditRecord;
import com.rongchen.byh.webadmin.reconciliation.rules.dsl.CustomComparisonFunctionRegistry;
import com.rongchen.byh.webadmin.reconciliation.strategy.ComparisonStrategy;
import com.rongchen.byh.webadmin.reconciliation.strategy.JsonDslComparisonStrategy;
import com.rongchen.byh.webadmin.upms.model.DzReconciliationRulesEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.context.ApplicationContext;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * 测试空的 comparisonFields 数组的处理
 */
public class EmptyComparisonFieldsTest {

    @Mock
    private CustomComparisonFunctionRegistry customComparisonFunctionRegistry;
    
    @Mock
    private ApplicationContext applicationContext;

    private RuleStrategyFactory ruleStrategyFactory;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        ruleStrategyFactory = new RuleStrategyFactory(
            customComparisonFunctionRegistry, 
            applicationContext, 
            "com.rongchen.byh.webadmin.reconciliation.rules.custom"
        );
        objectMapper = new ObjectMapper();
    }

    @Test
    void testEmptyComparisonFieldsArray() throws Exception {
        // 准备测试数据
        String ruleContentJson = """
            {
                "ruleId": "XHY_CREDIT_DEFAULT",
                "version": 1,
                "description": "信合元(XHY)默认授信JSON DSL对账规则",
                "matchingConfig": {
                    "ourSideKeyFields": ["ourPartnerCreditNo"],
                    "partnerSideKeyFields": ["partnerPartnerCreditNo"],
                    "allowOneToMany": false
                },
                "comparisonFields": []
            }
            """;

        DzReconciliationRulesEntity ruleEntity = new DzReconciliationRulesEntity();
        ruleEntity.setRuleId("XHY_CREDIT_DEFAULT");
        ruleEntity.setRuleType("JSON_DSL");
        ruleEntity.setRuleContentJson(ruleContentJson);
        ruleEntity.setVersion(1);

        // 测试创建策略不会抛出异常
        ComparisonStrategy strategy = ruleStrategyFactory.createStrategy(ruleEntity);
        assertNotNull(strategy);
        assertTrue(strategy instanceof JsonDslComparisonStrategy);

        // 测试空的comparisonFields数组的比较逻辑
        NormalizedCreditRecord ourRecord = createTestRecord("CREDIT001", "500");
        NormalizedCreditRecord partnerRecord = createTestRecord("CREDIT001", "500");
        
        ReconciliationContext context = new ReconciliationContext();
        context.setProcessingDate(LocalDate.now());
        
        Map<String, Object> strategyParameters = new HashMap<>();
        
        // 执行比较，应该返回空的差异列表
        List<FieldDifferenceDetail> differences = strategy.compare(
            ourRecord, partnerRecord, strategyParameters, context
        );
        
        assertNotNull(differences);
        assertTrue(differences.isEmpty(), "空的comparisonFields应该返回无差异");
    }

    private NormalizedCreditRecord createTestRecord(String creditNo, String status) {
        NormalizedCreditRecord record = new NormalizedCreditRecord();
        record.setPartnerCreditNo(creditNo);
        record.setCreditStatus(status);
        record.setCreditAmount(BigDecimal.valueOf(10000));
        record.setCreditDate(LocalDate.now());
        record.setStatus(status);
        record.setSourceChannel("TEST");
        record.setOriginalRecordId("TEST_ID_" + creditNo);
        return record;
    }
}
