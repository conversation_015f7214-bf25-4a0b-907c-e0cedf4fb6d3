package com.rongchen.byh.webadmin.upms.vo.export;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class OutstandingLoanBalanceVo {
    @Schema(description = "放款编号", example = "LOAN001")
    private String loanNo;

    @Schema(description = "客户", example = "客户A")
    private String customer;

    @Schema(description = "合作方", example = "合作方B")
    private String partner;

    @Schema(description = "资金方", example = "资金方C")
    private String funder;

    @Schema(description = "起始日期", example = "2025-01-01")
    private String startDate;

    @Schema(description = "终止日期", example = "2025-02-01")
    private String endDate;

    @Schema(description = "持续天数", example = "30")
    private Long durationDays;

    @Schema(description = "放款日期", example = "2025-01-15")
    private String loanDate;

    @Schema(description = "融担费率", example = "0.05")
    private String guaranteeRate;

    @Schema(description = "每日在贷余额", example = "10000.00")
    private Double dailyLoanBalance;

    @Schema(description = "每日融担费", example = "500.00")
    private Double dailyGuaranteeFee;

    @Schema(description = "统计期内累计融担费", example = "15000.00")
    private Double cumulativeGuaranteeFee;

    @Schema(description = "统计月份", example = "2025-01")
    private String statisticMonth;

    @Schema(description = "原始资金方代码", example = "XJ_HB2")
    private String originalFundCode;
}
