package com.rongchen.byh.webadmin.reconciliation.model;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 通用标准化交易记录接口。
 * <p>
 * 所有具体的标准化交易模型（如借款、还款、授信等）都应实现此接口。
 * 它定义了所有标准化记录共有的元数据字段访问器。
 */
public interface NormalizedTransaction {

    /**
     * 获取此记录的源渠道编码。
     * 
     * @return 源渠道编码 (例如 "XHY", "MYSYSTEM")
     */
    String getSourceChannel();

    /**
     * 设置此记录的源渠道编码。
     * 
     * @param sourceChannel 源渠道编码
     */
    void setSourceChannel(String sourceChannel);

    /**
     * 获取此记录在源系统/文件中的原始ID或唯一标识。
     * 
     * @return 原始记录ID
     */
    String getOriginalRecordId();

    /**
     * 设置此记录在源系统/文件中的原始ID或唯一标识。
     * 
     * @param originalRecordId 原始记录ID
     */
    void setOriginalRecordId(String originalRecordId);

    /**
     * 获取附加数据Map，用于存储未在标准字段中定义的其他源数据或中间处理数据。
     * 
     * @return 附加数据Map
     */
    Map<String, Object> getAdditionalData();

    /**
     * 设置附加数据Map。
     * 
     * @param additionalData 附加数据Map
     */
    void setAdditionalData(Map<String, Object> additionalData);

    /**
     * 获取用于对账金额汇总的 核心金额字段。
     * 每个具体的交易类型实现类应返回其用于金额累加的核心金额字段。
     * 例如，借款返回借款本金，还款返回实还总额。
     * 
     * @return 对账相关的金额，如果此交易类型不适用金额汇总或金额为空，则可以返回 BigDecimal.ZERO 或 null。
     */
    BigDecimal getReconciliationAmount();

    // 思考：是否需要一个通用的获取主键的方法，用于匹配和日志记录？
    // 或者一个获取所有字段的Map的方法？
    // Map<String, Object> getAllFieldsAsMap();
    // 或者由具体的子类提供各自的核心业务ID的getter
}