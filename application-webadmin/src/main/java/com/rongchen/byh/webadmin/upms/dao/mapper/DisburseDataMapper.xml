<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.webadmin.upms.dao.DisburseDataMapper">

    <resultMap id="BaseResultMap" type="com.rongchen.byh.webadmin.upms.model.DisburseData">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="productId" column="product_id" jdbcType="BIGINT"/>
            <result property="creditNo" column="credit_no" jdbcType="VARCHAR"/>
            <result property="loanNo" column="loan_no" jdbcType="VARCHAR"/>
            <result property="saleNo" column="sale_no" jdbcType="VARCHAR"/>
            <result property="creditAmount" column="credit_amount" jdbcType="DECIMAL"/>
            <result property="grossInterest" column="gross_interest" jdbcType="DECIMAL"/>
            <result property="creditStatus" column="credit_status" jdbcType="INTEGER"/>
            <result property="creditTime" column="credit_time" jdbcType="TIMESTAMP"/>
            <result property="loanTime" column="loan_time" jdbcType="TIMESTAMP"/>
            <result property="repaymentTime" column="repayment_time" jdbcType="TIMESTAMP"/>
            <result property="periods" column="periods" jdbcType="INTEGER"/>
            <result property="purposeLoan" column="purpose_loan" jdbcType="VARCHAR"/>
            <result property="yearRete" column="year_rete" jdbcType="VARCHAR"/>
            <result property="repaymentMethod" column="repayment_method" jdbcType="VARCHAR"/>
            <result property="saleRepayAmount" column="sale_repay_amount" jdbcType="DECIMAL"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,product_id,
        credit_no,loan_no,sale_no,
        credit_amount,gross_interest,credit_status,
        credit_time,loan_time,repayment_time,
        periods,purpose_loan,year_rete,
        repayment_method,sale_repay_amount,create_time
    </sql>
    <select id="list" resultType="com.rongchen.byh.webadmin.upms.vo.order.DisburseOrderListVo">
        select distinct
            dd.id,
            (select `name` from channel_data where id = u.channel_id) userChannelName,
            case u.source_mode
                when 0 then '线上'
                when 1 then '线下'
                when 2 then '空中放款'
                when 3 then '全流程API'
                else ''
            end sourceMode,
            ud.web_name userName,
            u.mobile,
            '产品名xxx' productName,
            credit_amount,
            '' otherFee,
            credit_status,
            case credit_status
            when 100 then '授信中'
            when 200 then '授信失败'
            when 300 then '放款中'
            when 400 then '放款失败'
            when 500 then '还款中'
            when 600 then '已结清'
            else '未知状态'
            end creditStatusName,
            '' stageStatusName,
            '' channelName,
            dd.create_time,
            ifnull(std.store_name,'') storeName,
            ifnull(sfd.user_name,'') saleUserName,
            ifnull(gd.group_name,'') groupName
        from disburse_data dd
        left join user_detail ud on dd.user_id = ud.user_id
        left join user_data u on dd.user_id = u.id
        LEFT JOIN user_staff us on us.user_id = u.id
        LEFT JOIN staff_data sfd on sfd.id = us.staff_id
        LEFT JOIN staff_group_relation sgr on sgr.staff_id = sfd.id
        LEFT JOIN group_data gd on gd.id = sgr.group_id
        LEFT JOIN group_store_relation gsr on gsr.group_id = sgr.group_id
        LEFT JOIN store_data std on std.id = gsr.store_id
        <where>
            <if test="disburseOrderId != null">
                and dd.id = #{disburseOrderId}
            </if>
            <if test="userName != null">
                and ud.web_name like concat('%',#{userName},'%')
            </if>
            <if test="creditStatus != null">
                and dd.credit_status = #{creditStatus}
            </if>
            <if test="createStartTime != null and createStartTime != '' and createEndTime != null and createEndTime != ''">
                and dd.create_time >= #{createStartTime} and dd.create_time &lt; #{createEndTime}
            </if>
            <if test="mobile != null and mobile != ''">
                and u.mobile = #{mobile}
            </if>
            <if test="storeName != null and storeName != ''">
                and std.store_name = #{storeName}
            </if>
            <if test="saleUserName != null and saleUserName != ''">
                and sfd.user_name = #{saleUserName}
            </if>
            <if test="groupName != null and groupName != ''">
                and gd.group_name = #{groupName}
            </if>
            <if test="sysUserId != null and sysUserId != '' and sysRoleName != null and sysRoleName == '门店主管'">
                and std.sys_user_id = #{sysUserId}
            </if>
            <if test="sysUserId != null and sysUserId != '' and sysRoleName != null and sysRoleName == '门店组长'">
                and gd.sys_user_id = #{sysUserId}
            </if>
            <if test="sysUserId != null and sysUserId != '' and sysRoleName != null and sysRoleName == '门店销售'">
                and sfd.sys_user_id = #{sysUserId}
            </if>
            <if test="channelId != null and channelId != ''">
                and u.channel_id IN
                <foreach item="item" index="index" collection="channelId.split(',')"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by dd.create_time desc
    </select>
    <select id="detail" resultType="com.rongchen.byh.webadmin.upms.vo.order.DisburseOrderDetailVo">
        select
            dd.id,
            dd.user_id userId,
            dd.credit_no creditOrderId,
            '' institutionName,
            (select `name` from channel_data where id = ud.channel_id) userChannelName,
            case ud.source_mode
                when 0 then '线上'
                when 1 then '线下'
                when 2 then '空中放款'
                when 3 then '全流程API'
                else ''
            end sourceMode,
            '蚂蚁智慧' capitalName,
            (select web_name from user_detail where user_id = dd.user_id) userName,
            ud.mobile,
            '' productName,
            dd.credit_amount,
            dd.purpose_loan,
            dd.repayment_method,
            concat(dd.year_rete,'%') yearRete,
            '1' periodsUnit,
            '1' periodsUnitValue,
            dd.periods,
            (select user_bank_card.bank_name from user_bank_card where user_id = dd.user_id),
            dd.credit_amount loanAmount,
            dd.credit_amount arrivalAmount,
            dd.create_time loanApplyTime,
            dd.loan_time loanTime,
            dd.loan_no loanApplyNo,
            dd.loan_time channelLoanFinishTime,
            IF(rs.id is null, '否', '是') isCreateBill,
            rs.create_time billCreateTime,
            rs.create_time accountTime,
            dd.contract_id contractId
        from disburse_data dd
            left join user_data ud on dd.user_id = ud.id
            left join (select * from repay_schedule where id in (select min(id) from repay_schedule group by disburse_id)) rs on dd.id = rs.disburse_id
        where dd.id = #{id}
    </select>
    <select id="billList" resultType="com.rongchen.byh.webadmin.upms.vo.bill.BillListVo">
        select distinct
            dd.id,
            (select name from channel_data where id = ud.channel_id) userChannelName,
            case ud.source_mode
                when 0 then '线上'
                when 1 then '线下'
                when 2 then '空中放款'
                when 3 then '全流程API'
                else ''
            end sourceMode,
            u.web_name userName,
            ud.mobile,
            u.risk_level,
            dd.credit_status,
            case dd.fund_code
                when 'XJ_HB2' then '湖北消金'
                when 'XJ_WP' then '唯品消金'
                when 'XJ_ZY_DY' then '中原东营联合贷'
                when 'YH_HY_HE' then '韩亚银行'
                when 'YH_MS' then '蒙商消金'
                when 'YH_ZX' then '振兴银行'
                when 'YH_LH_HE' then '海尔蓝海银行'
                else dd.fund_code
            end fundCode,
            cd.`name` capitalName,
            LEAST(rs_count.repaid_count + 1, 12)  totalTerm,
            case dd.credit_status
            when 100 then '授信中'
            when 200 then '授信失败'
            when 300 then '放款中'
            when 400 then '放款失败'
            when 500 then '还款中'
            when 600 then '已结清'
            else '未知状态'
            end creditStatusName,
            '' productName,
            COALESCE(
                (
                    SELECT MIN(ns.repay_ownb_date) FROM repay_schedule cs JOIN repay_schedule ns  ON cs.disburse_id = ns.disburse_id
                    WHERE cs.disburse_id = dd.id AND ((cs.term_status IN ('B', 'O', 'L') AND cs.settle_flag = 'RUNNING' AND cs.repay_ownb_date != CURDATE())
                        OR (cs.settle_flag = 'CLOSE' AND ns.settle_flag = 'RUNNING'))
                    AND ns.repay_ownb_date > cs.repay_ownb_date
                ),
                rs1.repay_ownb_date
            ) AS repaymentDate,
            CASE
                WHEN dd.credit_status = 600 THEN '已结清'
                WHEN rs4.repay_ownb_date = CURDATE() AND ss.repay_ownb_date = CURDATE() THEN
                    CASE
                        WHEN rs4.settle_flag = 'CLOSE' AND ss.settle_flag = 'CLOSE' THEN '已还'
                        WHEN rs4.settle_flag != 'CLOSE' AND ss.settle_flag != 'CLOSE' THEN '未还'
                        ELSE '部分还款'
                    END
                ELSE '未还'
            END combinedFlag,
            rs2.date_pay_time lastRepayFinishTime,
            ifnull(dd.credit_amount, 0) repayTotalPrincipalAmt,
            ifnull(dd.gross_interest, 0) repayTotalInterestAmt,
            ifnull(dd.sale_repay_amount + dd.credit_amount + gross_interest, 0) repayTotalAmt,
            ifnull(rs3.actualOtherAmt, 0) actualTotalAmt,
            ifnull(rs3.actualTotalAmt, 0) actualOtherAmt,
            ifnull(rs3.repayOtherAmt, 0) repayOtherAmt,
            dd.create_time,
            ifnull(std.store_name,'') storeName,
            ifnull(sfd.user_name,'') saleUserName,
            ifnull(gd.group_name,'') groupName,
            case when max(rs5.compensation_status) = 1 and rs5.num > 0 then '代偿'
                when max(rs5.compensation_status) = 2 and rs5.num > 0 then '回购'
                else ''
            end compensateStatus
        from disburse_data dd
        left join user_data ud on dd.user_id = ud.id
        left join user_detail u on dd.user_id = u.user_id
        left JOIN capital_data cd on cd.id = dd.capital_id
        left join (select count(*) num, compensation_status, disburse_id from repay_schedule group by disburse_id, compensation_status) rs5 on dd.id = rs5.disburse_id
        LEFT JOIN (SELECT disburse_id,SUM(CASE WHEN (date_pay IS NOT NULL AND date_pay &lt;= CURDATE()) OR (date_pay IS NULL AND repay_ownb_date &lt;= CURDATE()) THEN 1 ELSE 0 END) repaid_count
                    FROM repay_schedule GROUP BY disburse_id) rs_count ON dd.id = rs_count.disburse_id
        left join (select repay_ownb_date, disburse_id, settle_flag from repay_schedule where id in (select min(id) from repay_schedule group by disburse_id)) rs1 on dd.id = rs1.disburse_id
        left join (select * from repay_schedule where id in (select max(id) from repay_schedule where settle_flag = 'CLOSE' group by disburse_id)) rs2 on dd.id = rs2.disburse_id
        left join (select sum(if(settle_flag = 'CLOSE', term_guarantor_fee, 0)) actualOtherAmt,
                          sum(if(settle_flag = 'CLOSE', total_amt, 0)) actualTotalAmt,
                          sum(term_guarantor_fee) repayOtherAmt, disburse_id   from repay_schedule group by disburse_id) rs3 on dd.id = rs3.disburse_id
        LEFT JOIN ( SELECT repay_term, term_status, settle_flag, disburse_id, repay_ownb_date FROM repay_schedule where repay_ownb_date = CURDATE() ) rs4 ON dd.id = rs4.disburse_id
        LEFT JOIN ( SELECT disburse_id, MAX( repay_term + 0 ) total_term FROM repay_schedule GROUP BY disburse_id ) t ON rs4.disburse_id = t.disburse_id
        LEFT JOIN (SELECT disburse_id, settle_flag, repay_ownb_date FROM sale_schedule where repay_ownb_date = CURDATE()) ss ON ss.disburse_id = dd.id
        LEFT JOIN user_staff us on us.user_id = ud.id
        LEFT JOIN staff_data sfd on sfd.id = us.staff_id
        LEFT JOIN staff_group_relation sgr on sgr.staff_id = sfd.id
        LEFT JOIN group_data gd on gd.id = sgr.group_id
        LEFT JOIN group_store_relation gsr on gsr.group_id = sgr.group_id
        LEFT JOIN store_data std on std.id = gsr.store_id
        <where>
            dd.credit_status in('500','600')
            <if test="disburseOrderId != null">
                and dd.id = #{disburseOrderId}
            </if>
            <if test="billId != null">
                and dd.id = #{billId}
            </if>
            <if test="userName != null and userName != ''">
                and u.web_name like concat('%',#{userName},'%')
            </if>
            <if test="mobile != null and mobile != ''">
                and ud.mobile = #{mobile}
            </if>
            <if test="status != null and status != ''">
                and dd.credit_status = #{status}
            </if>
            <if test="createStartTime != null and createStartTime != '' and createEndTime != null and createEndTime != ''">
                and dd.create_time >= #{createStartTime} and dd.create_time &lt; #{createEndTime}
            </if>
            <if test="latestRepayStartTime != null and latestRepayStartTime != '' and latestRepayEndTime != null and latestRepayEndTime != ''">
                and rs2.date_pay_time >= #{latestRepayStartTime} and rs2.date_pay_time &lt; #{latestRepayEndTime}
            </if>
            <if test="storeName != null and storeName != ''">
                and std.store_name = #{storeName}
            </if>
            <if test="saleUserName != null and saleUserName != ''">
                and sfd.user_name = #{saleUserName}
            </if>
            <if test="groupName != null and groupName != ''">
                and gd.group_name = #{groupName}
            </if>
            <if test="sysUserId != null and sysUserId != '' and sysRoleName != null and sysRoleName == '门店主管'">
                and std.sys_user_id = #{sysUserId}
            </if>
            <if test="sysUserId != null and sysUserId != '' and sysRoleName != null and sysRoleName == '门店组长'">
                and gd.sys_user_id = #{sysUserId}
            </if>
            <if test="sysUserId != null and sysUserId != '' and sysRoleName != null and sysRoleName == '门店销售'">
                and sfd.sys_user_id = #{sysUserId}
            </if>
            <if test="sysRoleName != null and sysRoleName == '线下催收'">
                and ud.channel_id not in('26')
            </if>
            <if test="capitalName != null and capitalName != ''">
                and cd.name = #{capitalName}
            </if>
            <if test="channelId != null and channelId != ''">
                and ud.channel_id IN
                <foreach item="item" index="index" collection="channelId.split(',')"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="combinedFlag != null and combinedFlag != ''">
                <foreach item="flag" index="index" collection="combinedFlag.split(',')" open="and (" separator="or" close=")">
                    (
                        CASE
                            WHEN dd.credit_status = 600 THEN '已结清'
                            WHEN rs4.repay_ownb_date = CURDATE() AND ss.repay_ownb_date = CURDATE() THEN
                                CASE
                                    WHEN rs4.settle_flag = 'CLOSE' AND ss.settle_flag = 'CLOSE' THEN '已还'
                                    WHEN rs4.settle_flag = 'RUNNING' AND ss.settle_flag = 'RUNNING' THEN '未还'
                                    ELSE '部分还款'
                                END
                            ELSE '未还'
                        END = #{flag}
                    )
                </foreach>
            </if>
            <if test="compensateStatus != null">
                <if test="compensateStatus == 1">
                    and rs5.compensation_status = 1 and rs5.num > 0
                </if>
                <if test="compensateStatus == 2">
                    and rs5.compensation_status = 2 and rs5.num > 0
                </if>
            </if>
        </where>
        group by dd.id
        order by dd.create_time desc
    </select>
    <select id="billDetail" resultType="com.rongchen.byh.webadmin.upms.vo.bill.BillDetailVo">
        select
            dd.id,
            dd.credit_status,
            case dd.credit_status
                when 100 then '授信中'
                when 200 then '授信失败'
                when 300 then '放款中'
                when 400 then '放款失败'
                when 500 then '还款中'
                when 600 then '已结清'
                else '未知状态'
                end creditStatusName,
            '' productName,
            u.web_name userName,
            ud.mobile,
            u.id_number,
            u.risk_level,
            ubc.bank_name,
            ubc.bank_account,
            '' repayChannelName,
            rs1.repay_ownb_date repaymentDate,
            '' repaymentMethod,
            concat(dd.year_rete,'%') yearRete,
            '1' periodsUnit,
            dd.periods,
            case dd.fund_code
                when 'XJ_HB2' then '湖北消金'
                when 'XJ_WP' then '唯品消金'
                when 'XJ_ZY_DY' then '中原东营联合贷'
                when 'YH_HY_HE' then '韩亚银行'
                when 'YH_MS' then '蒙商消金'
                when 'YH_ZX' then '振兴银行'
                when 'YH_LH_HE' then '海尔蓝海银行'
                else dd.fund_code
            end fundCode,
            ifnull((dd.credit_amount + dd.gross_interest + sale_repay_amount), 0) repayTotalAmt,
            ifnull((rs2.reAmt + ss.saAmt), 0) actualTotalAmt,
            ifnull(dd.credit_amount, 0) repayTotalPrincipalAmt,
            ifnull((rs2.rePrin + ss.saPrin), 0) actualTotalPrincipalAmt,
            ifnull(dd.gross_interest, 0) repayTotalInterestAmt,
            ifnull((rs2.reInt + ss.saInt), 0) actualTotalInterestAmt,
            ifnull((rs2.reTotalFin + ss.saTotalFin), 0) repayTotalPenaltyAmt,
            ifnull((rs2.reAcTotalFin + ss.saAcTotalFin), 0) actualTotalPenaltyAmt,
            ifnull(rs2.reToalGuFee, 0) repayOtherAmt,
            ifnull(rs2.reAcGuFee, 0) actualOtherAmt,
            case  u.relationship_one
                WHEN '1' THEN
                    '父母'
                WHEN '2' THEN
                    '配偶'
                WHEN '3' THEN
                    '子女'
                ELSE
                    ''
             END as relationship_one,
            u.emergency_name_one,
            u.emergency_mobile_one,
            case u.relationship_two
                WHEN '20' THEN
                    '朋友'
                WHEN '30' THEN
                    '同事'
                WHEN '10' THEN
                    '亲属'
                WHEN '99' THEN
                    '其他'
                ELSE
                    ''
                END as relationship_two,
            u.emergency_name_two,
            u.emergency_mobile_two,
            acr.channel apiChannel
        from disburse_data dd
                 left join user_data ud on dd.user_id = ud.id
                 left join user_detail u on dd.user_id = u.user_id
                 left join user_bank_card ubc on dd.user_id = ubc.user_id
                 LEFT JOIN api_credit_record acr ON acr.user_id = dd.user_id and acr.credit_status = '2'
                 left join (select repay_ownb_date, disburse_id from repay_schedule where id in (select min(id) from repay_schedule group by disburse_id)) rs1 on dd.id = rs1.disburse_id
                 left join (select disburse_id, sum(if(settle_flag = 'CLOSE', total_amt, 0)) reAmt, sum(if(settle_flag = 'CLOSE', term_ret_prin, 0)) rePrin, sum(if(settle_flag = 'CLOSE', term_ret_int, 0)) reInt, sum(term_ret_fint) reTotalFin, sum(if(settle_flag = 'CLOSE', term_ret_fint, 0)) reAcTotalFin, sum(term_guarantor_fee) reToalGuFee, sum(if(settle_flag = 'CLOSE', term_guarantor_fee, 0)) reAcGuFee from repay_schedule group by disburse_id) rs2 on dd.id = rs2.disburse_id
                 left join (select disburse_id, sum(if(settle_flag = 'CLOSE', total_amt, 0)) saAmt, sum(if(settle_flag = 'CLOSE', term_ret_prin, 0)) saPrin, sum(if(settle_flag = 'CLOSE', term_ret_int, 0)) saInt, sum(term_ret_fint) saTotalFin, sum(if(settle_flag = 'CLOSE', term_ret_fint, 0)) saAcTotalFin from sale_schedule  group by disburse_id) ss on dd.id = ss.disburse_id
        where dd.id = #{id}
    </select>
    <select id="billPeriodDetail" resultType="com.rongchen.byh.webadmin.upms.vo.bill.BillPeriodDetailVo">
        select
            id billRepayNo,
            disburse_id billId,
            settle_flag,
            case settle_flag
                when 'RUNNING' then '未结'
                when 'CLOSE' then '已结'
                when 'REPAYING' then '还款中'
                else '未知状态'
            end settleFlagName,
            repay_term,
            repay_ownb_date datePay,
            ifnull(total_amt, 0) totalAmt,
            ifnull(if(settle_flag = 'CLOSE', total_amt, 0), 0) totalRealAmt,
            ifnull(term_ret_prin, 0) termRetPrin,
            ifnull(if(settle_flag = 'CLOSE', term_ret_prin, 0), 0) termRealPrin,
            ifnull(term_ret_int, 0) termRetInt,
            ifnull(if(settle_flag = 'CLOSE', term_ret_int, 0),0) termRealInt,
            0 termRetSerFee,
            0 termRealSerFee,
            '融担费' otherFeeType,
            ifnull(term_guarantor_fee, 0) termRetOtherFee,
            ifnull(if(settle_flag = 'CLOSE', term_guarantor_fee, 0), 0) termRealOtherFee,
            ifnull(if(settle_flag = 'L', to_days(current_date() - repay_owne_date), 0), 0) repayStatus,
            ifnull(term_ret_fint, 0) overdueInterest,
            ifnull(term_overdue_guarantor_fee, 0) termOverdueGuarantorFee,
            ifnull(if(settle_flag = 'CLOSE', term_ret_fint, 0), 0) realOverdueInterest,
            1 repayType,
            case when compensation_status = 1  then '代偿'
                 when compensation_status = 2 then '回购'
                 else ''
                end compensateStatus,
            case when compensation_status = 1  then (term_ret_prin + term_ret_int)
                 when compensation_status = 2 then (term_ret_prin + term_ret_int)
                end compensateAmount
        from repay_schedule
        where disburse_id = #{id}
        union all
        select
            id billRepayNo,
            disburse_id billId,
            settle_flag,
            case settle_flag
                when 'RUNNING' then '未结'
                when 'CLOSE' then '已结'
                when 'REPAYING' then '还款中'
                else '未知状态'
                end settleFlagName,
            repay_term,
            repay_ownb_date datePay,
            ifnull(total_amt, 0) totalAmt,
            ifnull(if(settle_flag = 'CLOSE', total_amt, 0), 0) totalRealAmt,
            ifnull(term_ret_prin, 0) termRetPrin,
            ifnull(if(settle_flag = 'CLOSE', term_ret_prin, 0), 0) termRealPrin,
            ifnull(term_ret_int, 0) termRetInt,
            ifnull(if(settle_flag = 'CLOSE', term_ret_int, 0),0) termRealInt,
            0 termRetSerFee,
            0 termRealSerFee,
            '融担费' otherFeeType,
            0 termRetOtherFee,
            0 termRealOtherFee,
            ifnull(if(settle_flag = 'L', to_days(current_date() - repay_owne_date), 0), 0) repayStatus,
            ifnull(term_ret_fint, 0) overdueInterest,
            0 termOverdueGuarantorFee,
            ifnull(if(settle_flag = 'CLOSE', term_ret_fint, 0), 0) realOverdueInterest,
            2 repayType,
            '' compensateStatus,
            null compensateAmount
        from sale_schedule
        where disburse_id = #{id}
    </select>
    <select id="billScheduleRepayRecord" resultType="com.rongchen.byh.webadmin.upms.vo.bill.BillRepayRecordVo">
        select
            rao.repay_apply_no payNo,
            rao.repay_apply_no tradeNo,
            case rao.pay_method
                when 1 then (rs.term_ret_prin + rs.term_ret_int)
                else rs.total_amt
                end payAmt,
            rao.repay_status payStatus,
            case rao.repay_status
                when 0 then '待审核'
                when 1 then '还款成功'
                when 2 then '还款失败'
                else '未知状态'
            end payStatusName,
            case rao.pay_method
                when 1 then '代偿支付'
                else ''
            end payMethod,
            case rao.repay_type
                when 1 then '主动提交还款'
                when 2 then '自动跑批扣款'
                when 3 then '逾期自动跑批扣款'
                when 4 then '内部线下还款发起'
                when 8 then '内部发起提前结清'
                else '未知方式'
            end payMethodName,
            reason channelReturnMsg,
            '' remark,
            case rao.repay_status
                when 0 then rao.create_time
                when 1 then rao.response_time
                when 2 then rao.response_time
                else rao.create_time
                end datePayTime
            from repay_schedule rs
            left join repay_schedule_apply rao on rs.id = rao.repay_schedule_id
            where rs.id = #{id} order by rao.create_time desc ,rao.response_time desc
    </select>
    <select id="billSaleRepayRecord" resultType="com.rongchen.byh.webadmin.upms.vo.bill.BillRepayRecordVo">
        select
            rsa.repay_apply_no payNo,
            rsa.repay_apply_no tradeNo,
            ss.total_amt payAmt,
            rsa.repay_status payStatus,
            case rsa.repay_status
                when 0 then '待审核'
                when 1 then '还款成功'
                when 2 then '还款失败'
                else '未知状态'
                end payStatusName,
            '' payMethod,
            case rsa.repay_type
                when 1 then '主动提交还款'
                when 2 then '自动跑批扣款'
                when 3 then '逾期自动跑批扣款'
                when 4 then '内部线下还款发起'
                when 8 then '内部发起提前结清'
                else '未知方式'
                end payMethodName,
            reason channelReturnMsg,
            '' remark,
            case rsa.repay_status
                when 0 then rsa.create_time
                when 1 then rsa.response_time
                when 2 then rsa.response_time
                else rsa.create_time
            end datePayTime
        from sale_schedule ss
                 left join repay_sale_apply rsa on ss.id = rsa.sale_schedule_id
        where ss.id = #{id} order by rsa.create_time desc ,rsa.response_time desc

    </select>
    <select id="overdueList" resultType="com.rongchen.byh.webadmin.upms.vo.bill.OverdueListVo">
        SELECT DISTINCT
            dd.id,
            ( SELECT name FROM channel_data WHERE id = ud.channel_id ) userChannelName,
            case ud.source_mode
                when 0 then '线上'
                when 1 then '线下'
                when 2 then '空中放款'
                when 3 then '全流程API'
            else ''
            end sourceMode,
            u.web_name userName,
            ud.mobile,
            u.risk_level,
            dd.credit_status,
            CASE
                WHEN rs_count.repaid_count IS NOT NULL THEN CONCAT(rs_count.repaid_count, '/', 12)
                WHEN ss_count.repaid_count IS NOT NULL THEN CONCAT(ss_count.repaid_count, '/', 12)
                ELSE '0/12'
            END totalTerm,
            CASE
                WHEN DATEDIFF(CURRENT_DATE, earliest_rs.repay_inte_date) IS NOT NULL THEN DATEDIFF(CURRENT_DATE, earliest_rs.repay_inte_date)
                WHEN DATEDIFF(CURRENT_DATE, earliest_ss.repay_inte_date) IS NOT NULL THEN DATEDIFF(CURRENT_DATE, earliest_ss.repay_inte_date)
                ELSE '0'
            END overdueDays,
            CASE
                dd.credit_status
                WHEN 100 THEN '授信中'
                WHEN 200 THEN '授信失败'
                WHEN 300 THEN '放款中'
                WHEN 400 THEN '放款失败'
                WHEN 500 THEN '还款中'
                WHEN 600 THEN '已结清'
                ELSE '未知状态'
            END creditStatusName,
            '' productName,
            CASE
                WHEN earliest_rs.repay_ownb_date > CURDATE() THEN '未还'
                WHEN earliest_rs.repay_ownb_date &lt;= CURDATE() AND earliest_rs.term_status in( 'O','N') AND earliest_rs.settle_flag = 'CLOSE' THEN '已还'
                WHEN earliest_rs.repay_ownb_date IS NULL AND earliest_rs.term_status IS NULL AND earliest_rs.settle_flag IS NULL THEN '已还'
                WHEN earliest_rs.repay_ownb_date &lt;= CURDATE() AND earliest_rs.settle_flag = 'RUNNING'  THEN '逾期'
                WHEN earliest_rs.repay_ownb_date &lt;= CURDATE() AND earliest_rs.settle_flag = 'REPAYING' THEN '还款批扣中'
                ELSE '未知状态'
            END AS termStatus,
            '' saleScheduleStatus,
            COALESCE(
                (
                    SELECT MIN(cs.repay_ownb_date) FROM repay_schedule cs JOIN repay_schedule ns  ON cs.disburse_id = ns.disburse_id
                    WHERE cs.disburse_id = dd.id AND (cs.settle_flag = 'RUNNING')
                ),
                earliest_rs.repay_ownb_date
            ) AS repaymentDate,
            ifnull( dd.credit_amount, 0 ) repayTotalPrincipalAmt,
            ifnull( dd.gross_interest, 0 ) repayTotalInterestAmt,
            ifnull( dd.sale_repay_amount + dd.credit_amount + gross_interest, 0 ) repayTotalAmt,
            ifnull( rs5.actualTotalAmt , 0 ) + ifnull( ss5.actualTotalAmt , 0 ) totalRemainingAmountDue,
            ifnull( rs3.actualOtherAmt, 0 ) actualTotalAmt,
            ifnull( rs3.actualTotalAmt, 0 ) actualOtherAmt,
            ifnull( rs3.repayOtherAmt, 0 ) repayOtherAmt,
            dd.create_time,
            ifnull(std.store_name,'') storeName,
            ifnull(sfd.user_name,'') saleUserName,
            ifnull(gd.group_name,'') groupName
        FROM
        disburse_data dd
        LEFT JOIN (SELECT disburse_id, MIN(id) min_id FROM repay_schedule WHERE (repay_ownb_date &lt; CURDATE() AND settle_flag != 'CLOSE') GROUP BY disburse_id) earliest_rs_sub ON dd.id = earliest_rs_sub.disburse_id
        LEFT JOIN repay_schedule earliest_rs ON earliest_rs.id = earliest_rs_sub.min_id

        LEFT JOIN (SELECT disburse_id, MIN(id) min_id FROM sale_schedule WHERE (repay_ownb_date &lt; CURDATE() AND settle_flag != 'CLOSE') GROUP BY disburse_id) earliest_ss_sub ON dd.id = earliest_ss_sub.disburse_id
        LEFT JOIN sale_schedule earliest_ss ON earliest_ss.id = earliest_ss_sub.min_id

        LEFT JOIN user_data ud ON dd.user_id = ud.id
        LEFT JOIN user_detail u ON dd.user_id = u.user_id
        LEFT JOIN (SELECT disburse_id,SUM(CASE WHEN (date_pay IS NOT NULL AND date_pay &lt;= CURDATE()) OR (date_pay IS NULL AND repay_ownb_date &lt;= CURDATE()) THEN 1 ELSE 0 END) repaid_count
                    FROM repay_schedule GROUP BY disburse_id) rs_count ON dd.id = rs_count.disburse_id
        LEFT JOIN (SELECT disburse_id,SUM(CASE WHEN (date_pay IS NOT NULL AND date_pay &lt;= CURDATE()) OR (date_pay IS NULL AND repay_ownb_date &lt;= CURDATE()) THEN 1 ELSE 0 END) repaid_count
                    FROM sale_schedule GROUP BY disburse_id) ss_count ON dd.id = ss_count.disburse_id
        LEFT JOIN (SELECT sum(IF( settle_flag = 'CLOSE', term_guarantor_fee, 0 )) actualOtherAmt,sum(IF( settle_flag = 'CLOSE', total_amt, 0 )) actualTotalAmt,sum( term_guarantor_fee ) repayOtherAmt,
                    disburse_id FROM repay_schedule GROUP BY disburse_id) rs3 ON dd.id = rs3.disburse_id
        LEFT JOIN (SELECT sum(IF( settle_flag = 'CLOSE', total_amt, 0 )) actualTotalAmt,disburse_id FROM sale_schedule GROUP BY disburse_id) ss1 ON dd.id = ss1.disburse_id
        LEFT JOIN (SELECT sum(IF( settle_flag = 'RUNNING', total_amt, 0 )) actualTotalAmt,disburse_id FROM repay_schedule
                        where settle_flag = 'RUNNING' AND DATEDIFF(COALESCE(date_pay, CURDATE()), repay_ownb_date )  > 0 GROUP BY disburse_id) rs5 ON dd.id = rs5.disburse_id
        LEFT JOIN (SELECT sum(IF( settle_flag = 'RUNNING', total_amt, 0 )) actualTotalAmt,disburse_id FROM sale_schedule
                        where settle_flag = 'RUNNING' AND DATEDIFF(COALESCE(date_pay, CURDATE()), repay_ownb_date )  > 0 GROUP BY disburse_id) ss5 ON dd.id = ss5.disburse_id

        LEFT JOIN user_staff us ON us.user_id = ud.id
        LEFT JOIN staff_data sfd ON sfd.id = us.staff_id
        LEFT JOIN staff_group_relation sgr ON sgr.staff_id = sfd.id
        LEFT JOIN group_data gd ON gd.id = sgr.group_id
        LEFT JOIN group_store_relation gsr ON gsr.group_id = sgr.group_id
        LEFT JOIN store_data std ON std.id = gsr.store_id
        <where>
            (earliest_rs.settle_flag != 'CLOSE' or earliest_ss.settle_flag != 'CLOSE')
            <if test="billId != null">
                and dd.id = #{billId}
            </if>
            <if test="userName != null and userName != ''">
                and u.web_name like concat('%',#{userName},'%')
            </if>
            <if test="mobile != null and mobile != ''">
                and ud.mobile = #{mobile}
            </if>
            <if test="status != null and status != ''">
                and dd.credit_status = #{status}
            </if>
            <if test="createStartTime != null and createStartTime != '' and createEndTime != null and createEndTime != ''">
                and dd.create_time >= #{createStartTime} and dd.create_time &lt; #{createEndTime}
            </if>
            <if test="repayStartTime != null and repayStartTime != '' and repayEndTime != null and repayEndTime != ''">
                and  COALESCE(
                    (
                        SELECT MIN(cs.repay_ownb_date) FROM repay_schedule cs JOIN repay_schedule ns  ON cs.disburse_id = ns.disburse_id
                        WHERE cs.disburse_id = dd.id AND (cs.settle_flag = 'RUNNING')
                        ),
                        earliest_rs.repay_ownb_date
                    ) >= #{repayStartTime}  and  COALESCE(
                        (
                            SELECT MIN(cs.repay_ownb_date) FROM repay_schedule cs JOIN repay_schedule ns  ON cs.disburse_id = ns.disburse_id
                            WHERE cs.disburse_id = dd.id AND (cs.settle_flag = 'RUNNING')
                        ),
                        earliest_rs.repay_ownb_date
                    ) &lt; #{repayEndTime}
            </if>
            <if test="storeName != null and storeName != ''">
                and std.store_name = #{storeName}
            </if>
            <if test="saleUserName != null and saleUserName != ''">
                and sfd.user_name = #{saleUserName}
            </if>
            <if test="groupName != null and groupName != ''">
                and gd.group_name = #{groupName}
            </if>
            <if test="groupName != null and groupName != ''">
                and gd.group_name = #{groupName}
            </if>
            <if test="sysUserId != null and sysUserId != '' and sysRoleName != null and sysRoleName == '门店主管'">
                and std.sys_user_id = #{sysUserId}
            </if>
            <if test="sysUserId != null and sysUserId != '' and sysRoleName != null and sysRoleName == '门店组长'">
                and gd.sys_user_id = #{sysUserId}
            </if>
            <if test="sysUserId != null and sysUserId != '' and sysRoleName != null and sysRoleName == '门店销售'">
                and sfd.sys_user_id = #{sysUserId}
            </if>
            <if test="sysRoleName != null and sysRoleName == '线下催收'">
                and ud.channel_id not in('26')
            </if>
            <if test="channelId != null and channelId != ''">
                and ud.channel_id IN
                <foreach item="item" index="index" collection="channelId.split(',')"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            order by DATEDIFF(CURRENT_DATE, earliest_rs.repay_inte_date),DATEDIFF(CURRENT_DATE, earliest_ss.repay_inte_date)
        </where>
    </select>

    <select id="saleScheduleList" resultType="com.rongchen.byh.webadmin.upms.model.SaleSchedule">
        select * from sale_schedule where disburse_id = #{id} and settle_flag = 'RUNNING'
    </select>

    <select id="saleScheduleSettleFlag" resultType="com.rongchen.byh.webadmin.upms.model.SaleSchedule">
        select * from sale_schedule where disburse_id = #{id}
    </select>

    <update id="updateCreditStatus">
        update disburse_data set credit_status = '600',repayment_time = now() where id = #{disburseId}
    </update>

    <!-- 授信数据与申请信息联合查询 - 优化版本 -->
    <select id="selectCreditDataWithApplyInfo" resultType="com.rongchen.byh.webadmin.upms.dto.CreditDataWithApplyInfo">
        SELECT
            -- disburse_data表字段
            dd.id,
            dd.user_id AS userId,
            dd.product_id AS productId,
            dd.credit_no AS creditNo,
            dd.loan_no AS loanNo,
            dd.sale_no AS saleNo,
            dd.credit_amount AS creditAmount,
            dd.gross_interest AS grossInterest,
            dd.credit_status AS creditStatus,
            dd.credit_time AS creditTime,
            dd.loan_time AS loanTime,
            dd.repayment_time AS repaymentTime,
            dd.periods,
            dd.fund_code AS fundCode,
            dd.purpose_loan AS purposeLoan,
            dd.year_rete AS yearRete,
            dd.repayment_method AS repaymentMethod,
            dd.sale_repay_amount AS saleRepayAmount,
            dd.bank_name AS bankName,
            dd.create_time AS createTime,
            dd.bank_phone AS bankPhone,
            dd.bank_account AS bankAccount,
            dd.consult_fee AS consultFee,
            dd.capital_record_id AS capitalRecordId,
            dd.capital_id AS capitalId,
            dd.contract_id AS contractId,
            dd.fund_order_id AS fundOrderId,
            dd.sale_model AS saleModel,
            dd.sale_channel AS saleChannel,
            -- user_loan_apply表字段
            ul.id AS applyId,
            ul.credit_id AS creditId,
            ul.api_credit_no AS apiCreditNo,
            ul.apply_type AS applyType,
            ul.audits_status AS auditsStatus,
            ul.online_type AS onlineType,
            ul.user_apply_info AS userApplyInfo,
            ul.create_time AS applyCreateTime,
            ul.update_time AS applyUpdateTime
        FROM
            disburse_data dd
        LEFT JOIN user_loan_apply ul ON dd.user_id = ul.user_id
            AND ul.apply_type = 0
            AND ul.online_type IN (0,1,2,3,5,6,7)
        WHERE
            dd.credit_status = 500
            AND DATE(ul.create_time) = #{startDate}
        ORDER BY
            dd.create_time ASC

        <!--
        性能优化说明：
        1. 使用LEFT JOIN替代子查询，减少查询复杂度
        2. 在WHERE条件中直接过滤，避免先查询再过滤
        3. 建议在以下字段上创建索引以提升性能：
           - user_loan_apply(user_id, apply_type, online_type, create_time)
           - disburse_data(user_id, credit_status, create_time)
        -->
    </select>
</mapper>



