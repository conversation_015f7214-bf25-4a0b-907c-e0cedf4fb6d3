package com.rongchen.byh.webadmin.upms.vo.export;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class OverDayUserCountVo {
    @Schema(description = "还款日期", example = "2023-04-15")
    private String loanDate;

    @Schema(description = "渠道号", example = "xhy")
    private String channelName;

    @Schema(description = "回收率", example = "2.00%")
    private String overdue;

    @Schema(description = "逾期1天用户数", example = "100")
    private Integer overDay1;

    @Schema(description = "逾期2天用户数", example = "80")
    private Integer overDay2;

    @Schema(description = "逾期3天用户数", example = "60")
    private Integer overDay3;

    @Schema(description = "逾期4天用户数", example = "40")
    private Integer overDay4;

    @Schema(description = "逾期5天用户数", example = "30")
    private Integer overDay5;

    @Schema(description = "逾期6天用户数", example = "20")
    private Integer overDay6;

    @Schema(description = "逾期7天用户数", example = "15")
    private Integer overDay7;

    @Schema(description = "逾期8天用户数", example = "10")
    private Integer overDay8;

    @Schema(description = "逾期9天用户数", example = "8")
    private Integer overDay9;

    @Schema(description = "逾期10天用户数", example = "6")
    private Integer overDay10;

    @Schema(description = "逾期11天用户数", example = "5")
    private Integer overDay11;

    @Schema(description = "逾期12天用户数", example = "4")
    private Integer overDay12;

    @Schema(description = "逾期13天用户数", example = "3")
    private Integer overDay13;

    @Schema(description = "逾期14天用户数", example = "2")
    private Integer overDay14;

    @Schema(description = "逾期15天用户数", example = "1")
    private Integer overDay15;

    @Schema(description = "逾期16天用户数", example = "0")
    private Integer overDay16;
}
