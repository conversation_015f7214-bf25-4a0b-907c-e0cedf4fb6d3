package com.rongchen.byh.webadmin.upms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.rongchen.byh.common.api.zifang.vo.*;
import com.rongchen.byh.common.core.object.MyPageData;
import com.rongchen.byh.common.core.object.MyPageParam;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.webadmin.upms.dto.bill.*;
import com.rongchen.byh.webadmin.upms.dto.order.DisburseOrderDetailDto;
import com.rongchen.byh.webadmin.upms.dto.order.DisburseOrderListDto;
import com.rongchen.byh.webadmin.upms.model.DisburseData;
import com.rongchen.byh.webadmin.upms.model.LateCollectionsLog;
import com.rongchen.byh.webadmin.upms.vo.bill.*;
import com.rongchen.byh.webadmin.upms.vo.order.DisburseOrderDetailVo;
import com.rongchen.byh.webadmin.upms.vo.order.DisburseOrderListVo;
import com.rongchen.byh.webadmin.upms.vo.order.LateCollectionsLogVo;
import com.rongchen.byh.webadmin.upms.vo.order.OnLoanVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【disburse_data(支用记录)】的数据库操作Service
* @createDate 2025-01-23 10:21:02
*/
public interface DisburseDataService extends IService<DisburseData> {
    ResponseResult<MyPageData<DisburseOrderListVo>> list(DisburseOrderListDto dto, MyPageParam pageParam);

    ResponseResult<DisburseOrderDetailVo> detail(DisburseOrderDetailDto dto);

    ResponseResult<MyPageData<BillListVo>> billList(BillListDto dto, MyPageParam pageParam);

    ResponseResult<BillDetailVo> billDetail(BillDetailDto dto);

    ResponseResult<List<BillRepayRecordVo>> billRepayRecordList(BillRepayRecordDto dto);

    ResponseResult<Void> repay(BillRepayDto dto);

    ResponseResult<MyPageData<OverdueListVo>> overdueList(OverdueListDto dto, MyPageParam pageParam);

    ResponseResult<BillDetailVo> overdueDetailList(BillDetailDto dto);

    ResponseResult<List<BillRepayRecordVo>> overdueRepayRecordList(BillRepayRecordDto dto);

    ResponseResult<Void> onlineRepay(BillRepayDto dto);

    ResponseResult<MyPageData<LateCollectionsLogVo>> LateCollectionsLogList(BillDetailDto dto, MyPageParam pageParam);

    ResponseResult<Void> insertLateCollectionsLog(LateCollectionsLog dto);

    Void download(DisburseOrderDetailDto dto, HttpServletResponse response);

    ResponseResult<BillPreRepayApplyVo> settlement(SettleRepayApplyDto dto);

    ResponseResult<Void> settle(SettleRepayApplyDto dto);

    ResponseResult<RepaymentResultVo> repaymentResult(SettleRepayApplyDto dto);

    ResponseResult<SaleRepayResultVo> saleResult(SettleRepayApplyDto dto);

    ResponseResult<ContractSignedQueryVo> downloadMayi(DisburseOrderDetailDto dto, HttpServletResponse response);

    ResponseResult<SaleSignedQueryVo> downloadMayiQY(DisburseOrderDetailDto dto, HttpServletResponse response);

    ResponseResult<OnLoanVo> onLoan();
}
