<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.webadmin.upms.dao.UserLoanApplyMapper">
  <resultMap id="BaseResultMap" type="com.rongchen.byh.webadmin.upms.model.UserLoanApplyEntity">
    <!--@mbg.generated-->
    <!--@Table `qiyecao12`.`user_loan_apply`-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="credit_id" jdbcType="VARCHAR" property="creditId" />
    <result column="api_credit_no" jdbcType="VARCHAR" property="apiCreditNo" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="apply_type" jdbcType="INTEGER" property="applyType" />
    <result column="audits_status" jdbcType="INTEGER" property="auditsStatus" />
    <result column="online_type" jdbcType="INTEGER" property="onlineType" />
    <result column="user_apply_info" jdbcType="VARCHAR" property="userApplyInfo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `id`, `credit_id`, `api_credit_no`, `user_id`, `apply_type`, `audits_status`, `online_type`, 
    `user_apply_info`, `create_time`, `update_time`
  </sql>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.rongchen.byh.webadmin.upms.model.UserLoanApplyEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `qiyecao12`.`user_loan_apply`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="creditId != null">
        `credit_id`,
      </if>
      <if test="apiCreditNo != null">
        `api_credit_no`,
      </if>
      <if test="userId != null">
        `user_id`,
      </if>
      <if test="applyType != null">
        `apply_type`,
      </if>
      <if test="auditsStatus != null">
        `audits_status`,
      </if>
      <if test="onlineType != null">
        `online_type`,
      </if>
      <if test="userApplyInfo != null">
        `user_apply_info`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="creditId != null">
        #{creditId,jdbcType=VARCHAR},
      </if>
      <if test="apiCreditNo != null">
        #{apiCreditNo,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="applyType != null">
        #{applyType,jdbcType=INTEGER},
      </if>
      <if test="auditsStatus != null">
        #{auditsStatus,jdbcType=INTEGER},
      </if>
      <if test="onlineType != null">
        #{onlineType,jdbcType=INTEGER},
      </if>
      <if test="userApplyInfo != null">
        #{userApplyInfo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.rongchen.byh.webadmin.upms.model.UserLoanApplyEntity">
    <!--@mbg.generated-->
    update `qiyecao12`.`user_loan_apply`
    <set>
      <if test="creditId != null">
        `credit_id` = #{creditId,jdbcType=VARCHAR},
      </if>
      <if test="apiCreditNo != null">
        `api_credit_no` = #{apiCreditNo,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        `user_id` = #{userId,jdbcType=BIGINT},
      </if>
      <if test="applyType != null">
        `apply_type` = #{applyType,jdbcType=INTEGER},
      </if>
      <if test="auditsStatus != null">
        `audits_status` = #{auditsStatus,jdbcType=INTEGER},
      </if>
      <if test="onlineType != null">
        `online_type` = #{onlineType,jdbcType=INTEGER},
      </if>
      <if test="userApplyInfo != null">
        `user_apply_info` = #{userApplyInfo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.rongchen.byh.webadmin.upms.model.UserLoanApplyEntity">
    <!--@mbg.generated-->
    update `qiyecao12`.`user_loan_apply`
    set `credit_id` = #{creditId,jdbcType=VARCHAR},
      `api_credit_no` = #{apiCreditNo,jdbcType=VARCHAR},
      `user_id` = #{userId,jdbcType=BIGINT},
      `apply_type` = #{applyType,jdbcType=INTEGER},
      `audits_status` = #{auditsStatus,jdbcType=INTEGER},
      `online_type` = #{onlineType,jdbcType=INTEGER},
      `user_apply_info` = #{userApplyInfo,jdbcType=VARCHAR},
      `create_time` = #{createTime,jdbcType=TIMESTAMP},
      `update_time` = #{updateTime,jdbcType=TIMESTAMP}
    where `id` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update `qiyecao12`.`user_loan_apply`
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`credit_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.creditId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`api_credit_no` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.apiCreditNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`user_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.userId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="`apply_type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.applyType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="`audits_status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.auditsStatus,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="`online_type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.onlineType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="`user_apply_info` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.userApplyInfo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`create_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="`update_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
    </trim>
    where `id` in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update `qiyecao12`.`user_loan_apply`
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`credit_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creditId != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.creditId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`api_credit_no` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.apiCreditNo != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.apiCreditNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`user_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.userId != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.userId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="`apply_type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.applyType != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.applyType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="`audits_status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.auditsStatus != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.auditsStatus,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="`online_type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.onlineType != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.onlineType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="`user_apply_info` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.userApplyInfo != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.userApplyInfo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`create_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="`update_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
    </trim>
    where `id` in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `qiyecao12`.`user_loan_apply`
    (`credit_id`, `api_credit_no`, `user_id`, `apply_type`, `audits_status`, `online_type`, 
      `user_apply_info`, `create_time`, `update_time`)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.creditId,jdbcType=VARCHAR}, #{item.apiCreditNo,jdbcType=VARCHAR}, #{item.userId,jdbcType=BIGINT}, 
        #{item.applyType,jdbcType=INTEGER}, #{item.auditsStatus,jdbcType=INTEGER}, #{item.onlineType,jdbcType=INTEGER}, 
        #{item.userApplyInfo,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertOrUpdate" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `qiyecao12`.`user_loan_apply`
    (`credit_id`, `api_credit_no`, `user_id`, `apply_type`, `audits_status`, `online_type`, 
      `user_apply_info`, `create_time`, `update_time`)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.creditId,jdbcType=VARCHAR}, #{item.apiCreditNo,jdbcType=VARCHAR}, #{item.userId,jdbcType=BIGINT}, 
        #{item.applyType,jdbcType=INTEGER}, #{item.auditsStatus,jdbcType=INTEGER}, #{item.onlineType,jdbcType=INTEGER}, 
        #{item.userApplyInfo,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
    on duplicate key update 
    credit_id=values(credit_id),
    api_credit_no=values(api_credit_no),
    user_id=values(user_id),
    apply_type=values(apply_type),
    audits_status=values(audits_status),
    online_type=values(online_type),
    user_apply_info=values(user_apply_info),
    create_time=values(create_time),
    update_time=values(update_time)
  </insert>
</mapper>