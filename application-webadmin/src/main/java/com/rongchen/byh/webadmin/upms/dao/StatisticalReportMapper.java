package com.rongchen.byh.webadmin.upms.dao;

import com.rongchen.byh.webadmin.upms.dto.export.StatisticalReportDto;
import com.rongchen.byh.webadmin.upms.vo.export.*;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

public interface StatisticalReportMapper {

    List<StatisticalReportListVo> statisticalReportCreditList(StatisticalReportDto dto);

    List<StatisticalReportListVo> statisticalReportRepaymentList(StatisticalReportDto dto);

    List<OverdueProcessVo> getOverdueProcessList(@Param("dto")StatisticalReportDto dto,@Param("currentDate") LocalDate currentDate);

    List<Map<String,Object>> selectRepayScheduleCountUserAndAmount(@Param("currentDate") LocalDate currentDate,@Param("channelId")String channelId);

    /**
     * 查询不同月份的放款人数
     * @return
     */
    List<LoanDataVo> getDisburseDataList(@Param("dto")StatisticalReportDto dto);

    /**
     *  查询一个时间区间内逾期的人数
     * @return
     */
    Long calculateOverdueUserCount(Map<String,Object> map);

    /**
     * 获取指定放款月份和 MOB 月份的逾期用户 ID 列表
     * @param params 包含放款月份（loanYearMonth）和 MOB 月份（mobYearMonth）的参数 map
     * @return 逾期用户 ID 列表
     */
    List<Long> getOverdueUserIds(Map<String, Object> params);

    /**
     * 查询不同月份的放款金额
     * @return
     */
    List<LoanDataVo> getDisburseDataAmountList(@Param("dto")StatisticalReportDto dto);

    /**
     *  计算MOB月份的逾期未还本金总和
     * @return
     */
    Double sumOverdueTermRetPrin(Map<String,Object> map);


    List<DailyWithdrawalVo> dailyWithdrawalList(StatisticalReportDto dto);

    //注册
    List<DailyCreditVo> selectCountZC();
    //进件
    List<DailyCreditVo> selectCountJJ();
    //授信通过
    List<DailyCreditVo> selectCountSXTG();
    //授信拒绝
    List<DailyCreditVo> selectCountSXJJ();
    //放款
    List<DailyCreditVo> selectCountPay();

    List<OverdueUserDetailsVo> overdueUserCountDaysList(StatisticalReportDto dto);

    List<OverdueAmountDetailsVo> overdueAmountDaysList(StatisticalReportDto dto);

    List<OverDayUserCountVo> recoveryDaysList(StatisticalReportDto dto);

    List<OverDayUserAmountVo> recoveryDaysAmountList(StatisticalReportDto dto);

    List<OutstandingLoanBalanceVo> loanBalanceList(StatisticalReportDto dto);

}
