package com.rongchen.byh.webadmin.upms.dto.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 支用订单列表dto
 * @date 2025/1/23 11:04:01
 */
@Data
public class DisburseOrderListDto {
    @Schema(description = "还款单号")
    private Long disburseOrderId;

    @Schema(description = "客户名称")
    private String userName;

    @Schema(description = "客户手机号")
    private String mobile;

    @Schema(description = "产品id")
    private Long productId;

    @Schema(description = "状态")
    private Integer creditStatus;

    @Schema(description = "创建开始时间")
    private String createStartTime;

    @Schema(description = "创建结束时间")
    private String createEndTime;

    @Schema(description = "系统用户id")
    private Long sysUserId;

    @Schema(description = "系统用户名称")
    private String sysRoleName;

    @Schema(description = "门店名称")
    private String  storeName;

    @Schema(description = "销售员名称")
    private String  saleUserName;

    @Schema(description = "小组名称")
    private String  groupName;

    @Schema(description = "渠道ID")
    private String  channelId;

}
