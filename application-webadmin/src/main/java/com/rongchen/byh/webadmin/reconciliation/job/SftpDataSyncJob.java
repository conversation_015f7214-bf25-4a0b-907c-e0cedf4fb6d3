package com.rongchen.byh.webadmin.reconciliation.job;

import com.rongchen.byh.webadmin.reconciliation.service.SftpDataSyncService;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.time.LocalDate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 定时同步SFTP数据的Job。
 * <p>
 * 负责调度SFTP数据同步任务，具体业务逻辑由 {@link SftpDataSyncService} 处理。
 */
@Component
public class SftpDataSyncJob {

    private static final Logger logger = LoggerFactory.getLogger(SftpDataSyncJob.class);

    private final SftpDataSyncService sftpDataSyncService;

    @Autowired
    public SftpDataSyncJob(SftpDataSyncService sftpDataSyncService) {
        this.sftpDataSyncService = sftpDataSyncService;
    }

    /**
     * 执行SFTP数据同步任务。
     * 由XXL-Job等调度器触发。
     */
    @XxlJob("sftpDataSyncJobHandler")
    public void syncSftpData() {
        logger.info("开始执行SFTP数据同步任务...");

        try {
            // 同步昨天的数据（T+1对账中的T日）
            LocalDate processingDate = LocalDate.now().minusDays(1);

            SftpDataSyncService.SyncResult result = sftpDataSyncService.syncDataForDate(processingDate);

            if (result.hasFailures()) {
                logger.warn("SFTP数据同步任务完成，但存在失败项目。成功: {}, 失败: {}, 跳过: {}",
                        result.getSuccessCount(), result.getFailureCount(), result.getSkippedCount());

                // 记录失败的具体信息
                for (SftpDataSyncService.SyncTaskResult failedTask : result.getFailedTasks()) {
                    logger.error("失败任务详情：渠道[{}], 类型[{}], 错误: {}",
                            failedTask.getConfig().getChannelCode(),
                            failedTask.getConfig().getTransactionType(),
                            failedTask.getMessage());
                }
            } else {
                logger.info("SFTP数据同步任务执行完毕。成功: {}, 跳过: {}",
                        result.getSuccessCount(), result.getSkippedCount());
            }

        } catch (Exception e) {
            logger.error("SFTP数据同步任务执行异常: {}", e.getMessage(), e);
            // TODO: 可以考虑发送告警通知
            throw e; // 重新抛出异常，让XXL-Job知道任务失败
        }
    }
}