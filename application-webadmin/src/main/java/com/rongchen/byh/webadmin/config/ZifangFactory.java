package com.rongchen.byh.webadmin.config;


import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.Map;

@Configuration
public class ZifangFactory {

    @Resource
    ApplicationContext applicationContext;

    /**
     * 通过bean名称获取接口bean
     * @param beanName
     * @param clazz
     * @return
     * @param <T>
     */
    public <T> T getApi(String beanName, Class<T> clazz) {
        Map<String, T> beansOfType = applicationContext.getBeansOfType(clazz);

        T t = beansOfType.get(beanName);
        if (t != null) {
            return t;
        }
        return null;
    }


}
