package com.rongchen.byh.webadmin.reconciliation.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rongchen.byh.webadmin.reconciliation.job.ReconciliationJob;
import com.rongchen.byh.webadmin.reconciliation.util.BatchIdGenerator;
import com.rongchen.byh.webadmin.upms.model.DzBatchExecutionLogEntity;
import com.rongchen.byh.webadmin.upms.model.DzChannelReconConfigEntity;
import com.rongchen.byh.webadmin.upms.model.DzReconciliationDiffResultsEntity;
import com.rongchen.byh.webadmin.upms.model.DzReconciliationSummaryEntity;
import com.rongchen.byh.webadmin.upms.service.DzBatchExecutionLogService;
import com.rongchen.byh.webadmin.upms.service.DzChannelReconConfigService;
import com.rongchen.byh.webadmin.upms.service.DzReconciliationDiffResultsService;
import com.rongchen.byh.webadmin.upms.service.DzReconciliationSummaryService;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 对账重试服务。
 * <p>
 * 提供多种重试策略，支持按日期、渠道、交易类型或批次ID进行重试。
 * 重试前会自动清理相关的历史数据，确保重试的一致性和完整性。
 */
@Service
public class ReconciliationRetryService {

    private static final Logger logger = LoggerFactory.getLogger(ReconciliationRetryService.class);
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private final ReconciliationJob reconciliationJob;
    private final DzBatchExecutionLogService dzBatchLogService;
    private final DzReconciliationSummaryService dzReconciliationSummaryService;
    private final DzReconciliationDiffResultsService dzDiffResultsService;
    private final DzChannelReconConfigService dzChannelReconConfigService;
    private final BatchIdGenerator batchIdGenerator;
    private final PlatformTransactionManager transactionManager;

    @Autowired
    public ReconciliationRetryService(ReconciliationJob reconciliationJob,
            DzBatchExecutionLogService dzBatchLogService,
            DzReconciliationSummaryService dzReconciliationSummaryService,
            DzReconciliationDiffResultsService dzDiffResultsService,
            DzChannelReconConfigService dzChannelReconConfigService,
            BatchIdGenerator batchIdGenerator,
            PlatformTransactionManager transactionManager) {
        this.reconciliationJob = reconciliationJob;
        this.dzBatchLogService = dzBatchLogService;
        this.dzReconciliationSummaryService = dzReconciliationSummaryService;
        this.dzDiffResultsService = dzDiffResultsService;
        this.dzChannelReconConfigService = dzChannelReconConfigService;
        this.batchIdGenerator = batchIdGenerator;
        this.transactionManager = transactionManager;
    }

    /**
     * 按指定日期、渠道编码和交易类型重试对账。
     * <p>
     * 这是最精确的重试方式，适用于特定配置的重试需求。
     * 
     * @param processingDate  对账处理日期
     * @param channelCode     渠道编码
     * @param transactionType 交易类型 (LOAN, REPAYMENT, CREDIT)
     * @param retryReason     重试原因（用于日志记录）
     * @return 重试结果
     */
    public RetryResult retryByDateChannelType(LocalDate processingDate, String channelCode,
            String transactionType, String retryReason) {
        logger.info("开始按日期+渠道+交易类型重试对账: 日期={}, 渠道={}, 类型={}, 原因={}",
                processingDate.format(DATE_FORMATTER), channelCode, transactionType, retryReason);

        try {
            // 1. 参数验证
            if (processingDate == null || !StringUtils.hasText(channelCode) || !StringUtils.hasText(transactionType)) {
                return new RetryResult(false, "重试参数不能为空: processingDate, channelCode, transactionType");
            }

            // 2. 检查渠道配置是否存在且启用
            DzChannelReconConfigEntity config = getChannelReconConfig(channelCode, transactionType);
            if (config == null) {
                return new RetryResult(false,
                        String.format("未找到渠道[%s]类型[%s]的对账配置或配置未启用", channelCode, transactionType));
            }

            // 3. 生成预期的批次ID（用于查找和清理历史数据）
            String expectedBatchId = batchIdGenerator.generateBatchId(processingDate, channelCode, transactionType);

            // 4. 检查是否有正在处理的任务
            if (hasProcessingTasks(processingDate, channelCode, transactionType)) {
                return new RetryResult(false,
                        String.format("渠道[%s]类型[%s]日期[%s]存在正在处理中的对账任务，请稍后重试",
                                channelCode, transactionType, processingDate.format(DATE_FORMATTER)));
            }

            // 5. 清理历史数据
            cleanupHistoryData(processingDate, channelCode, transactionType);
            logger.info("已清理渠道[{}]类型[{}]日期[{}]的历史对账数据", channelCode, transactionType,
                    processingDate.format(DATE_FORMATTER));

            // 6. 创建单个配置列表并执行重试
            List<DzChannelReconConfigEntity> configList = Collections.singletonList(config);
            executeRetryReconciliation(processingDate, configList, retryReason);

            logger.info("渠道[{}]类型[{}]日期[{}]对账重试完成", channelCode, transactionType,
                    processingDate.format(DATE_FORMATTER));
            return new RetryResult(true, "对账重试成功", expectedBatchId);

        } catch (Exception e) {
            logger.error("渠道[{}]类型[{}]日期[{}]对账重试失败: {}", channelCode, transactionType,
                    processingDate.format(DATE_FORMATTER), e.getMessage(), e);
            return new RetryResult(false, "对账重试失败: " + e.getMessage(), e);
        }
    }

    /**
     * 按批次ID重试对账。
     * <p>
     * 从批次ID中解析出原始的日期、渠道和交易类型信息，然后执行重试。
     * 适用于已知具体失败批次的重试场景。
     * 
     * @param batchId     要重试的批次ID
     * @param retryReason 重试原因（用于日志记录）
     * @return 重试结果
     */
    public RetryResult retryByBatchId(String batchId, String retryReason) {
        logger.info("开始按批次ID重试对账: 批次ID={}, 原因={}", batchId, retryReason);

        try {
            // 1. 参数验证
            if (!StringUtils.hasText(batchId)) {
                return new RetryResult(false, "批次ID不能为空");
            }

            // 2. 从批次ID中解析信息
            BatchInfo batchInfo = parseBatchId(batchId);
            if (batchInfo == null) {
                return new RetryResult(false, "无法解析批次ID: " + batchId);
            }

            // 3. 查询批次执行日志验证批次存在
            DzBatchExecutionLogEntity batchLog = dzBatchLogService.getOne(
                    new LambdaQueryWrapper<DzBatchExecutionLogEntity>()
                            .eq(DzBatchExecutionLogEntity::getBatchId, batchId));

            if (batchLog == null) {
                return new RetryResult(false, "未找到批次ID对应的执行日志: " + batchId);
            }

            // 4. 检查当前批次状态
            if ("PROCESSING".equals(batchLog.getStatus())) {
                return new RetryResult(false, "批次ID " + batchId + " 正在处理中，无法重试");
            }

            // 5. 使用批次ID进行精确清理，然后调用重试
            logger.info("使用批次ID[{}]进行精确清理历史数据", batchId);
            cleanupHistoryDataByBatchId(batchId);

            // 6. 使用解析出的信息调用精确重试
            return retryByDateChannelType(batchInfo.processingDate, batchInfo.channelCode,
                    batchInfo.transactionType, "批次重试: " + retryReason);

        } catch (Exception e) {
            logger.error("批次ID[{}]对账重试失败: {}", batchId, e.getMessage(), e);
            return new RetryResult(false, "批次对账重试失败: " + e.getMessage(), e);
        }
    }

    /**
     * 按指定日期重试所有启用的对账配置。
     * <p>
     * 适用于系统性故障后的批量重试，会重试该日期下所有启用的渠道和交易类型配置。
     * 
     * @param processingDate 对账处理日期
     * @param retryReason    重试原因（用于日志记录）
     * @return 重试结果
     */
    public RetryResult retryByDate(LocalDate processingDate, String retryReason) {
        logger.info("开始按日期重试所有对账配置: 日期={}, 原因={}", processingDate.format(DATE_FORMATTER), retryReason);

        try {
            // 1. 参数验证
            if (processingDate == null) {
                return new RetryResult(false, "处理日期不能为空");
            }

            // 2. 查询所有启用的对账配置
            List<DzChannelReconConfigEntity> enabledConfigs = dzChannelReconConfigService.list(
                    new LambdaQueryWrapper<DzChannelReconConfigEntity>()
                            .eq(DzChannelReconConfigEntity::getReconEnabled, true));

            if (CollectionUtils.isEmpty(enabledConfigs)) {
                return new RetryResult(false, "没有找到启用的对账配置");
            }

            logger.info("找到 {} 条启用的对账配置", enabledConfigs.size());

            // 3. 检查是否有正在处理的任务
            List<DzBatchExecutionLogEntity> processingTasks = dzBatchLogService.list(
                    new LambdaQueryWrapper<DzBatchExecutionLogEntity>()
                            .eq(DzBatchExecutionLogEntity::getProcessingDate, java.sql.Date.valueOf(processingDate))
                            .eq(DzBatchExecutionLogEntity::getStatus, "PROCESSING"));

            if (!CollectionUtils.isEmpty(processingTasks)) {
                return new RetryResult(false,
                        String.format("日期[%s]存在 %d 个正在处理中的任务，请稍后重试",
                                processingDate.format(DATE_FORMATTER), processingTasks.size()));
            }
            for (DzBatchExecutionLogEntity processingTask : processingTasks) {
                // 4. 清理该日期下所有相关数据
                cleanupHistoryDataByBatchId(processingTask.getBatchId());
                logger.info("已清理批次[{}]的历史对账数据", processingTask.getBatchId());
            }

            logger.info("已清理日期[{}]的所有历史对账数据", processingDate.format(DATE_FORMATTER));

            // 5. 执行重试
            executeRetryReconciliation(processingDate, enabledConfigs, retryReason);

            logger.info("日期[{}]所有对账配置重试完成", processingDate.format(DATE_FORMATTER));
            return new RetryResult(true,
                    String.format("成功重试 %d 个对账配置", enabledConfigs.size()));

        } catch (Exception e) {
            logger.error("日期[{}]对账重试失败: {}", processingDate.format(DATE_FORMATTER), e.getMessage(), e);
            return new RetryResult(false, "对账重试失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取渠道对账配置
     */
    private DzChannelReconConfigEntity getChannelReconConfig(String channelCode, String transactionType) {
        return dzChannelReconConfigService.getOne(
                new LambdaQueryWrapper<DzChannelReconConfigEntity>()
                        .eq(DzChannelReconConfigEntity::getChannelCode, channelCode)
                        .eq(DzChannelReconConfigEntity::getTransactionType, transactionType)
                        .eq(DzChannelReconConfigEntity::getReconEnabled, true));
    }

    /**
     * 检查是否有正在处理的任务
     */
    private boolean hasProcessingTasks(LocalDate processingDate, String channelCode, String transactionType) {
        List<DzBatchExecutionLogEntity> processingTasks = dzBatchLogService.list(
                new LambdaQueryWrapper<DzBatchExecutionLogEntity>()
                        .eq(DzBatchExecutionLogEntity::getProcessingDate, java.sql.Date.valueOf(processingDate))
                        .eq(DzBatchExecutionLogEntity::getChannelCode, channelCode)
                        .eq(DzBatchExecutionLogEntity::getTransactionType, transactionType)
                        //PROCESSING=正在处理中、COMPLETED=完成、FAILED=失败
                        .eq(DzBatchExecutionLogEntity::getStatus, "PROCESSING"));
        return !CollectionUtils.isEmpty(processingTasks);
    }

    /**
     * 基于批次ID进行精确清理历史数据
     *
     * @param batchId 要清理的批次ID
     */
    private void cleanupHistoryDataByBatchId(String batchId) {
        if (!StringUtils.hasText(batchId)) {
            logger.warn("批次ID为空，跳过清理操作");
            return;
        }

        logger.info("开始基于批次ID[{}]清理历史数据", batchId);

        executeInTransaction(() -> {
            // 1. 清理批次执行日志
            long batchLogCount = dzBatchLogService.count(new LambdaQueryWrapper<DzBatchExecutionLogEntity>()
                    .eq(DzBatchExecutionLogEntity::getBatchId, batchId));
            if (batchLogCount > 0) {
                dzBatchLogService.remove(new LambdaQueryWrapper<DzBatchExecutionLogEntity>()
                        .eq(DzBatchExecutionLogEntity::getBatchId, batchId));
                logger.info("清理批次执行日志: {} 条记录", batchLogCount);
            }

            // 2. 清理对账摘要
            long summaryCount = dzReconciliationSummaryService.count(new LambdaQueryWrapper<DzReconciliationSummaryEntity>()
                    .eq(DzReconciliationSummaryEntity::getReconBatchId, batchId));
            if (summaryCount > 0) {
                dzReconciliationSummaryService.remove(new LambdaQueryWrapper<DzReconciliationSummaryEntity>()
                        .eq(DzReconciliationSummaryEntity::getReconBatchId, batchId));
                logger.info("清理对账摘要: {} 条记录", summaryCount);
            }

            // 3. 清理差异结果 - 使用精确的批次ID匹配
            long diffCount = dzDiffResultsService.count(new LambdaQueryWrapper<DzReconciliationDiffResultsEntity>()
                    .eq(DzReconciliationDiffResultsEntity::getBatchId, batchId));
            if (diffCount > 0) {
                dzDiffResultsService.remove(new LambdaQueryWrapper<DzReconciliationDiffResultsEntity>()
                        .eq(DzReconciliationDiffResultsEntity::getBatchId, batchId));
                logger.info("清理差异结果: {} 条记录", diffCount);
            }

            logger.info("批次ID[{}]历史数据清理完成，共清理: 批次日志{}条, 摘要{}条, 差异{}条",
                    batchId, batchLogCount, summaryCount, diffCount);
        });
    }

    /**
     * 清理历史数据（支持批次ID优化）
     *
     * @param processingDate 处理日期
     * @param channelCode 渠道编码
     * @param transactionType 交易类型
     * @param batchId 可选的批次ID，如果提供则优先使用精确清理
     */
    private void cleanupHistoryData(LocalDate processingDate, String channelCode, String transactionType, String batchId) {
        // 如果提供了批次ID，优先使用精确清理
        if (StringUtils.hasText(batchId)) {
            logger.info("使用批次ID[{}]进行精确清理", batchId);
            cleanupHistoryDataByBatchId(batchId);
            return;
        }

        // 回退到原有的清理逻辑
        cleanupHistoryData(processingDate, channelCode, transactionType);
    }

    /**
     * 清理历史数据
     */
    private void cleanupHistoryData(LocalDate processingDate, String channelCode, String transactionType) {
        logger.info("使用日期渠道类型进行清理: 日期={}, 渠道={}, 类型={}",
                processingDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")), channelCode, transactionType);

        // 使用编程式事务替代注解式事务
        executeInTransaction(() -> {
            // 清理批次执行日志
            long batchLogCount = dzBatchLogService.count(new LambdaQueryWrapper<DzBatchExecutionLogEntity>()
                    .eq(DzBatchExecutionLogEntity::getProcessingDate, java.sql.Date.valueOf(processingDate))
                    .eq(DzBatchExecutionLogEntity::getChannelCode, channelCode)
                    .like(DzBatchExecutionLogEntity::getTransactionType, transactionType));
            if (batchLogCount > 0) {
                dzBatchLogService.remove(new LambdaQueryWrapper<DzBatchExecutionLogEntity>()
                        .eq(DzBatchExecutionLogEntity::getProcessingDate, java.sql.Date.valueOf(processingDate))
                        .eq(DzBatchExecutionLogEntity::getChannelCode, channelCode)
                        .like(DzBatchExecutionLogEntity::getTransactionType, transactionType));
                logger.info("清理批次执行日志: {} 条记录 ", batchLogCount);
            }

            // 清理差异结果 - 通过对账摘要表收集批次ID进行精确清理
            List<String> batchIds = collectBatchIdsFromSummary(processingDate, channelCode, transactionType);
            long diffCount = 0;
            if (!CollectionUtils.isEmpty(batchIds)) {
                logger.info("从对账摘要表收集到 {} 个批次ID，进行精确清理", batchIds.size());
                diffCount = cleanupDiffResultsByBatchIds(batchIds);
            }

            // 清理对账摘要
            long summaryCount = dzReconciliationSummaryService.count(new LambdaQueryWrapper<DzReconciliationSummaryEntity>()
                    .eq(DzReconciliationSummaryEntity::getProcessingDate, java.sql.Date.valueOf(processingDate))
                    .eq(DzReconciliationSummaryEntity::getChannelCode, channelCode)
                    .eq(DzReconciliationSummaryEntity::getTransactionType, transactionType));
            if (summaryCount > 0) {
                dzReconciliationSummaryService.remove(new LambdaQueryWrapper<DzReconciliationSummaryEntity>()
                        .eq(DzReconciliationSummaryEntity::getProcessingDate, java.sql.Date.valueOf(processingDate))
                        .eq(DzReconciliationSummaryEntity::getChannelCode, channelCode)
                        .eq(DzReconciliationSummaryEntity::getTransactionType, transactionType));
                logger.info("清理对账摘要: {} 条记录 ", summaryCount);
            }

            logger.info("历史数据清理完成，共清理: 批次日志{}条, 摘要{}条, 差异{}条",
                    batchLogCount, summaryCount, diffCount);
        });
    }

    /**
     * 从对账摘要表收集相关的批次ID
     *
     * @param processingDate 处理日期
     * @param channelCode 渠道编码
     * @param transactionType 交易类型
     * @return 收集到的批次ID列表
     */
    private List<String> collectBatchIdsFromSummary(LocalDate processingDate, String channelCode, String transactionType) {
        try {
            List<DzReconciliationSummaryEntity> summaryList = dzReconciliationSummaryService.list(
                    new LambdaQueryWrapper<DzReconciliationSummaryEntity>()
                            .eq(DzReconciliationSummaryEntity::getProcessingDate, java.sql.Date.valueOf(processingDate))
                            .eq(DzReconciliationSummaryEntity::getChannelCode, channelCode)
                            .eq(DzReconciliationSummaryEntity::getTransactionType, transactionType));

            if (CollectionUtils.isEmpty(summaryList)) {
                logger.debug("未找到匹配的对账摘要记录: 日期={}, 渠道={}, 类型={}",
                        processingDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")), channelCode, transactionType);
                return new ArrayList<>();
            }

            List<String> batchIds = summaryList.stream()
                    .map(DzReconciliationSummaryEntity::getReconBatchId)
                    .filter(StringUtils::hasText)
                    .distinct()
                    .collect(Collectors.toList());

            logger.debug("从 {} 条对账摘要记录中收集到 {} 个唯一批次ID", summaryList.size(), batchIds.size());
            return batchIds;

        } catch (Exception e) {
            logger.error("从对账摘要表收集批次ID时发生错误: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据批次ID列表清理差异结果
     *
     * @param batchIds 批次ID列表
     * @return 清理的记录数量
     */
    private long cleanupDiffResultsByBatchIds(List<String> batchIds) {
        if (CollectionUtils.isEmpty(batchIds)) {
            return 0;
        }

        try {
            // 先统计数量
            long diffCount = dzDiffResultsService.count(new LambdaQueryWrapper<DzReconciliationDiffResultsEntity>()
                    .in(DzReconciliationDiffResultsEntity::getBatchId, batchIds));

            if (diffCount > 0) {
                // 执行删除
                dzDiffResultsService.remove(new LambdaQueryWrapper<DzReconciliationDiffResultsEntity>()
                        .in(DzReconciliationDiffResultsEntity::getBatchId, batchIds));
                logger.info("清理差异结果（精确匹配）: {} 条记录，涉及批次ID: {}", diffCount, batchIds);
            } else {
                logger.debug("未找到需要清理的差异结果记录，批次ID: {}", batchIds);
            }

            return diffCount;

        } catch (Exception e) {
            logger.error("根据批次ID清理差异结果时发生错误: {}, 批次ID: {}", e.getMessage(), batchIds, e);
            return 0;
        }
    }

    /**
     * 执行重试对账 - 处理指定的配置列表
     * 此方法已被重构，不再修改全局配置状态，而是直接调用job处理指定配置。
     */
    private void executeRetryReconciliation(LocalDate processingDate, List<DzChannelReconConfigEntity> configList,
            String retryReason) {
        logger.info("开始执行重试对账任务: 日期={}, 配置数量={}, 重试原因={}",
                processingDate.format(DATE_FORMATTER), configList.size(), retryReason);

        if (CollectionUtils.isEmpty(configList)) {
            logger.warn("重试对账任务中止：配置列表为空，日期={}, 原因={}", processingDate.format(DATE_FORMATTER), retryReason);
            return;
        }

        try {
            // 直接调用ReconciliationJob中处理特定配置列表的方法
            // 父月度批次ID为null，因为这是日重试
            // 为批次ID添加后缀以区分普通日对账和日重试
            logger.info("重试原因: {}, 调用标准对账流程处理 {} 个配置 (日期: {})", retryReason, configList.size(),
                    processingDate.format(DATE_FORMATTER));
            reconciliationJob.processDailyReconciliationForConfigs(processingDate, configList, null);

            logger.info("重试对账任务针对日期 {} 的 {} 个配置执行完成", processingDate.format(DATE_FORMATTER), configList.size());

        } catch (Exception e) {
            logger.error("重试对账任务执行失败: 日期={}, 原因={}, 错误: {}",
                    processingDate.format(DATE_FORMATTER), retryReason, e.getMessage(), e);
            // 此处抛出运行时异常，让调用方(如retryByDateChannelType)的catch块统一处理并构建RetryResult
            throw new RuntimeException("重试对账核心执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 复制配置对象以备份状态
     * 此方法在新版 executeRetryReconciliation 中不再需要，但暂时保留，以防其他地方可能用到或用于回退。
     * 如果确认无用，后续可以移除。
     */
    private DzChannelReconConfigEntity copyConfig(DzChannelReconConfigEntity original) {
        DzChannelReconConfigEntity copy = new DzChannelReconConfigEntity();
        copy.setId(original.getId());
        copy.setChannelCode(original.getChannelCode());
        copy.setTransactionType(original.getTransactionType());
        copy.setReconEnabled(original.getReconEnabled());
        copy.setDataSourceType(original.getDataSourceType());
        // 复制其他必要字段...
        return copy;
    }

    /**
     * 在事务中执行操作
     * 
     * @param action 要在事务中执行的操作
     */
    private void executeInTransaction(Runnable action) {
        // 定义事务，使用 PROPAGATION_REQUIRES_NEW 传播行为
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);

        // 开始事务
        TransactionStatus status = transactionManager.getTransaction(def);

        try {
            // 执行操作
            action.run();

            // 提交事务
            transactionManager.commit(status);
        } catch (Exception e) {
            // 回滚事务
            transactionManager.rollback(status);

            // 重新抛出异常，让调用者可以处理
            throw new RuntimeException("事务执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证重试请求的合法性
     */
    private RetryResult validateRetryRequest(LocalDate processingDate, String channelCode, String transactionType) {
        // 1. 日期有效性检查
        if (processingDate == null) {
            return new RetryResult(false, "处理日期不能为空");
        }

        // 检查日期不能是未来日期
        if (processingDate.isAfter(LocalDate.now())) {
            return new RetryResult(false, "处理日期不能是未来日期: " + processingDate.format(DATE_FORMATTER));
        }

        // 检查日期不能过于久远（例如超过90天）
        if (processingDate.isBefore(LocalDate.now().minusDays(90))) {
            return new RetryResult(false, "处理日期过于久远（超过90天）: " + processingDate.format(DATE_FORMATTER));
        }

        // 2. 渠道和交易类型有效性检查
        if (StringUtils.hasText(channelCode) && StringUtils.hasText(transactionType)) {
            // 验证交易类型是否支持
            if (!"LOAN".equalsIgnoreCase(transactionType) &&
                    !"REPAYMENT".equalsIgnoreCase(transactionType) &&
                    !"CREDIT".equalsIgnoreCase(transactionType)) {
                return new RetryResult(false, "不支持的交易类型: " + transactionType);
            }
        }

        return new RetryResult(true, "验证通过");
    }

    /**
     * 检查系统资源状态
     */
    private RetryResult checkSystemStatus() {
        try {
            // 1. 检查当前系统中正在处理的任务数量
            long processingCount = dzBatchLogService.count(
                    new LambdaQueryWrapper<DzBatchExecutionLogEntity>()
                            .eq(DzBatchExecutionLogEntity::getStatus, "PROCESSING"));

            // 如果正在处理的任务过多，拒绝新的重试请求
            if (processingCount > 10) { // 可配置的阈值
                return new RetryResult(false,
                        String.format("系统当前有 %d 个任务正在处理中，请稍后重试", processingCount));
            }

            // 2. 可以添加其他系统状态检查，如数据库连接、存储空间等

            return new RetryResult(true, "系统状态良好");

        } catch (Exception e) {
            logger.error("检查系统状态时发生错误: {}", e.getMessage(), e);
            return new RetryResult(false, "系统状态检查失败: " + e.getMessage());
        }
    }

    /**
     * 检查数据完整性
     */
    private RetryResult checkDataIntegrity(LocalDate processingDate, String channelCode, String transactionType) {
        try {
            // 基础的数据完整性检查
            // 检查是否存在相关的历史批次记录（证明之前有过对账记录）
            if (StringUtils.hasText(channelCode) && StringUtils.hasText(transactionType)) {
                long historyCount = dzBatchLogService.count(
                        new LambdaQueryWrapper<DzBatchExecutionLogEntity>()
                                .eq(DzBatchExecutionLogEntity::getProcessingDate, java.sql.Date.valueOf(processingDate))
                                .eq(DzBatchExecutionLogEntity::getChannelCode, channelCode)
                                .like(DzBatchExecutionLogEntity::getTransactionType, transactionType));

                // 如果没有历史记录，提醒用户可能需要先进行数据同步
                if (historyCount == 0) {
                    logger.warn("未找到日期[{}]渠道[{}]类型[{}]的历史对账记录，请确认数据已同步",
                            processingDate.format(DATE_FORMATTER), channelCode, transactionType);
                }
            }

            return new RetryResult(true, "数据完整性检查通过");

        } catch (Exception e) {
            logger.error("检查数据完整性时发生错误: {}", e.getMessage(), e);
            return new RetryResult(false, "数据完整性检查失败: " + e.getMessage());
        }
    }

    /**
     * 从批次ID中解析日期、渠道和交易类型信息
     * 支持多种批次ID格式:
     * - 新格式: BATCH_yyyyMMdd_CH_TYPE_ShortUUID (如: BATCH_20250322_XHY_LOAN_A1B2C3D4)
     * - 旧格式: RECON_yyyyMMdd_channelCode_transactionType_RECON_shortId
     * - 月度格式: MONTHLY_yyyyMM_...
     */
    private BatchInfo parseBatchId(String batchId) {
        try {
            if (!StringUtils.hasText(batchId)) {
                return null;
            }

            String[] parts = batchId.split("_");
            if (parts.length < 3) {
                logger.warn("批次ID格式不正确，无法解析: {}", batchId);
                return null;
            }

            String prefix = parts[0];

            // 解析新格式: BATCH_yyyyMMdd_CH_TYPE_ShortUUID
            if ("BATCH".equals(prefix) && parts.length >= 5) {
                String dateStr = parts[1];
                LocalDate processingDate = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyyMMdd"));
                String channelCode = parts[2];
                String transactionType = parts[3];

                logger.debug("解析新格式批次ID: 日期={}, 渠道={}, 类型={}", dateStr, channelCode, transactionType);
                return new BatchInfo(processingDate, channelCode, transactionType);
            }

            // 解析旧格式: RECON_yyyyMMdd_channelCode_transactionType_RECON_shortId
            else if ("RECON".equals(prefix) && parts.length >= 5) {
                String dateStr = parts[1];
                LocalDate processingDate = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyyMMdd"));
                String channelCode = parts[2];
                String transactionType = parts[3];

                logger.debug("解析旧格式批次ID: 日期={}, 渠道={}, 类型={}", dateStr, channelCode, transactionType);
                return new BatchInfo(processingDate, channelCode, transactionType);
            }

            // 解析月度格式: MONTHLY_yyyyMM_...
            else if ("MONTHLY".equals(prefix) || batchId.startsWith("MONTHLY")) {
                // 月度批次ID暂不支持解析为单日信息
                logger.warn("月度批次ID不支持解析为单日信息: {}", batchId);
                return null;
            }

            else {
                logger.warn("不支持的批次ID格式: {}", batchId);
                return null;
            }

        } catch (Exception e) {
            logger.error("解析批次ID失败: {}, 错误: {}", batchId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 为指定日期和配置列表清理历史对账数据（优化版，优先使用批次ID精确清理）。
     *
     * @param processingDate 要清理数据的处理日期
     * @param configsToClean 要清理数据的渠道对账配置列表
     */
    private void cleanupDataForDateAndConfigs(LocalDate processingDate,
            List<DzChannelReconConfigEntity> configsToClean) {
        if (processingDate == null || CollectionUtils.isEmpty(configsToClean)) {
            logger.warn("清理历史数据中止：处理日期或配置列表为空。日期: {}, 配置数量: {}",
                    processingDate, configsToClean == null ? 0 : configsToClean.size());
            return;
        }

        logger.info("开始为日期 {} 清理 {} 个指定配置的历史对账数据（优先使用批次ID精确清理）...",
                processingDate.format(DATE_FORMATTER), configsToClean.size());

        for (DzChannelReconConfigEntity config : configsToClean) {
            if (config != null && StringUtils.hasText(config.getChannelCode())
                    && StringUtils.hasText(config.getTransactionType())) {
                try {
                    logger.info("清理历史数据: 日期={}, 渠道={}, 类型={}",
                            processingDate.format(DATE_FORMATTER), config.getChannelCode(),
                            config.getTransactionType());

                    // 优化：先查询相关的批次ID，然后使用精确清理
                    List<DzBatchExecutionLogEntity> relatedBatches = dzBatchLogService.list(
                            new LambdaQueryWrapper<DzBatchExecutionLogEntity>()
                                    .eq(DzBatchExecutionLogEntity::getProcessingDate, java.sql.Date.valueOf(processingDate))
                                    .eq(DzBatchExecutionLogEntity::getChannelCode, config.getChannelCode())
                                    .like(DzBatchExecutionLogEntity::getTransactionType, config.getTransactionType()));

                    if (!CollectionUtils.isEmpty(relatedBatches)) {
                        // 使用批次ID进行精确清理
                        logger.info("找到 {} 个相关批次，使用批次ID进行精确清理", relatedBatches.size());
                        for (DzBatchExecutionLogEntity batch : relatedBatches) {
                            if (StringUtils.hasText(batch.getBatchId())) {
                                cleanupHistoryDataByBatchId(batch.getBatchId());
                            }
                        }
                    } else {
                        // 回退到传统清理方式
                        logger.info("未找到相关批次记录，使用传统清理方式");
                        cleanupHistoryData(processingDate, config.getChannelCode(), config.getTransactionType());
                    }
                } catch (Exception e) {
                    logger.error("为日期 {}, 渠道 {}, 类型 {} 清理历史数据时发生错误: {}",
                            processingDate.format(DATE_FORMATTER), config.getChannelCode(), config.getTransactionType(),
                            e.getMessage(), e);
                    // 根据策略决定是否继续清理其他配置或抛出异常
                }
            } else {
                logger.warn("跳过清理一个无效的配置项: {}", config);
            }
        }
        logger.info("日期 {} 的指定配置历史对账数据清理完成。", processingDate.format(DATE_FORMATTER));
    }

    /**
     * 按指定年月、可选的渠道编码和交易类型重试月度对账。
     *
     * @param yearMonth       要重试的年月
     * @param channelCode     可选，特定渠道编码。如果为空，则处理所有符合条件的启用配置。
     * @param transactionType 可选，特定交易类型。如果为空，则处理所有符合条件的启用配置。
     * @param retryReason     重试原因（用于日志记录）
     * @return 重试结果，包含月度主批次ID（如果成功启动）
     */
    public RetryResult retryByYearMonth(YearMonth yearMonth, String channelCode, String transactionType,
            String retryReason) {
        String logContext = String.format("月度重试: 年月=%s, 渠道=%s, 类型=%s, 原因=%s",
                yearMonth,
                StringUtils.hasText(channelCode) ? channelCode : "ALL",
                StringUtils.hasText(transactionType) ? transactionType : "ALL",
                retryReason);
        logger.info("开始 {}", logContext);

        // 1. 参数验证
        if (yearMonth == null) {
            return new RetryResult(false, "重试参数错误: yearMonth 不能为空");
        }
        // 可选：添加对 yearMonth 的更详细验证，例如不能是未来月份等。

        try {
            // 2. 筛选对账配置
            LambdaQueryWrapper<DzChannelReconConfigEntity> queryWrapper = new LambdaQueryWrapper<DzChannelReconConfigEntity>()
                    .eq(DzChannelReconConfigEntity::getReconEnabled, true);
            if (StringUtils.hasText(channelCode)) {
                queryWrapper.eq(DzChannelReconConfigEntity::getChannelCode, channelCode);
            }
            if (StringUtils.hasText(transactionType)) {
                queryWrapper.eq(DzChannelReconConfigEntity::getTransactionType, transactionType);
            }
            List<DzChannelReconConfigEntity> configsToProcess = dzChannelReconConfigService.list(queryWrapper);

            if (CollectionUtils.isEmpty(configsToProcess)) {
                String message = String.format("未找到符合条件的启用对账配置: 年月=%s, 渠道=%s, 类型=%s",
                        yearMonth, channelCode, transactionType);
                logger.warn(message);
                return new RetryResult(false, message);
            }
            logger.info("{} - 找到 {} 条符合条件的配置进行处理。", logContext, configsToProcess.size());

            // 3. 生成月度主批次ID
            String effectiveChannelForBatchId = StringUtils.hasText(channelCode) ? channelCode : "ALL_CH";
            String effectiveTypeForBatchId = StringUtils.hasText(transactionType) ? transactionType : "ALL_TX";
            String monthlyMasterBatchId = batchIdGenerator.generateMonthlyBatchId(yearMonth,
                    effectiveChannelForBatchId,
                    effectiveTypeForBatchId,
                    "MANUAL_RETRY");
            logger.info("{} - 生成月度主批次ID: {}", logContext, monthlyMasterBatchId);

            // TODO: 未来可以引入月度主批次日志表 (DzMonthlyBatchExecutionLogEntity) 在此处记录开始

            // 4. 迭代处理该月的每一天
            int daysInMonth = yearMonth.lengthOfMonth();
            int successDays = 0;
            int failedDays = 0;

            for (int day = 1; day <= daysInMonth; day++) {
                LocalDate currentProcessingDate = yearMonth.atDay(day);
                String dayLogContext = String.format("月度重试日处理 (主批次 %s): 日期=%s", monthlyMasterBatchId,
                        currentProcessingDate);
                logger.info("开始 {}", dayLogContext);

                try {
                    // 4a. 清理当天的历史数据 (针对筛选出的配置)
                    cleanupDataForDateAndConfigs(currentProcessingDate, configsToProcess);

                    // 4b. 执行当天的对账，传入主月度批次ID和特定后缀
                    reconciliationJob.processDailyReconciliationForConfigs(currentProcessingDate,
                            configsToProcess,
                            monthlyMasterBatchId);
                    logger.info("{} 处理完成。", dayLogContext);
                    successDays++;
                } catch (Exception e) {
                    logger.error("{} 处理失败。错误: {}", dayLogContext, e.getMessage(), e);
                    failedDays++;
                    // 根据策略，可以选择是否因为一天失败就中止整个重试
                    // 当前实现是记录错误并继续处理其他天
                }
            }

            // TODO: 未来可以引入月度主批次日志表，在此处记录结束和整体状态 (成功天数/失败天数)

            String finalMessage = String.format("月度对账重试完成: 年月=%s, 主批次ID=%s. 总天数=%d, 成功天数=%d, 失败天数=%d",
                    yearMonth, monthlyMasterBatchId, daysInMonth, successDays, failedDays);
            logger.info(finalMessage);
            return new RetryResult(failedDays == 0, finalMessage, monthlyMasterBatchId);

        } catch (Exception e) {
            logger.error("{} 执行失败: {}", logContext, e.getMessage(), e);
            return new RetryResult(false, logContext + " 执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 对账重试结果
     */
    @Getter
    public static class RetryResult {
        // Getters
        private final boolean success;
        private final String message;
        private String batchId;
        private Throwable exception;

        public RetryResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }

        public RetryResult(boolean success, String message, String batchId) {
            this.success = success;
            this.message = message;
            this.batchId = batchId;
        }

        public RetryResult(boolean success, String message, Throwable exception) {
            this.success = success;
            this.message = message;
            this.exception = exception;
        }

    }

    /**
     * 批次信息解析结果
     */
    @Getter
    private static class BatchInfo {
        // Getters
        private final LocalDate processingDate;
        private final String channelCode;
        private final String transactionType;

        public BatchInfo(LocalDate processingDate, String channelCode, String transactionType) {
            this.processingDate = processingDate;
            this.channelCode = channelCode;
            this.transactionType = transactionType;
        }

    }
}