package com.rongchen.byh.webadmin.upms.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**

 * 项目名称：byh_java
 * 文件名称: UserLoanApplyEntity
 * 创建时间: 2025-06-05 16:36
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.webadmin.upms.model
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
/**
 * 用户贷款申请
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "`qiyecao12`.`user_loan_apply`")
public class UserLoanApplyEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 授信id
     */
    @TableField(value = "`credit_id`")
    private String creditId;

    /**
     * api授信no
     */
    @TableField(value = "`api_credit_no`")
    private String apiCreditNo;

    /**
     * 用户id
     */
    @TableField(value = "`user_id`")
    private Long userId;

    /**
     * 申请类型 0 初筛申请 1 授信申请
     */
    @TableField(value = "`apply_type`")
    private Integer applyType;

    /**
     * 审核状态 0 待审核 1 审核通过 2 审核不通过  4 转人工
     */
    @TableField(value = "`audits_status`")
    private Integer auditsStatus;

    /**
     * 线上线下类型 0 线上 1 线下 2空中 5空中仅注册 6线上仅注册 7线下仅注册
     */
    @TableField(value = "`online_type`")
    private Integer onlineType;

    /**
     * 提交申请信息
     */
    @TableField(value = "`user_apply_info`")
    private String userApplyInfo;

    /**
     * 创建时间
     */
    @TableField(value = "`create_time`",fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "`update_time`",fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}