package com.rongchen.byh.webadmin.upms.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rongchen.byh.webadmin.upms.dto.CompensationDto;
import com.rongchen.byh.webadmin.upms.model.RepaySchedule;
import com.rongchen.byh.webadmin.upms.model.RepayScheduleApply;
import com.rongchen.byh.webadmin.upms.vo.RepayScheduleCompensationVo;
import com.rongchen.byh.webadmin.upms.vo.order.OnLoanVo;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 项目名称：byh_java
 * 文件名称: RepayScheduleMapper
 * 创建时间: 2025-05-23 17:12
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.webadmin.upms.dao
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
public interface RepayScheduleMapper extends BaseMapper<RepaySchedule> {

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(RepaySchedule record);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(RepaySchedule record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(RepaySchedule record);

    int updateBatch(@Param("list") List<RepaySchedule> list);

    int updateBatchSelective(@Param("list") List<RepaySchedule> list);

    int batchInsert(@Param("list") List<RepaySchedule> list);

    int batchInsertOrUpdate(@Param("list") List<RepaySchedule> list);

    List<RepayScheduleApply> repayScheduleApplyList(String repayApplyNo);

    int updateRepaySchedule(RepaySchedule dto);

    int updateRepayScheduleSettleFlag(RepaySchedule dto);

    OnLoanVo selectOnLoan();

    List<RepayScheduleCompensationVo> selectByOrderNoAndTerm(List<CompensationDto> list);
}