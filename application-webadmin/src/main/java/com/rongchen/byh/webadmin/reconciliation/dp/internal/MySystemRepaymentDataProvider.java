package com.rongchen.byh.webadmin.reconciliation.dp.internal;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rongchen.byh.webadmin.reconciliation.context.ReconciliationContext;
import com.rongchen.byh.webadmin.reconciliation.dataProvider.DataProvider;
import com.rongchen.byh.webadmin.reconciliation.model.NormalizedRepaymentRecord;
import com.rongchen.byh.webadmin.upms.dao.RepayScheduleMapper;
import com.rongchen.byh.webadmin.upms.model.RepaySchedule;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 我方系统还款数据提供者。
 */
@Component
public class MySystemRepaymentDataProvider implements DataProvider {

    private static final Logger logger = LoggerFactory.getLogger(MySystemRepaymentDataProvider.class);
    private static final DateTimeFormatter YYYY_MM_DD_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter YYYY_MM_DD_HH_MM_SS_FORMATTER = DateTimeFormatter
            .ofPattern("yyyy-MM-dd HH:mm:ss");

    private final RepayScheduleMapper repayScheduleMapper;
    // private final ObjectMapper objectMapper; // No longer needed for POJO to Map
    // if not returning List<Map>

    @Autowired
    public MySystemRepaymentDataProvider(RepayScheduleMapper repayScheduleMapper /* , ObjectMapper objectMapper */) {
        this.repayScheduleMapper = repayScheduleMapper;
        // this.objectMapper = objectMapper;
    }

    @Override
    public List<Map<String, Object>> loadData(ReconciliationContext context) throws Exception {
        // 此方法不再用于我方数据提供者，我方数据直接通过loadNormalizedData提供标准化模型
        logger.warn("MySystemRepaymentDataProvider.loadData()被调用，但推荐使用loadNormalizedData()获取我方还款数据。");
        throw new UnsupportedOperationException("请使用 loadNormalizedData() 方法获取我方系统的标准化还款数据。");
    }

    @Override
    public List<NormalizedRepaymentRecord> loadNormalizedData(ReconciliationContext context) throws Exception {
        logger.info("开始从我方系统加载并转换还款数据 (RepaySchedule)，处理日期: {}", context.getProcessingDate());

        LocalDate processingDate = context.getProcessingDate();
        String processingDateStr = processingDate.format(YYYY_MM_DD_FORMATTER);
        LambdaQueryWrapper<RepaySchedule> queryWrapper = new LambdaQueryWrapper<RepaySchedule>()
                .eq(RepaySchedule::getDatePay, processingDateStr) // 查询实际还款日为processingDate的记录
                .orderByAsc(RepaySchedule::getCreateTime); // 示例排序

        logger.info("我方还款计划查询条件: date_pay = '{}'", processingDateStr);

        List<RepaySchedule> repaymentEntities = repayScheduleMapper.selectList(queryWrapper);

        if (CollectionUtils.isEmpty(repaymentEntities)) {
            logger.info("在我方系统未找到实际还款日为 {} 的还款数据 (RepaySchedule)。", processingDateStr);
            return new ArrayList<>();
        }
        logger.info("从我方系统加载了 {} 条实际还款日为 {} 的还款数据 (RepaySchedule)。", repaymentEntities.size(), processingDateStr);

        return repaymentEntities.stream()
                .map(entity -> convertToNormalized(entity, context))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private NormalizedRepaymentRecord convertToNormalized(RepaySchedule entity, ReconciliationContext context) {
        try {
            NormalizedRepaymentRecord norm = new NormalizedRepaymentRecord();
            // --- 直接映射字段 (假设NormalizedRepaymentRecord中属性名与此对应) ---
            // 您需要确保NormalizedRepaymentRecord中的setter与这里的getter能够对应上，或者手动set
            // BeanUtils.copyProperties(entity, norm); // 这是一个选项，但要注意类型和名称匹配

            // 手动映射关键字段，并进行必要的类型转换和逻辑处理
            norm.setPartnerLoanOrderNo(entity.getDisburseId() != null ? String.valueOf(entity.getDisburseId()) : null); // 假设disburseId对应资方借款单号
            norm.setPartnerRepaymentId(entity.getRepayApplyNo()); // 我方还款申请流水 作为 资方还款ID (标准模型中的)

            // 日期时间转换 (RepaySchedule中很多日期是String，需要解析)
            norm.setSubmissionDatetime(parseOurDateTime(entity.getPayTime())); // 还款发起时间
            norm.setRepaymentEffectiveDate(parseOurDateTime(entity.getDatePayTime())); // 实际还款日期时间

            norm.setInstallmentNumbersStr(entity.getRepayTerm());
            norm.setRepaymentMethodCode(entity.getRepayMethod()); // 0线上，1线下 -> 映射规则可以处理为更友好的值
            norm.setPaymentChannelCode(null); // 我方还款计划中通常不直接记录资方支付通道，可留空或从其他关联获取
            norm.setDebtTransferStatusCode(
                entity.getCompensationStatus() != null ? String.valueOf(entity.getCompensationStatus()) : null); // 0非代偿
            // 1代偿
            // 2回购
            norm.setRepaymentTypeCode(determineRepaymentType(entity)); // 需要根据termStatus, settleFlag等判断

            norm.setTotalRepaidAmount(entity.getTotalAmt()); // 本期应还 -> 假设对账时是对比此金额
            norm.setRepaidPrincipalAmount(entity.getPrinAmt()); // 本期应还本金
            norm.setRepaidInterestAmount(entity.getTermRetInt()); // 本期应还利息
            norm.setRepaidFeeAmount(calculateOurFees(entity)); // 本期应还服务费+融担费 -> 需要自定义逻辑
            norm.setRepaidOverdueAmount(entity.getTermRetFint()); // 本期应还罚息

            norm.setStatus(entity.getTermStatus()); // 直接使用我方计划状态作为初始状态

            // 设置NormalizedTransaction接口字段
            norm.setSourceChannel(getChannelCode()); // MYSYSTEM
            norm.setOriginalRecordId(entity.getId() != null ? String.valueOf(entity.getId()) : null);
            norm.setAdditionalData(new HashMap<>()); // 初始化
            // 可以将RepaySchedule中其他未直接映射的字段放入additionalData
            // norm.getAdditionalData().put("our_plan_id", entity.getPlanId());

            return norm;
        } catch (Exception e) {
            logger.error("将RepaySchedule实体 (ID: {}) 转换为NormalizedRepaymentRecord失败: {}", entity.getId(),
                    e.getMessage(),
                    e);
            return null;
        }
    }

    // 辅助方法：根据我方还款计划状态判断标准还款类型
    private String determineRepaymentType(RepaySchedule entity) {
        // TODO: 实现更精确的逻辑根据 termStatus, settleFlag, 以及可能的其他字段
        // 例如: CURRENT, PREPAYMENT, OVERDUE, CLEAN etc.
        // This is a placeholder
        if ("CLOSE".equals(entity.getSettleFlag())) {
            // 需要判断是正常结清还是提前结清，可能需要比较还款日期和应还日期
            return "CLEAN"; // 笼统地设为结清
        }
        if ("O".equals(entity.getTermStatus()) || "L".equals(entity.getTermStatus())
                || "B".equals(entity.getTermStatus())) {
            return "OVERDUE";
        }
        return "CURRENT"; // 默认当期
    }

    // 辅助方法：计算我方费用总和 (示例，您需要根据实际情况调整)
    private BigDecimal calculateOurFees(RepaySchedule entity) {
        BigDecimal guarantorFee = Optional.ofNullable(entity.getTermGuarantorFee()).orElse(BigDecimal.ZERO);
        BigDecimal serviceFee = Optional.ofNullable(entity.getTermServiceFee()).orElse(BigDecimal.ZERO);
        // 罚息中的融担罚费是否也算费用？根据对账口径
        BigDecimal overdueGuarantorFee = Optional.ofNullable(entity.getTermOverdueGuarantorFee())
                .orElse(BigDecimal.ZERO);
        return guarantorFee.add(serviceFee).add(overdueGuarantorFee);
    }

    // 辅助方法：解析我方系统中的日期时间字符串
    private LocalDateTime parseOurDateTime(String dateTimeStr) {
        if (!StringUtils.hasText(dateTimeStr))
            return null;
        try {
            if (dateTimeStr.length() == 19) { // "yyyy-MM-dd HH:mm:ss"
                return LocalDateTime.parse(dateTimeStr, YYYY_MM_DD_HH_MM_SS_FORMATTER);
            } else if (dateTimeStr.length() == 10) { // "yyyy-MM-dd"
                // 如果是纯日期，需要决定它代表一天的哪个时间点。
                // 例如，一天的开始：
                LocalDate date = LocalDate.parse(dateTimeStr, YYYY_MM_DD_FORMATTER);
                return date.atStartOfDay();
                // 或者，如果业务上纯日期意味着一天的结束：
                // return date.atTime(23, 59, 59);
            }
            logger.warn("无法识别的我方系统日期时间字符串格式: {}", dateTimeStr);
            return null;
        } catch (DateTimeParseException e) {
            logger.warn("解析我方日期时间字符串 '{}' 失败: {}", dateTimeStr, e.getMessage());
            return null;
        }
    }

    @Override
    public String getChannelCode() {
        return "MYSYSTEM";
    }

    @Override
    public String getTransactionType() {
        return "REPAYMENT";
    }

    @Override
    public String getDataSourceType() {
        return "OUR_SIDE";
    }
}