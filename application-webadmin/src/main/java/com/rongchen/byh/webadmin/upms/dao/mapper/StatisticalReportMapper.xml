<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.webadmin.upms.dao.StatisticalReportMapper">

    <select id="statisticalReportCreditList" resultType="com.rongchen.byh.webadmin.upms.vo.export.StatisticalReportListVo">
          SELECT DISTINCT
                IFNULL(std.store_name, '其他') AS storeName,
                IFNULL(gd.group_name, '其他') AS groupName,
                IFNULL(sfd.user_name, '其他') AS userName,
                cd.`name` AS channelName,
                CASE ul.audits_status
                    WHEN 0 THEN '待审核'
                    WHEN 1 THEN '审核通过'
                    WHEN 2 THEN '审核不通过'
                    WHEN 4 THEN '转人工'
                    ELSE '无' END AS auditsStatus,
                ud.create_time,
                CASE ud.source_mode
                    WHEN 0 THEN '线上'
                    WHEN 1 THEN '线下'
                    WHEN 2 THEN '空中放款'
                    WHEN 3 THEN '全流程API'
                    ELSE ''
                END sourceMode
            FROM (SELECT id,create_time,source_mode,channel_id  FROM user_data ) ud
                LEFT JOIN (SELECT user_id FROM user_detail) ude ON ud.id = ude.user_id
                LEFT JOIN channel_data cd ON ud.channel_id = cd.id
                LEFT JOIN (SELECT user_id, audits_status FROM user_loan_apply WHERE apply_type = 0) ul ON ud.id = ul.user_id
                LEFT JOIN user_staff us ON us.user_id = ud.id
                LEFT JOIN staff_data sfd ON sfd.id = us.staff_id
                LEFT JOIN staff_group_relation sgr ON sgr.staff_id = sfd.id
                LEFT JOIN group_data gd ON gd.id = sgr.group_id
                LEFT JOIN group_store_relation gsr ON gsr.group_id = sgr.group_id
                LEFT JOIN store_data std ON std.id = gsr.store_id
            <where>
                <if test="channelId != null and channelId != ''">
                    AND cd.id = #{channelId}
                </if>
                <if test="timeStart != null and timeStart != '' and timeEnd != null and timeEnd != ''">
                    AND ud.create_time &gt;= #{timeStart} AND ud.create_time &lt; #{timeEnd}
                </if>
                <if test="storeName != null and storeName != ''">
                    AND IFNULL(std.store_name, '其他') = #{storeName}
                </if>
                <if test="groupName != null and groupName != ''">
                    AND IFNULL(gd.group_name, '其他') = #{groupName}
                </if>
            </where>
    </select>


   <select id="statisticalReportRepaymentList" resultType="com.rongchen.byh.webadmin.upms.vo.export.StatisticalReportListVo">
      select DISTINCT
           IFNULL(std.store_name, '其他') AS storeName,
           IFNULL(gd.group_name, '其他') AS groupName,
           IFNULL(sfd.user_name, '其他') AS userName,
           cd.`name` AS channelName,
           case dd.credit_status
               when 100 then '授信中'
               when 200 then '授信失败'
               when 300 then '放款中'
               when 400 then '放款失败'
               when 500 then '还款中'
               when 600 then '已结清'
               else '无'
            end auditsStatus,
            dd.create_time,
           CASE ud.source_mode
               WHEN 0 THEN '线上'
               WHEN 1 THEN '线下'
               WHEN 2 THEN '空中放款'
               WHEN 3 THEN '全流程API'
               ELSE ''
           END sourceMode
       FROM
           disburse_data dd
       left join (SELECT id,mobile,source_mode,channel_id  FROM user_data ) ud on dd.user_id = ud.id
       left join (SELECT user_id FROM user_detail) ude on ud.id = ude.user_id
       left join (select disburse_id , total_amt from repay_schedule where repay_term = 1 ) rs on dd.id = rs.disburse_id
       left join channel_data cd on ud.channel_id = cd.id
       LEFT JOIN user_staff us on us.user_id = ud.id
       LEFT JOIN staff_data sfd on sfd.id = us.staff_id
       LEFT JOIN staff_group_relation sgr on sgr.staff_id = sfd.id
       LEFT JOIN group_data gd on gd.id = sgr.group_id
       LEFT JOIN group_store_relation gsr on gsr.group_id = sgr.group_id
       LEFT JOIN store_data std on std.id = gsr.store_id
       <where>
           <if test="channelId != null and channelId != ''">
               AND cd.`name` = #{channelId}
           </if>
           <if test="timeStart!= null and timeStart!= '' and timeEnd!= null and timeEnd!= ''">
               AND dd.create_time &gt;= #{timeStart} AND dd.create_time &lt; #{timeEnd}
           </if>
           <if test="storeName != null and storeName != ''">
               AND IFNULL(std.store_name, '其他') = #{storeName}
           </if>
           <if test="groupName != null and groupName != ''">
               AND IFNULL(gd.group_name, '其他') = #{groupName}
           </if>
      </where>
   </select>

    <!-- 逾期处理明细 -->
    <select id="getOverdueProcessList" resultType="com.rongchen.byh.webadmin.upms.vo.export.OverdueProcessVo">
        SELECT
            op.user_id ,
            op.overdue_days,
            DATE(op.create_time) createTime,
            cd.`name` channelName,
            op.total_ret_prin totalRetPrin,
            op.total_amt
        FROM
            overdue_process op
        INNER JOIN user_data ud ON ud.id = op.user_id
        INNER JOIN channel_data cd ON cd.id = ud.channel_id
        WHERE
            op.repay_term = '1'
            AND op.overdue_days > 0
        <if test="currentDate != null">
            AND op.repay_ownb_date >= DATE_FORMAT(#{currentDate} , '%Y-%m-01')
            AND op.repay_ownb_date &lt;= DATE_FORMAT(#{currentDate} , '%Y-%m-%d')
            <if test="dto.channelId != null and dto.channelId != ''">
                AND ud.channel_id = #{dto.channelId}
            </if>
            group by  op.user_id, op.overdue_days ,cd.`name`
        </if>
        <if test="dto.timeStart != null and dto.timeStart != '' and dto.timeEnd != null and dto.timeEnd != ''">
            AND op.create_time &gt;= #{dto.timeStart} AND op.create_time &lt; #{dto.timeEnd}
        </if>
        <if test="dto.channelId != null and dto.channelId != '' and currentDate == null ">
            AND ud.channel_id = #{dto.channelId}
        </if>
    </select>

    <select id="selectRepayScheduleCountUserAndAmount" resultType="java.util.Map">
        SELECT
            SUM(dd.credit_amount) totalAmount,
            COUNT(DISTINCT dd.user_id) countUser,
            cd.name channelName
        FROM
            disburse_data dd
        INNER JOIN user_data ud ON ud.id = dd.user_id
        INNER JOIN channel_data cd ON cd.id = ud.channel_id
        WHERE
            dd.id IN ( SELECT disburse_id FROM repay_schedule WHERE repay_ownb_date >= DATE_FORMAT( #{currentDate}, '%Y-%m-01' ) AND repay_ownb_date &lt;= DATE_FORMAT( #{currentDate}, '%Y-%m-%d' ) AND repay_term = 1 )
        <if test="channelId != null and channelId != ''">
            AND cd.id = #{channelId}
        </if>
        GROUP BY cd.`name`
    </select>

    <select id="calculateOverdueUserCount" resultType="java.lang.Long" parameterType="map">
        SELECT COUNT(DISTINCT rs.user_id)
        FROM repay_schedule rs
        JOIN disburse_data dd ON rs.user_id = dd.user_id and dd.id = rs.disburse_id
        WHERE
            rs.term_status IN ('O', 'L', 'B')
          AND DATE_FORMAT(dd.loan_time, '%Y-%m') = #{loanYearMonth}
          AND SUBSTR(repay_ownb_date, 1, 7) = #{mobYearMonth}
    </select>

    <select id="getOverdueUserIds" resultType="java.lang.Long">
        SELECT
            DISTINCT rs.user_id
        FROM repay_schedule rs
        JOIN disburse_data dd ON rs.user_id = dd.user_id and dd.id = rs.disburse_id
        LEFT JOIN user_data ud ON dd.user_id = ud.id
        LEFT JOIN channel_data cd ON ud.channel_id = cd.id
        WHERE
            DATE_FORMAT(dd.loan_time, '%Y-%m') = #{loanYearMonth}
          AND SUBSTR(rs.repay_ownb_date, 1, 7) = #{mobYearMonth}
          AND cd.`name` = #{channelName}
          AND DATEDIFF(COALESCE(rs.date_pay, CURDATE()), rs.repay_ownb_date) > 0
          AND rs.settle_flag = 'RUNNING'
    </select>


    <select id="getDisburseDataList" resultType="com.rongchen.byh.webadmin.upms.vo.export.LoanDataVo">
        SELECT
            DATE_FORMAT(loan_time, '%Y-%m') yearMonth,
            IFNULL(cd.`name`,'xxmd') channelName,
            count(user_id) userCount
        FROM
            disburse_data dd
        LEFT JOIN user_data ud ON dd.user_id = ud.id
        LEFT JOIN channel_data cd ON ud.channel_id = cd.id
        WHERE credit_status in(500,600)
        <if test="dto.yearMonthStart != null and dto.yearMonthStart != '' and dto.yearMonthEnd != null and dto.yearMonthEnd != ''">
            AND loan_time &gt;= #{dto.yearMonthStart} AND loan_time &lt; #{dto.yearMonthEnd}
        </if>
        <if test="dto.channelId != null and dto.channelId != ''">
            AND ud.channel_id = #{dto.channelId}
        </if>
        group by DATE_FORMAT(loan_time, '%Y-%m'),IFNULL(cd.`name`,'xxmd')
    </select>

    <select id="getDisburseDataAmountList" resultType="com.rongchen.byh.webadmin.upms.vo.export.LoanDataVo">
        SELECT
            DATE_FORMAT( loan_time, '%Y-%m' ) yearMonth,
            IFNULL(cd.`name`,'xxmd') channelName,
            SUM( credit_amount ) creditAmount
        FROM
            disburse_data dd
        LEFT JOIN user_data ud ON dd.user_id = ud.id
        LEFT JOIN channel_data cd ON ud.channel_id = cd.id
        WHERE
            credit_status IN ( 500, 600 )
        <if test="dto.yearMonthStart != null and dto.yearMonthStart != '' and dto.yearMonthEnd != null and dto.yearMonthEnd != ''">
            AND loan_time &gt;= #{dto.yearMonthStart} AND loan_time &lt; #{dto.yearMonthEnd}
        </if>
        <if test="dto.channelId != null and dto.channelId != ''">
            AND ud.channel_id = #{dto.channelId}
        </if>
        GROUP BY
            DATE_FORMAT( loan_time, '%Y-%m'),IFNULL(cd.`name`,'xxmd')
    </select>

    <select id="sumOverdueTermRetPrin" resultType="java.lang.Double">
        SELECT IFNULL(SUM( term_ret_prin ) ,0)
        FROM repay_schedule
        WHERE
            user_id IN (
                SELECT DISTINCT rs.user_id
                FROM repay_schedule rs
                 JOIN disburse_data dd ON dd.user_id = rs.user_id and rs.disburse_id = dd.id
                 JOIN user_data ud ON dd.user_id = ud.id
                 JOIN channel_data cd ON ud.channel_id = cd.id
                WHERE
                    DATE_FORMAT(dd.loan_time, '%Y-%m') = #{loanYearMonth}
                  AND SUBSTR(rs.repay_ownb_date, 1, 7) BETWEEN #{startMobMonth} AND #{endMobMonth}
                  AND DATEDIFF(COALESCE(rs.date_pay, CURDATE()), rs.repay_ownb_date) > 0
                  AND rs.settle_flag = 'RUNNING'
                  AND cd.`name` = #{channelName}
            )
          AND settle_flag = 'RUNNING'
    </select>

    <select id="dailyWithdrawalList" resultType="com.rongchen.byh.webadmin.upms.vo.export.DailyWithdrawalVo">
        SELECT
            udr.registration_date registrationDate,
            cd.name ,
            CASE ula.online_type
                WHEN 0 THEN '线上'
                WHEN 1 THEN '线下'
                WHEN 2 THEN '空中放款'
                WHEN 3 THEN '全流程API'
                ELSE ''
                END sourceMode,
            COUNT(DISTINCT udr.id) userCount,
            SUM(CASE WHEN ula.audits_status = 1 THEN 1 ELSE 0 END) auditsStatus,
            SUM(CASE WHEN ddp.credit_status NOT IN ('100', '200') AND ddp.create_date = udr.registration_date AND ula.audits_status = 1 THEN 1 ELSE 0 END) tOWithdrawal,
            SUM(CASE WHEN ddp.credit_status NOT IN ('100', '200') AND ddp.create_date = DATE_ADD(udr.registration_date, INTERVAL 1 DAY) AND ula.audits_status = 1 THEN 1 ELSE 0 END) t1Withdrawal,
            SUM(CASE WHEN ddp.credit_status NOT IN ('100', '200') AND ddp.create_date = DATE_ADD(udr.registration_date, INTERVAL 2 DAY) AND ula.audits_status = 1 THEN 1 ELSE 0 END) t2Withdrawal,
            SUM(CASE WHEN ddp.credit_status NOT IN ('100', '200') AND ddp.create_date = DATE_ADD(udr.registration_date, INTERVAL 3 DAY) AND ula.audits_status = 1 THEN 1 ELSE 0 END) t3Withdrawal,
            SUM(CASE WHEN ddp.credit_status NOT IN ('100', '200') AND ddp.create_date = DATE_ADD(udr.registration_date, INTERVAL 4 DAY) AND ula.audits_status = 1 THEN 1 ELSE 0 END) t4Withdrawal,
            SUM(CASE WHEN ddp.credit_status NOT IN ('100', '200') AND ddp.create_date = DATE_ADD(udr.registration_date, INTERVAL 5 DAY) AND ula.audits_status = 1 THEN 1 ELSE 0 END) t5Withdrawal,
            SUM(CASE WHEN ddp.credit_status NOT IN ('100', '200') AND ddp.create_date = DATE_ADD(udr.registration_date, INTERVAL 6 DAY) AND ula.audits_status = 1 THEN 1 ELSE 0 END) t6Withdrawal,
            SUM(CASE WHEN ddp.credit_status NOT IN ('100', '200') AND ddp.create_date = DATE_ADD(udr.registration_date, INTERVAL 7 DAY) AND ula.audits_status = 1 THEN 1 ELSE 0 END) t7Withdrawal,
            COUNT(DISTINCT CASE WHEN ddp.credit_status NOT IN ('100', '200') AND ddp.create_date BETWEEN DATE_ADD(udr.registration_date, INTERVAL 0 DAY)
                AND DATE_ADD(udr.registration_date, INTERVAL 3 DAY) AND ula.audits_status = 1 THEN ddp.user_id END) day3WithdrawalCount,
            COUNT(DISTINCT CASE WHEN ddp.credit_status NOT IN ('100', '200') AND ddp.create_date BETWEEN DATE_ADD(udr.registration_date, INTERVAL 0 DAY)
                AND DATE_ADD(udr.registration_date, INTERVAL 7 DAY) AND ula.audits_status = 1 THEN ddp.user_id END) day7WithdrawalCount,
            COUNT(DISTINCT CASE WHEN ddp.credit_status NOT IN ('100', '200') AND ddp.create_date BETWEEN DATE_ADD(udr.registration_date, INTERVAL 0 DAY)
                AND DATE_ADD(udr.registration_date, INTERVAL 15 DAY) AND ula.audits_status = 1 THEN ddp.user_id END) day15WithdrawalCount,
            COUNT(DISTINCT CASE WHEN ddp.credit_status NOT IN ('100', '200') AND ddp.create_date BETWEEN DATE_ADD(udr.registration_date, INTERVAL 0 DAY)
                AND DATE_ADD(udr.registration_date, INTERVAL 30 DAY) AND ula.audits_status = 1 THEN ddp.user_id END) day30WithdrawalCount
        FROM
            (SELECT id, channel_id, source_mode, DATE_FORMAT(create_time, '%Y-%m-%d') AS registration_date FROM user_data) udr
                LEFT JOIN (SELECT DISTINCT credit_status,DATE_FORMAT(create_time, '%Y-%m-%d') AS create_date,user_id FROM disburse_data ) ddp ON udr.id = ddp.user_id
                LEFT JOIN channel_data cd ON udr.channel_id = cd.id
                LEFT JOIN (SELECT DISTINCT user_id,  audits_status ,online_type FROM user_loan_apply WHERE apply_type = 0) ula ON udr.id = ula.user_id
        <where>
            <if test="timeStart != null and timeStart != '' and timeEnd != null and timeEnd != ''">
                AND udr.registration_date &gt;= #{timeStart} AND udr.registration_date &lt; #{timeEnd}
            </if>
            <if test="channelId != null and channelId != ''">
                and udr.channel_id IN
                <foreach item="item" index="index" collection="channelId.split(',')"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="sourceMode != null and sourceMode != ''">
                AND ula.online_type = #{sourceMode}
            </if>
        </where>
        GROUP BY
            udr.registration_date,
            cd.name
        order by udr.registration_date desc
    </select>

    <select id="selectCountZC" resultType="com.rongchen.byh.webadmin.upms.vo.export.DailyCreditVo">
        SELECT
            DATE(ud.create_time) createTime,
            count(ud.id) count,
            cd.`name`,
            cd.id channelId,
            CASE ud.source_mode
                WHEN 0 THEN '线上'
                WHEN 1 THEN '线下'
                WHEN 2 THEN '空中放款'
                WHEN 3 THEN '全流程API'
                ELSE ''
                END sourceMode
        FROM
            (select id,channel_id,create_time,source_mode from user_data) ud
        left join channel_data cd on cd.id = ud.channel_id
        GROUP BY DATE(ud.create_time) ,cd.`name`,ud.source_mode
    </select>

    <select id="selectCountJJ" resultType="com.rongchen.byh.webadmin.upms.vo.export.DailyCreditVo">
        SELECT
            DATE(ud.create_time) createTime,
            count(ud.id) count,
            cd.`name`,
            cd.id channelId,
            CASE ul.online_type
                WHEN 0 THEN '线上'
                WHEN 1 THEN '线下'
                WHEN 2 THEN '空中放款'
                WHEN 3 THEN '全流程API'
                ELSE ''
                END sourceMode
        FROM
            (select id,channel_id,create_time from user_data) ud
        INNER join (select user_id,online_type from user_loan_apply where apply_type = 0) ul  on ul.user_id = ud.id
        left join channel_data cd on cd.id = ud.channel_id
        GROUP BY  DATE(ud.create_time) ,cd.`name`,ul.online_type
    </select>

    <select id="selectCountSXTG" resultType="com.rongchen.byh.webadmin.upms.vo.export.DailyCreditVo">
        SELECT
            DATE(ula.create_time) createTime,
            count(ud.id) count,
            cd.`name`,
            cd.id channelId,
            CASE ula.online_type
                WHEN 0 THEN '线上'
                WHEN 1 THEN '线下'
                WHEN 2 THEN '空中放款'
                WHEN 3 THEN '全流程API'
                ELSE ''
                END sourceMode
        FROM
            user_loan_apply ula
        left join (select id,channel_id,create_time from user_data) ud on ula.user_id = ud.id
        left join channel_data cd on cd.id = ud.channel_id
        WHERE ula.apply_type = 0 and ula.audits_status = 1
        GROUP BY DATE(ula.create_time) ,cd.`name`,ula.online_type;
    </select>
    <select id="selectCountSXJJ" resultType="com.rongchen.byh.webadmin.upms.vo.export.DailyCreditVo">
        SELECT
            DATE(ula.create_time) createTime,
            count(ud.id) count,
            cd.`name`,
            cd.id channelId,
            CASE ula.online_type
                WHEN 0 THEN '线上'
                WHEN 1 THEN '线下'
                WHEN 2 THEN '空中放款'
                WHEN 3 THEN '全流程API'
                ELSE ''
                END sourceMode
        FROM
            user_loan_apply ula
        left join (select id,channel_id,create_time,source_mode from user_data) ud on ula.user_id = ud.id
        left join channel_data cd on cd.id = ud.channel_id
        WHERE ula.apply_type = 0 and ula.audits_status IN ('2','4')
        GROUP BY DATE(ula.create_time) ,cd.`name`,ula.online_type
    </select>
    <select id="selectCountPay" resultType="com.rongchen.byh.webadmin.upms.vo.export.DailyCreditVo">
        SELECT
            DATE(ddp.create_time) createTime,
            cd.`name`,
            cd.id channelId,
            CASE ula.online_type
                WHEN 0 THEN '线上'
                WHEN 1 THEN '线下'
                WHEN 2 THEN '空中放款'
                WHEN 3 THEN '全流程API'
                ELSE ''
                END sourceMode,
            SUM(CASE WHEN ddp.credit_status in(500,400) AND ula.audits_status = 1 THEN 1 ELSE 0 END) payoutsUser,
            SUM(CASE WHEN ddp.credit_status = 500 AND ula.audits_status = 1 THEN 1 ELSE 0 END) successUser,
            SUM(CASE WHEN ddp.credit_status = 400 AND ula.audits_status = 1 THEN 1 ELSE 0 END) errorUser,
            SUM(CASE WHEN ddp.credit_status = 500 AND ula.audits_status = 1 THEN ddp.credit_amount ELSE 0 END) successAmount
        FROM
            (SELECT DISTINCT credit_status, DATE(create_time) AS create_time, user_id, credit_amount FROM disburse_data) ddp
        LEFT JOIN (SELECT DISTINCT user_id, audits_status,online_type FROM user_loan_apply WHERE apply_type = 0) ula ON ddp.user_id = ula.user_id
        LEFT JOIN (SELECT id, channel_id, source_mode, DATE(create_time) AS registration_date FROM user_data) udr ON ddp.user_id = udr.id
        LEFT JOIN channel_data cd ON udr.channel_id = cd.id
        GROUP BY
            DATE(ddp.create_time), cd.`name`,
            ula.online_type
    </select>
    <select id="overdueUserCountDaysList" resultType="com.rongchen.byh.webadmin.upms.vo.export.OverdueUserDetailsVo">
        SELECT
            fd.loanDate,
            COUNT(DISTINCT fd.user_id) AS countUser,
            fd.channelName,
            CASE
                WHEN fd.loanDate = CURDATE()
                THEN IFNULL(os.overdueCount, 0)
                ELSE  COUNT(DISTINCT CASE WHEN DATE(op.create_time) = CURDATE() THEN op.user_id END)
            END AS overdueUserCount,
            CASE
                WHEN fd.loanDate = CURDATE()
                THEN IFNULL(CONCAT(ROUND(os.overdueCount / NULLIF(COUNT(DISTINCT fd.user_id), 0) * 100, 2), '%'), '0.00%')
                ELSE CONCAT(ROUND(COUNT(DISTINCT CASE WHEN DATE(op.create_time) = CURDATE() THEN op.user_id END) / NULLIF(COUNT(DISTINCT fd.user_id), 0) * 100, 2), '%')
            END AS overdue,
            CONCAT(ROUND(COUNT(DISTINCT CASE WHEN op.overdue_days = 1 THEN op.user_id END) / NULLIF(COUNT(DISTINCT fd.user_id), 0) * 100, 2), '%') AS overdue01DayCount,
            CONCAT(ROUND(COUNT(DISTINCT CASE WHEN op.overdue_days = 2 THEN op.user_id END) / NULLIF(COUNT(DISTINCT fd.user_id), 0) * 100, 2), '%') AS overdue02DayCount,
            CONCAT(ROUND(COUNT(DISTINCT CASE WHEN op.overdue_days = 3 THEN op.user_id END) / NULLIF(COUNT(DISTINCT fd.user_id), 0) * 100, 2), '%') AS overdue03DayCount,
            CONCAT(ROUND(COUNT(DISTINCT CASE WHEN op.overdue_days = 4 THEN op.user_id END) / NULLIF(COUNT(DISTINCT fd.user_id), 0) * 100, 2), '%') AS overdue04DayCount,
            CONCAT(ROUND(COUNT(DISTINCT CASE WHEN op.overdue_days = 5 THEN op.user_id END) / NULLIF(COUNT(DISTINCT fd.user_id), 0) * 100, 2), '%') AS overdue05DayCount,
            CONCAT(ROUND(COUNT(DISTINCT CASE WHEN op.overdue_days = 6 THEN op.user_id END) / NULLIF(COUNT(DISTINCT fd.user_id), 0) * 100, 2), '%') AS overdue06DayCount,
            CONCAT(ROUND(COUNT(DISTINCT CASE WHEN op.overdue_days = 7 THEN op.user_id END) / NULLIF(COUNT(DISTINCT fd.user_id), 0) * 100, 2), '%') AS overdue07DayCount,
            CONCAT(ROUND(COUNT(DISTINCT CASE WHEN op.overdue_days = 8 THEN op.user_id END) / NULLIF(COUNT(DISTINCT fd.user_id), 0) * 100, 2), '%') AS overdue08DayCount,
            CONCAT(ROUND(COUNT(DISTINCT CASE WHEN op.overdue_days = 9 THEN op.user_id END) / NULLIF(COUNT(DISTINCT fd.user_id), 0) * 100, 2), '%') AS overdue09DayCount,
            CONCAT(ROUND(COUNT(DISTINCT CASE WHEN op.overdue_days = 10 THEN op.user_id END) / NULLIF(COUNT(DISTINCT fd.user_id), 0) * 100, 2), '%') AS overdue10DayCount,
            CONCAT(ROUND(COUNT(DISTINCT CASE WHEN op.overdue_days >= 15 THEN op.user_id END) / NULLIF(COUNT(DISTINCT fd.user_id), 0) * 100, 2), '%') AS overdue15DayCount,
            CONCAT(ROUND(COUNT(DISTINCT CASE WHEN op.overdue_days >= 20 THEN op.user_id END) / NULLIF(COUNT(DISTINCT fd.user_id), 0) * 100, 2), '%') AS overdue20DayCount,
            CONCAT(ROUND(COUNT(DISTINCT CASE WHEN op.overdue_days >= 30 THEN op.user_id END) / NULLIF(COUNT(DISTINCT fd.user_id), 0) * 100, 2), '%') AS overdue30DayCount,
            CONCAT(ROUND(COUNT(DISTINCT CASE WHEN op.overdue_days >= 60 THEN op.user_id END) / NULLIF(COUNT(DISTINCT fd.user_id), 0) * 100, 2), '%') AS overdue60DayCount,
            CONCAT(ROUND(COUNT(DISTINCT CASE WHEN op.overdue_days >= 90 THEN op.user_id END) / NULLIF(COUNT(DISTINCT fd.user_id), 0) * 100, 2), '%') AS overdue90DayCount
        FROM (
            SELECT
                dd.user_id,
                rs.repay_ownb_date AS loanDate,
                ud.channel_id,
                IFNULL(cd.`name`, 'xxmd') AS channelName
            FROM
                disburse_data dd
            LEFT JOIN repay_schedule rs ON dd.id = rs.disburse_id and rs.repay_term = #{repayTerm}
            LEFT JOIN user_data ud ON ud.id = dd.user_id
            LEFT JOIN channel_data cd ON cd.id = ud.channel_id
            WHERE
                dd.credit_status IN (500, 600)
        ) fd
        LEFT JOIN overdue_process op ON fd.user_id = op.user_id
        LEFT JOIN (
            SELECT
                COUNT(DISTINCT rs.user_id) AS overdueCount,
                ud.channel_id
            FROM
                repay_schedule rs
            JOIN user_data ud ON ud.id = rs.user_id AND rs.repay_term = #{repayTerm}
            WHERE
                rs.repay_ownb_date = CURDATE()
                AND rs.settle_flag = 'RUNNING'
                AND rs.date_pay IS NULL
            GROUP BY
                ud.channel_id
        ) os ON os.channel_id = fd.channel_id
        <where>
            <if test="timeStart != null and timeStart != '' and timeEnd != null and timeEnd != ''">
                AND fd.loanDate  &gt;= #{timeStart} AND fd.loanDate &lt; #{timeEnd}
            </if>
            <if test="channelId != null and channelId != ''">
                and fd.channel_id IN
                <foreach item="item" index="index" collection="channelId.split(',')"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY
            fd.loanDate,fd.channelName;
    </select>
    <select id="overdueAmountDaysList" resultType="com.rongchen.byh.webadmin.upms.vo.export.OverdueAmountDetailsVo">
        SELECT
            fd.loanDate,
            IFNULL(cd.`name`, 'xxmd') AS channelName,
            COUNT(DISTINCT fd.user_id) AS countUser,
            COALESCE(cmo.overdue_users, COUNT(DISTINCT CASE WHEN DATE(lo.create_time) = CURDATE() THEN lo.user_id END)) AS overdueUserCount,
            fd.sumCreditAmount,
            COALESCE(cmo.total_due, ROUND(SUM(CASE WHEN DATE(lo.create_time) = CURDATE() THEN lo.total_ret_prin END), 2), 0) AS totalRetPrin,
            CONCAT(ROUND(
            COALESCE(cmo.total_due, SUM(COALESCE(CASE WHEN DATE(lo.create_time) = CURDATE() THEN lo.total_ret_prin END, 0)))
            / NULLIF(fd.sumCreditAmount, 0) * 100,
            2), '%') AS overdueRate,
            CONCAT(ROUND(SUM(CASE WHEN lo.overdue_days = 1 THEN lo.total_ret_prin ELSE 0 END) / NULLIF(fd.sumCreditAmount, 0) * 100, 2), '%') AS overdue1DayRate,
            CONCAT(ROUND(SUM(CASE WHEN lo.overdue_days = 2 THEN lo.total_ret_prin ELSE 0 END) / NULLIF(fd.sumCreditAmount, 0) * 100, 2), '%') AS overdue2DayRate,
            CONCAT(ROUND(SUM(CASE WHEN lo.overdue_days = 3 THEN lo.total_ret_prin ELSE 0 END) / NULLIF(fd.sumCreditAmount, 0) * 100, 2), '%') AS overdue3DayRate,
            CONCAT(ROUND(SUM(CASE WHEN lo.overdue_days = 4 THEN lo.total_ret_prin ELSE 0 END) / NULLIF(fd.sumCreditAmount, 0) * 100, 2), '%') AS overdue4DayRate,
            CONCAT(ROUND(SUM(CASE WHEN lo.overdue_days = 5 THEN lo.total_ret_prin ELSE 0 END) / NULLIF(fd.sumCreditAmount, 0) * 100, 2), '%') AS overdue5DayRate,
            CONCAT(ROUND(SUM(CASE WHEN lo.overdue_days = 6 THEN lo.total_ret_prin ELSE 0 END) / NULLIF(fd.sumCreditAmount, 0) * 100, 2), '%') AS overdue6DayRate,
            CONCAT(ROUND(SUM(CASE WHEN lo.overdue_days = 7 THEN lo.total_ret_prin ELSE 0 END) / NULLIF(fd.sumCreditAmount, 0) * 100, 2), '%') AS overdue7DayRate,
            CONCAT(ROUND(SUM(CASE WHEN lo.overdue_days = 8 THEN lo.total_ret_prin ELSE 0 END) / NULLIF(fd.sumCreditAmount, 0) * 100, 2), '%') AS overdue8DayRate,
            CONCAT(ROUND(SUM(CASE WHEN lo.overdue_days = 9 THEN lo.total_ret_prin ELSE 0 END) / NULLIF(fd.sumCreditAmount, 0) * 100, 2), '%') AS overdue9DayRate,
            CONCAT(ROUND(SUM(CASE WHEN lo.overdue_days = 10 THEN lo.total_ret_prin ELSE 0 END) / NULLIF(fd.sumCreditAmount, 0) * 100, 2), '%') AS overdue10DayRate,
            CONCAT(ROUND(SUM(CASE WHEN lo.overdue_days >= 15 THEN lo.total_ret_prin ELSE 0 END) / NULLIF(fd.sumCreditAmount, 0) * 100, 2), '%') AS overdue15DayRate,
            CONCAT(ROUND(SUM(CASE WHEN lo.overdue_days >= 20 THEN lo.total_ret_prin ELSE 0 END) / NULLIF(fd.sumCreditAmount, 0) * 100, 2), '%') AS overdue20DayRate,
            CONCAT(ROUND(SUM(CASE WHEN lo.overdue_days >= 30 THEN lo.total_ret_prin ELSE 0 END) / NULLIF(fd.sumCreditAmount, 0) * 100, 2), '%') AS overdue30DayRate,
            CONCAT(ROUND(SUM(CASE WHEN lo.overdue_days >= 60 THEN lo.total_ret_prin ELSE 0 END) / NULLIF(fd.sumCreditAmount, 0) * 100, 2), '%') AS overdue60DayRate,
            CONCAT(ROUND(SUM(CASE WHEN lo.overdue_days >= 90 THEN lo.total_ret_prin ELSE 0 END) / NULLIF(fd.sumCreditAmount, 0) * 100, 2), '%') AS overdue90DayRate
        FROM (
            SELECT
                dd.user_id,
                dd.id AS disburse_id,
                dd.credit_amount,
                rs.repay_ownb_date AS loanDate,
                ud.channel_id,
                channel_loan_sums.sumCreditAmount
            FROM
                disburse_data dd
            LEFT JOIN repay_schedule rs ON dd.id = rs.disburse_id AND rs.repay_term = #{repayTerm}
            LEFT JOIN user_data ud ON ud.id = dd.user_id
            JOIN (
                SELECT
                    rs.repay_ownb_date AS loanDate,
                    ud.channel_id,
                    SUM(dd.credit_amount) AS sumCreditAmount
                FROM
                    disburse_data dd
                JOIN repay_schedule rs ON dd.id = rs.disburse_id AND rs.repay_term = #{repayTerm}
                JOIN user_data ud ON ud.id = dd.user_id
                WHERE
                    dd.credit_status IN (500, 600)
                GROUP BY
                    rs.repay_ownb_date,
                    ud.channel_id
            ) channel_loan_sums ON rs.repay_ownb_date = channel_loan_sums.loanDate
            AND ud.channel_id = channel_loan_sums.channel_id
            WHERE
                dd.credit_status IN (500, 600)
        ) fd
        LEFT JOIN channel_data cd ON cd.id = fd.channel_id
        LEFT JOIN (
            SELECT
                op.user_id,
                op.disburse_id,
                op.overdue_days,
                op.total_ret_prin,
                ud.channel_id,
                op.create_time
            FROM overdue_process op
            LEFT JOIN user_data ud ON ud.id = op.user_id
            WHERE op.repay_term = (
                SELECT MAX(repay_term)
                FROM overdue_process
                WHERE user_id = op.user_id
                AND disburse_id = op.disburse_id
            )
        ) lo ON fd.user_id = lo.user_id
        AND fd.disburse_id = lo.disburse_id
        AND fd.channel_id = lo.channel_id
        LEFT JOIN (
            SELECT
                cmo.channel_id,
                COUNT(DISTINCT cmo.user_id) AS overdue_users,
                SUM(cmo.total_due) AS total_due
            FROM (
                SELECT
                    rs.user_id,
                    ud.channel_id,
                    SUM(rs.term_ret_prin) AS total_due
                FROM repay_schedule rs
                JOIN disburse_data dd ON rs.disburse_id = dd.id AND rs.repay_term = #{repayTerm}
                AND dd.credit_status IN (500, 600)
                JOIN user_data ud ON ud.id = rs.user_id
                WHERE
                    rs.repay_ownb_date = CURDATE()
                    AND rs.settle_flag = 'RUNNING'
                    AND rs.date_pay IS NULL
                GROUP BY rs.user_id, ud.channel_id
            ) cmo
            GROUP BY cmo.channel_id
        ) cmo ON fd.channel_id = cmo.channel_id AND fd.loanDate = CURDATE()
        <where>
            <if test="timeStart != null and timeStart != '' and timeEnd != null and timeEnd != ''">
                AND fd.loanDate  &gt;= #{timeStart} AND fd.loanDate &lt; #{timeEnd}
            </if>
            <if test="channelId != null and channelId != ''">
                and fd.channel_id IN
                <foreach item="item" index="index" collection="channelId.split(',')"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY
            fd.loanDate,
            IFNULL(cd.`name`, 'xxmd'),
            fd.sumCreditAmount;
    </select>

    <select id="recoveryDaysList" resultType="com.rongchen.byh.webadmin.upms.vo.export.OverDayUserCountVo">
        SELECT
            fd.loanDate,
            fd.channelName,
            COALESCE(
                CONCAT(
                    ROUND(
                        (
                            COUNT(DISTINCT CASE WHEN op.overdue_days = 1 THEN op.user_id END) -
                            COUNT(DISTINCT CASE WHEN op.overdue_days = DATEDIFF(CURDATE(), fd.loanDate) THEN op.user_id END)
                        ) / NULLIF(COUNT(DISTINCT CASE WHEN op.overdue_days = 1 THEN op.user_id END), 0) * 100,
                        2
                    ),
                    '%'
                ),
                '0.00%'
            ) AS overdue,
            COUNT(DISTINCT CASE WHEN op.overdue_days = 1 THEN op.user_id END) overDay1,
            COUNT(DISTINCT CASE WHEN op.overdue_days = 2 THEN op.user_id END) overDay2,
            COUNT(DISTINCT CASE WHEN op.overdue_days = 3 THEN op.user_id END) overDay3,
            COUNT(DISTINCT CASE WHEN op.overdue_days = 4 THEN op.user_id END) overDay4,
            COUNT(DISTINCT CASE WHEN op.overdue_days = 5 THEN op.user_id END) overDay5,
            COUNT(DISTINCT CASE WHEN op.overdue_days = 6 THEN op.user_id END) overDay6,
            COUNT(DISTINCT CASE WHEN op.overdue_days = 7 THEN op.user_id END) overDay7,
            COUNT(DISTINCT CASE WHEN op.overdue_days = 8 THEN op.user_id END) overDay8,
            COUNT(DISTINCT CASE WHEN op.overdue_days = 9 THEN op.user_id END) overDay9,
            COUNT(DISTINCT CASE WHEN op.overdue_days = 10 THEN op.user_id END) overDay10,
            COUNT(DISTINCT CASE WHEN op.overdue_days = 11 THEN op.user_id END) overDay11,
            COUNT(DISTINCT CASE WHEN op.overdue_days = 12 THEN op.user_id END) overDay12,
            COUNT(DISTINCT CASE WHEN op.overdue_days = 13 THEN op.user_id END) overDay13,
            COUNT(DISTINCT CASE WHEN op.overdue_days = 14 THEN op.user_id END) overDay14,
            COUNT(DISTINCT CASE WHEN op.overdue_days = 15 THEN op.user_id END) overDay15,
            COUNT(DISTINCT CASE WHEN op.overdue_days = 16 THEN op.user_id END) overDay16
        FROM (
            SELECT
                dd.user_id,
                rs.repay_ownb_date loanDate,
                ud.channel_id,
                IFNULL(cd.`name`, 'xxmd') AS channelName
            FROM
                disburse_data dd
            LEFT JOIN repay_schedule rs ON dd.id = rs.disburse_id and rs.repay_term = #{repayTerm}
            LEFT JOIN user_data ud ON ud.id = dd.user_id
            LEFT JOIN channel_data cd ON cd.id = ud.channel_id
            WHERE
                dd.credit_status IN (500, 600)
        ) fd
        LEFT JOIN overdue_process op ON fd.user_id = op.user_id
        <where>
            <if test="timeStart != null and timeStart != '' and timeEnd != null and timeEnd != ''">
                AND fd.loanDate &gt;= #{timeStart} AND fd.loanDate &lt; #{timeEnd}
            </if>
            <if test="channelId != null and channelId != ''">
                and fd.channel_id IN
                <foreach item="item" index="index" collection="channelId.split(',')"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY
            fd.loanDate,fd.channelName;
    </select>


    <select id="recoveryDaysAmountList"  resultType="com.rongchen.byh.webadmin.upms.vo.export.OverDayUserAmountVo" >
        SELECT
            fd.loanDate,
            IFNULL(cd.`name`, 'xxmd') AS channelName,
            COALESCE(
                CONCAT(
                    ROUND(
                        (
                            ROUND(SUM(CASE WHEN lo.overdue_days = 1 THEN lo.total_ret_prin ELSE 0 END), 2) -
                            ROUND(SUM(CASE WHEN lo.overdue_days = DATEDIFF(CURDATE(), fd.loanDate) THEN lo.total_ret_prin ELSE 0 END), 2)
                        ) / NULLIF(ROUND(SUM(CASE WHEN lo.overdue_days = 1 THEN lo.total_ret_prin ELSE 0 END), 2), 0) * 100,
                        2
                    ),
                    '%'
                ),
            '0.00%'
            ) AS overdue,
            ROUND(SUM(CASE WHEN lo.overdue_days = 1 THEN lo.total_ret_prin ELSE 0 END), 2) AS overdue1DayAmount,
            ROUND(SUM(CASE WHEN lo.overdue_days = 2 THEN lo.total_ret_prin ELSE 0 END), 2) AS overdue2DayAmount,
            ROUND(SUM(CASE WHEN lo.overdue_days = 3 THEN lo.total_ret_prin ELSE 0 END), 2) AS overdue3DayAmount,
            ROUND(SUM(CASE WHEN lo.overdue_days = 4 THEN lo.total_ret_prin ELSE 0 END), 2) AS overdue4DayAmount,
            ROUND(SUM(CASE WHEN lo.overdue_days = 5 THEN lo.total_ret_prin ELSE 0 END), 2) AS overdue5DayAmount,
            ROUND(SUM(CASE WHEN lo.overdue_days = 6 THEN lo.total_ret_prin ELSE 0 END), 2) AS overdue6DayAmount,
            ROUND(SUM(CASE WHEN lo.overdue_days = 7 THEN lo.total_ret_prin ELSE 0 END), 2) AS overdue7DayAmount,
            ROUND(SUM(CASE WHEN lo.overdue_days = 8 THEN lo.total_ret_prin ELSE 0 END), 2) AS overdue8DayAmount,
            ROUND(SUM(CASE WHEN lo.overdue_days = 9 THEN lo.total_ret_prin ELSE 0 END), 2) AS overdue9DayAmount,
            ROUND(SUM(CASE WHEN lo.overdue_days = 10 THEN lo.total_ret_prin ELSE 0 END), 2) AS overdue10DayAmount,
            ROUND(SUM(CASE WHEN lo.overdue_days = 11 THEN lo.total_ret_prin ELSE 0 END), 2) AS overdue11DayAmount,
            ROUND(SUM(CASE WHEN lo.overdue_days = 12 THEN lo.total_ret_prin ELSE 0 END), 2) AS overdue12DayAmount,
            ROUND(SUM(CASE WHEN lo.overdue_days = 13 THEN lo.total_ret_prin ELSE 0 END), 2) AS overdue13DayAmount,
            ROUND(SUM(CASE WHEN lo.overdue_days = 14 THEN lo.total_ret_prin ELSE 0 END), 2) AS overdue14DayAmount,
            ROUND(SUM(CASE WHEN lo.overdue_days = 15 THEN lo.total_ret_prin ELSE 0 END), 2) AS overdue15DayAmount,
            ROUND(SUM(CASE WHEN lo.overdue_days = 16 THEN lo.total_ret_prin ELSE 0 END), 2) AS overdue16DayAmount
        FROM (
            SELECT
                dd.user_id,
                dd.id AS disburse_id,
                dd.credit_amount,
                rs.repay_ownb_date AS loanDate,
                ud.channel_id
            FROM disburse_data dd
            LEFT JOIN repay_schedule rs ON dd.id = rs.disburse_id AND rs.repay_term = #{repayTerm}
            LEFT JOIN user_data ud ON ud.id = dd.user_id
            WHERE dd.credit_status IN (500, 600)
        ) fd
        LEFT JOIN channel_data cd ON cd.id = fd.channel_id
            LEFT JOIN (
            SELECT
                op.user_id,
                op.disburse_id,
                op.overdue_days,
                op.total_ret_prin,
                ud.channel_id,
                op.create_time
            FROM overdue_process op
            LEFT JOIN user_data ud ON ud.id = op.user_id
            WHERE op.repay_term = (
                SELECT MAX(repay_term)
                FROM overdue_process
                WHERE user_id = op.user_id
                AND disburse_id = op.disburse_id
            )
        ) lo ON fd.user_id = lo.user_id
        AND fd.disburse_id = lo.disburse_id
        AND fd.channel_id = lo.channel_id
        <where>
            <if test="timeStart != null and timeStart != '' and timeEnd != null and timeEnd != ''">
                AND fd.loanDate &gt;= #{timeStart} AND fd.loanDate &lt; #{timeEnd}
            </if>
            <if test="channelId != null and channelId != ''">
                and fd.channel_id IN
                <foreach item="item" index="index" collection="channelId.split(',')"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY
            fd.loanDate,
            IFNULL(cd.`name`, 'xxmd');
    </select>
    <select id="loanBalanceList" resultType="com.rongchen.byh.webadmin.upms.vo.export.OutstandingLoanBalanceVo">
        SELECT DISTINCT
        dd.loan_no AS loanNo,
        ude.web_name AS customer,
        CASE ud.source_mode
            WHEN 0 THEN '线上'
            WHEN 1 THEN '线下'
            WHEN 2 THEN '空中放款'
            WHEN 3 THEN '信合元'
            ELSE ''
        END AS partner,
        rs3.start_date AS startDate,
        rs3.end_date AS endDate,
        rs3.days_count AS durationDays,
        rs3.term_ret_prin AS dailyLoanBalance,
        DATE(dd.loan_time) AS loanDate,
        dd.fund_code AS originalFundCode
        FROM
            disburse_data dd
        LEFT JOIN user_data ud ON dd.user_id = ud.id
        LEFT JOIN user_detail ude ON ud.id = ude.user_id
        LEFT JOIN (
            SELECT * FROM (
                SELECT
                    r.disburse_id,
                    CASE
                        WHEN rp.settlement_status = 'CLOSE' THEN  r.repay_intb_date
                        ELSE DATE_FORMAT(r.date_pay, '%Y-%m-01')
                    END AS start_date,
                    DATE_SUB(r.date_pay, INTERVAL 1 DAY) AS end_date,
                    CASE
                        WHEN rp.settlement_status = 'CLOSE' THEN  DATEDIFF(DATE_SUB(r.date_pay, INTERVAL 1 DAY), r.repay_intb_date) + 1
                        ELSE DATEDIFF(DATE_SUB(r.date_pay, INTERVAL 1 DAY), DATE_FORMAT(r.date_pay, '%Y-%m-01')) + 1
                    END AS days_count,
                    rp.before_repay_principal AS term_ret_prin
                FROM `repay_schedule` r
                JOIN (
                    SELECT
                        disburse_id,
                        SUM(term_ret_prin) AS before_repay_principal,
                        CASE
                        WHEN COUNT(*) = 12 AND SUM(CASE WHEN settle_flag = 'CLOSE' THEN 1 ELSE 0 END) = 12
                        THEN 'CLOSE'
                        ELSE 'RUNNING'
                        END AS settlement_status
                    FROM `repay_schedule`
                    <where>
                        <if test="timeStart != null and timeStart != '' and timeEnd != null and timeEnd != ''">
                            ((date_pay >= #{timeStart} AND  date_pay &lt; #{timeEnd}) OR date_pay IS NULL)
                        </if>
                    </where>
                    GROUP BY disburse_id
                ) rp ON r.disburse_id = rp.disburse_id
               <where>
                   <if test="timeStart != null and timeStart != '' and timeEnd != null and timeEnd != ''">
                       r.date_pay >= #{timeStart} AND  r.date_pay &lt; #{timeEnd}
                   </if>
               </where>

                UNION ALL

                SELECT
                    r.disburse_id,
                    r.date_pay AS start_date,
                    LAST_DAY(r.date_pay) AS end_date,
                    DATEDIFF(LAST_DAY(r.date_pay), r.date_pay)+1 AS days_count,
                    rp.before_repay_principal - r.term_ret_prin AS term_ret_prin
                FROM `repay_schedule` r
                JOIN (
                    SELECT
                        disburse_id,
                        SUM(term_ret_prin) AS before_repay_principal
                    FROM `repay_schedule`
                    <where>
                        <if test="timeStart != null and timeStart != '' and timeEnd != null and timeEnd != ''">
                            ((date_pay >= #{timeStart} AND  date_pay &lt; #{timeEnd}) OR date_pay IS NULL)
                        </if>
                    </where>
                    GROUP BY disburse_id
                ) rp ON r.disburse_id = rp.disburse_id
                <where>
                    <if test="timeStart != null and timeStart != '' and timeEnd != null and timeEnd != ''">
                        r.date_pay >= #{timeStart} AND  r.date_pay &lt; #{timeEnd}
                    </if>
                </where>
            ) t WHERE t.term_ret_prin > 0
        ) rs3 ON rs3.disburse_id = dd.id
        LEFT JOIN channel_data cd ON ud.channel_id = cd.id
        <where>
            rs3.start_date IS NOT NULL
            <if test="loanTimeStart != null and loanTimeStart != '' and loanTimeEnd != null and loanTimeEnd != ''">
                AND  DATE(dd.loan_time) >= #{loanTimeStart} AND DATE(dd.loan_time) &lt; #{loanTimeEnd}
            </if>
            <if test="channelId != null and channelId != ''">
                and ud.channel_id IN
                <foreach item="item" index="index" collection="channelId.split(',')"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="fundCode != null and fundCode != ''">
                and dd.fund_code IN
                <foreach item="item" index="index" collection="fundCode.split(',')"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY dd.loan_no DESC;
    </select>
</mapper>
