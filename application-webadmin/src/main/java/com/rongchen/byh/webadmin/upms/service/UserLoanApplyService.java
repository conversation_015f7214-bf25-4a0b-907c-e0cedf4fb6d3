package com.rongchen.byh.webadmin.upms.service;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;
import com.rongchen.byh.webadmin.upms.model.UserLoanApplyEntity;
import com.rongchen.byh.webadmin.upms.dao.UserLoanApplyMapper;
/**

 * 项目名称：byh_java
 * 文件名称: UserLoanApplyService
 * 创建时间: 2025-06-05 16:36
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.webadmin.upms.service
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
@Service
public class UserLoanApplyService extends ServiceImpl<UserLoanApplyMapper, UserLoanApplyEntity> {

    
    public int insertSelective(UserLoanApplyEntity record) {
        return baseMapper.insertSelective(record);
    }
    
    public int updateByPrimaryKeySelective(UserLoanApplyEntity record) {
        return baseMapper.updateByPrimaryKeySelective(record);
    }
    
    public int updateByPrimaryKey(UserLoanApplyEntity record) {
        return baseMapper.updateByPrimaryKey(record);
    }
    
    public int updateBatch(List<UserLoanApplyEntity> list) {
        return baseMapper.updateBatch(list);
    }
    
    public int updateBatchSelective(List<UserLoanApplyEntity> list) {
        return baseMapper.updateBatchSelective(list);
    }
    
    public int batchInsert(List<UserLoanApplyEntity> list) {
        return baseMapper.batchInsert(list);
    }
    
    public int batchInsertOrUpdate(List<UserLoanApplyEntity> list) {
        return baseMapper.batchInsertOrUpdate(list);
    }
}
