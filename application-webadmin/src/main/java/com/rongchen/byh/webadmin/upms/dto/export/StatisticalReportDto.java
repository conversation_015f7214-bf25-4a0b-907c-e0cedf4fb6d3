package com.rongchen.byh.webadmin.upms.dto.export;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class StatisticalReportDto {

    @Schema(description = "日期开始时间 yyyy-mm-dd")
    private String timeStart;

    @Schema(description = "日期结束时间 yyyy-mm-dd")
    private String timeEnd;

    @Schema(description = "月份开始时间 yyyy-mm")
    private String yearMonthStart;

    @Schema(description = "月份结束时间 yyyy-mm")
    private String yearMonthEnd;

    @Schema(description = "放款开始时间 yyyy-mm-dd")
    private String loanTimeStart;

    @Schema(description = "放款结束时间 yyyy-mm-dd")
    private String loanTimeEnd;

    @Schema(description = "门店名称")
    private String storeName;

    @Schema(description = "小组名称")
    private String groupName;

    @Schema(description = "渠道")
    private String channelId;

    @Schema(description = "来源模式")
    private String sourceMode;

    @Schema(description = "期数")
    private String repayTerm;

    @Schema(description = "资金方", example = "XJ_HB2")
    private String fundCode;

}
