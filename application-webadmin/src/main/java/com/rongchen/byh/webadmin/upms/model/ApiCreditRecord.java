package com.rongchen.byh.webadmin.upms.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName api_credit_record
 */
@TableName(value ="api_credit_record")
@Data
public class ApiCreditRecord implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 授信单号
     */
    private String creditNo;

    /**
     * 我方用户id
     */
    private Long userId;

    /**
     * 外部用户id
     */
    private String outUserId;

    /**
     * 渠道编码
     */
    private String channel;

    /**
     * 客户渠道来源
     */
    private String sourceChannel;

    /**
     * 授信状态 1-授信中 2-授信成功 3-授信拒绝
     */
    private Integer creditStatus;

    /**
     * 失败原因  授信失败有
     */
    private String failReason;

    /**
     * 授信金额  授信成功有
     */
    private BigDecimal creditMoney;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}