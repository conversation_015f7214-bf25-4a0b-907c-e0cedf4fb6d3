package com.rongchen.byh.webadmin.reconciliation.strategy;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.rongchen.byh.webadmin.reconciliation.context.ReconciliationContext;
import com.rongchen.byh.webadmin.reconciliation.model.FieldDifferenceDetail;
import com.rongchen.byh.webadmin.reconciliation.model.NormalizedTransaction;
import com.rongchen.byh.webadmin.reconciliation.rules.dsl.CustomComparisonFunction;
import com.rongchen.byh.webadmin.reconciliation.rules.dsl.CustomComparisonFunctionRegistry;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.beanutils.PropertyUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 基于JSON DSL的比较策略实现。
 * <p>
 * 此策略解析在 reconciliation_rules 表中定义的JSON DSL规则，
 * 并根据这些规则比较两个标准化记录的字段。
 */
public class JsonDslComparisonStrategy implements ComparisonStrategy {

    private static final Logger logger = LoggerFactory.getLogger(JsonDslComparisonStrategy.class);
    private static final ObjectMapper objectMapper = new ObjectMapper(); // Jackson ObjectMapper

    private final JsonNode comparisonFieldsNode; // 从规则的 rule_content_json.comparisonFields 获取
    private final CustomComparisonFunctionRegistry customFunctionRegistry; // 用于查找自定义比较函数
    // private final ExpressionEngine expressionEngine; // 可选的表达式引擎，如Aviator

    /**
     * 构造函数。
     *
     * @param comparisonFieldsNode   从对账规则JSON中解析出的 'comparisonFields' 节点。
     * @param customFunctionRegistry 自定义比较函数注册表。
     */
    public JsonDslComparisonStrategy(JsonNode comparisonFieldsNode,
            CustomComparisonFunctionRegistry customFunctionRegistry) {
        this.comparisonFieldsNode = Objects.requireNonNull(comparisonFieldsNode, "comparisonFieldsNode 不能为空");
        this.customFunctionRegistry = Objects.requireNonNull(customFunctionRegistry, "customFunctionRegistry 不能为空");
        // this.expressionEngine = expressionEngine; // 如果使用表达式引擎
    }

    @Override
    public List<FieldDifferenceDetail> compare(
            NormalizedTransaction ourRecord,
            NormalizedTransaction partnerRecord,
            Map<String, Object> strategyParameters,
            ReconciliationContext context) throws Exception {

        List<FieldDifferenceDetail> differences = new ArrayList<>();

        if (ourRecord == null || partnerRecord == null) {
            logger.warn("参与比较的记录不能为null。我方记录: {}, 资方记录: {}", ourRecord, partnerRecord);
            // 根据策略，可以添加一个表示记录缺失的差异，但这通常在匹配阶段处理
            return differences; // 或者抛出异常
        }

        if (!comparisonFieldsNode.isArray()) {
            logger.error("JSON DSL规则中的 'comparisonFields' 必须是一个数组。");
            throw new IllegalArgumentException("'comparisonFields' 格式错误，期望为数组。");
        }

        // 如果comparisonFields为空数组，表示只匹配不比较字段，直接返回无差异
        if (comparisonFieldsNode.isEmpty()) {
            logger.debug("comparisonFields为空数组，只匹配不比较字段，返回无差异。我方记录: {}, 资方记录: {}",
                ourRecord.getOriginalRecordId(), partnerRecord.getOriginalRecordId());
            return differences; // 返回空的差异列表
        }

        for (JsonNode fieldRule : comparisonFieldsNode) {
            String ourFieldName = fieldRule.path("ourField").asText();
            String partnerFieldName = fieldRule.path("partnerField").asText(ourFieldName); // 如果partnerField未指定，则默认使用ourField
            String comparisonType = fieldRule.path("comparisonType").asText("DEFAULT_EQUALS").toUpperCase();
            JsonNode paramsNode = fieldRule.path("params");
            String description = fieldRule.path("description").asText(ourFieldName + " 比较"); // 默认描述

            if (ourFieldName.isEmpty()) {
                logger.warn("比较规则中 'ourField' 缺失，跳过此规则: {}", fieldRule.toString());
                continue;
            }

            Object ourValue = null;
            Object partnerValue = null;

            try {
                ourValue = PropertyUtils.getProperty(ourRecord, ourFieldName);
            } catch (Exception e) {
                logger.warn("获取我方记录字段 '{}' (类型: {}) 的值失败: {}",
                        ourFieldName, ourRecord.getClass().getSimpleName(), e.getMessage());
            }

            try {
                partnerValue = PropertyUtils.getProperty(partnerRecord, partnerFieldName);
            } catch (Exception e) {
                logger.warn("获取资方记录字段 '{}' (类型: {}) 的值失败: {}",
                        partnerFieldName, partnerRecord.getClass().getSimpleName(), e.getMessage());
            }

            boolean match = false;
            String diffMessage = description + ": ";

            try {
                switch (comparisonType) {
                    case "DECIMAL_EQUALS":
                    case "AMOUNT_EQUALS": // 兼容旧称或别名
                        match = compareDecimal(ourValue, partnerValue, paramsNode);
                        if (!match)
                            diffMessage += String.format("我方值[%s] != 资方值[%s]", ourValue, partnerValue);
                        break;
                    case "DECIMAL_TOLERANCE":
                    case "AMOUNT_TOLERANCE":
                        match = compareDecimalWithTolerance(ourValue, partnerValue, paramsNode);
                        if (!match)
                            diffMessage += String.format("我方值[%s], 资方值[%s], 超出容差", ourValue, partnerValue);
                        break;
                    case "STRING_EQUALS":
                        match = compareString(ourValue, partnerValue, false, paramsNode);
                        if (!match)
                            diffMessage += String.format("我方值[%s] != 资方值[%s]", ourValue, partnerValue);
                        break;
                    case "STRING_EQUALS_IGNORE_CASE":
                        match = compareString(ourValue, partnerValue, true, paramsNode);
                        if (!match)
                            diffMessage += String.format("我方值[%s] != 资方值[%s] (忽略大小写)", ourValue, partnerValue);
                        break;
                    case "DATE_EQUALS":
                        match = compareDate(ourValue, partnerValue, paramsNode);
                        if (!match)
                            diffMessage += String.format("我方日期[%s] != 资方日期[%s]", ourValue, partnerValue);
                        break;
                    case "DATETIME_EQUALS":
                        match = compareDateTime(ourValue, partnerValue, paramsNode);
                        if (!match)
                            diffMessage += String.format("我方时间[%s] != 资方时间[%s]", ourValue, partnerValue);
                        break;
                    case "BOOLEAN_EQUALS":
                        match = compareBoolean(ourValue, partnerValue, paramsNode);
                        if (!match)
                            diffMessage += String.format("我方布尔值[%s] != 资方布尔值[%s]", ourValue, partnerValue);
                        break;
                    // case "EXPRESSION": // 需要集成表达式引擎，如Aviator
                    // match = evaluateExpression(ourValue, partnerValue,
                    // paramsNode.path("expression").asText(), context);
                    // if (!match) diffMessage += "表达式评估为false";
                    // break;
                    case "CUSTOM_FUNCTION":
                        String functionName = paramsNode.path("functionName").asText();
                        CustomComparisonFunction customFunc = customFunctionRegistry.get(functionName);
                        if (customFunc != null) {
                            @SuppressWarnings("unchecked") // paramsNode to Map conversion
                            Map<String, Object> funcParams = objectMapper.convertValue(paramsNode, Map.class);
                            match = customFunc.compare(ourValue, partnerValue, funcParams, context);
                            if (!match)
                                diffMessage += String.format("自定义函数 '%s' 比较失败: 我方[%s], 资方[%s]", functionName, ourValue,
                                        partnerValue);
                        } else {
                            logger.warn("未找到自定义比较函数: {}", functionName);
                            diffMessage += "未找到自定义比较函数: " + functionName + "，默认不匹配。";
                            match = false; // 函数未找到，视为不匹配
                        }
                        break;
                    case "DEFAULT_EQUALS": // 默认使用 Objects.equals
                    default:
                        match = Objects.equals(ourValue, partnerValue);
                        if (!match)
                            diffMessage += String.format("我方值[%s] != 资方值[%s] (默认比较)", ourValue, partnerValue);
                        break;
                }
            } catch (Exception e) {
                logger.error("字段 '{}'('{}') 比较时发生错误: {}", ourFieldName, description, e.getMessage(), e);
                diffMessage += "比较异常: " + e.getMessage();
                match = false; // 比较异常，视为不匹配
            }

            if (!match) {
                differences.add(new FieldDifferenceDetail(ourFieldName, ourValue, partnerValue, diffMessage));
            }
        }
        return differences;
    }

    // --- 内部比较辅助方法 ---

    private boolean compareDecimal(Object val1, Object val2, JsonNode params) {
        if (val1 == null && val2 == null)
            return true;
        if (val1 == null || val2 == null)
            return false;
        try {
            BigDecimal bd1 = new BigDecimal(val1.toString().trim());
            BigDecimal bd2 = new BigDecimal(val2.toString().trim());
            return bd1.compareTo(bd2) == 0;
        } catch (NumberFormatException e) {
            logger.warn("compareDecimal 转换数字失败: val1='{}', val2='{}'", val1, val2, e);
            return false;
        }
    }

    private boolean compareDecimalWithTolerance(Object val1, Object val2, JsonNode params) {
        if (val1 == null && val2 == null)
            return true;
        if (val1 == null || val2 == null)
            return false; // 通常不等，除非容差能覆盖单个null

        BigDecimal tolerance = BigDecimal.ZERO;
        if (params != null && params.has("tolerance")) {
            try {
                tolerance = new BigDecimal(params.get("tolerance").asText());
            } catch (NumberFormatException e) {
                logger.warn("容差值 '{}' 不是有效的BigDecimal，将使用0容差。", params.get("tolerance").asText());
            }
        }

        try {
            BigDecimal bd1 = new BigDecimal(val1.toString().trim());
            BigDecimal bd2 = new BigDecimal(val2.toString().trim());
            return bd1.subtract(bd2).abs().compareTo(tolerance) <= 0;
        } catch (NumberFormatException e) {
            logger.warn("compareDecimalWithTolerance 转换数字失败: val1='{}', val2='{}'", val1, val2, e);
            return false;
        }
    }

    private boolean compareString(Object val1, Object val2, boolean ignoreCase, JsonNode params) {
        String s1 = val1 == null ? null : val1.toString();
        String s2 = val2 == null ? null : val2.toString();
        if (params != null && params.path("trim").asBoolean(true)) { // 默认trim
            s1 = s1 != null ? s1.trim() : null;
            s2 = s2 != null ? s2.trim() : null;
        }
        if (s1 == null && s2 == null)
            return true;
        if (s1 == null || s2 == null)
            return false;
        return ignoreCase ? s1.equalsIgnoreCase(s2) : s1.equals(s2);
    }

    private boolean compareDate(Object val1, Object val2, JsonNode params) {
        if (val1 == null && val2 == null)
            return true;
        if (val1 == null || val2 == null)
            return false;

        String format = (params != null && params.has("format")) ? params.get("format").asText("yyyyMMdd") : "yyyyMMdd";
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        try {
            LocalDate date1, date2;
            if (val1 instanceof LocalDate)
                date1 = (LocalDate) val1;
            else
                date1 = LocalDate.parse(val1.toString().trim(), formatter);

            if (val2 instanceof LocalDate)
                date2 = (LocalDate) val2;
            else
                date2 = LocalDate.parse(val2.toString().trim(), formatter);

            return date1.isEqual(date2);
        } catch (DateTimeParseException e) {
            logger.warn("compareDate 解析日期失败: val1='{}', val2='{}', format='{}'", val1, val2, format, e);
            return false;
        }
    }

    private boolean compareDateTime(Object val1, Object val2, JsonNode params) {
        if (val1 == null && val2 == null)
            return true;
        if (val1 == null || val2 == null)
            return false;

        String format = (params != null && params.has("format")) ? params.get("format").asText("yyyyMMddHHmmss")
                : "yyyyMMddHHmmss";
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        try {
            LocalDateTime dt1, dt2;
            if (val1 instanceof LocalDateTime)
                dt1 = (LocalDateTime) val1;
            else
                dt1 = LocalDateTime.parse(val1.toString().trim(), formatter);

            if (val2 instanceof LocalDateTime)
                dt2 = (LocalDateTime) val2;
            else
                dt2 = LocalDateTime.parse(val2.toString().trim(), formatter);

            return dt1.isEqual(dt2);
        } catch (DateTimeParseException e) {
            logger.warn("compareDateTime 解析日期时间失败: val1='{}', val2='{}', format='{}'", val1, val2, format, e);
            return false;
        }
    }

    private boolean compareBoolean(Object val1, Object val2, JsonNode params) {
        if (val1 == null && val2 == null)
            return true;
        if (val1 == null || val2 == null)
            return false;

        try {
            // 尝试将常见字符串表示 (e.g., "true", "false", "1", "0", "Y", "N") 转为布尔值
            Boolean b1 = toBoolean(val1);
            Boolean b2 = toBoolean(val2);
            return Objects.equals(b1, b2);
        } catch (IllegalArgumentException e) {
            logger.warn("compareBoolean 转换布尔值失败: val1='{}', val2='{}'", val1, val2, e);
            return false; // 如果无法转换为布尔值，则认为不相等
        }
    }

    private Boolean toBoolean(Object value) {
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue() != 0;
        }
        String sValue = value.toString().trim().toLowerCase();
        if (sValue.equals("true") || sValue.equals("y") || sValue.equals("1") || sValue.equals("yes")) {
            return true;
        }
        if (sValue.equals("false") || sValue.equals("n") || sValue.equals("0") || sValue.equals("no")) {
            return false;
        }
        throw new IllegalArgumentException("无法将 '" + value + "' 安全转换为布尔值");
    }

    // TODO: 如果选择使用表达式引擎 (如Aviator), 取消下面的注释并实现
    /*
     * private boolean evaluateExpression(Object ourValue, Object partnerValue,
     * String expression, ReconciliationContext context) {
     * if (expression == null || expression.trim().isEmpty()) {
     * logger.warn("表达式为空，默认返回false");
     * return false;
     * }
     * Map<String, Object> env = new HashMap<>();
     * env.put("ourValue", ourValue);
     * env.put("partnerValue", partnerValue);
     * env.put("context", context); // 允许表达式访问上下文信息
     * // 如果需要，还可以放入其他辅助对象或函数到env中
     * try {
     * return (Boolean) AviatorEvaluator.execute(expression, env);
     * } catch (Exception e) {
     * logger.error("执行表达式 '{}' 失败: ourValue={}, partnerValue={}, error={}",
     * expression, ourValue, partnerValue, e.getMessage(), e);
     * return false; // 表达式执行异常，视为不匹配
     * }
     * }
     */

    @Override
    public String getStrategyName() {
        // 可以返回一个固定的名称，或者如果JSON DSL规则本身有ID或名称，可以基于那个生成
        return "JsonDslComparisonStrategy";
    }
}