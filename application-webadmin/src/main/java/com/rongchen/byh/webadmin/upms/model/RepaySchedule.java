package com.rongchen.byh.webadmin.upms.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**

 * 项目名称：byh_java
 * 文件名称: RepaySchedule
 * 创建时间: 2025-05-23 17:12
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.webadmin.upms.model
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */

/**
 * 还款计划表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "`repay_schedule`")
public class RepaySchedule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 支用表id
     */
    @TableField(value = "`disburse_id`")
    private Long disburseId;

    /**
     * 用户id
     */
    @TableField(value = "`user_id`")
    private Long userId;

    /**
     * 还款申请流水
     */
    @TableField(value = "`repay_apply_no`")
    private String repayApplyNo;

    /**
     * 还款起日 自动扣款开始日期 yyyy-MM-dd格式
     */
    @TableField(value = "`repay_ownb_date`")
    private String repayOwnbDate;

    /**
     * 还款止日 自动扣款结束日期 yyyy-MM-dd格式
     */
    @TableField(value = "`repay_owne_date`")
    private String repayOwneDate;

    /**
     * 本期起日 结息周期开始日期 yyyy-MM-dd格式，
     还款计划列表按本期起日正序排列
     */
    @TableField(value = "`repay_intb_date`")
    private String repayIntbDate;

    /**
     * 本期止日 结息周期截止日期 yyyy-MM-dd格式
     */
    @TableField(value = "`repay_inte_date`")
    private String repayInteDate;

    /**
     * 本期应还总金额 元
     */
    @TableField(value = "`total_amt`")
    private BigDecimal totalAmt;

    /**
     * 本期应还本金
     */
    @TableField(value = "`term_ret_prin`")
    private BigDecimal termRetPrin;

    /**
     * 本期应还利息
     */
    @TableField(value = "`term_ret_int`")
    private BigDecimal termRetInt;

    /**
     * 本期应还融担费
     */
    @TableField(value = "`term_guarantor_fee`")
    private BigDecimal termGuarantorFee;

    /**
     * 本期应还罚息
     */
    @TableField(value = "`term_ret_fint`")
    private BigDecimal termRetFint;

    /**
     * 本期应还融担罚费
     */
    @TableField(value = "`term_overdue_guarantor_fee`")
    private BigDecimal termOverdueGuarantorFee;

    /**
     * 本期已还本金
     */
    @TableField(value = "`prin_amt`")
    private BigDecimal prinAmt;

    /**
     * 本期未还本金
     */
    @TableField(value = "`no_ret_amt`")
    private BigDecimal noRetAmt;

    /**
     * 本期已还利息
     */
    @TableField(value = "`int_amt`")
    private BigDecimal intAmt;

    /**
     * 本期未还利息
     */
    @TableField(value = "`no_ret_int`")
    private BigDecimal noRetInt;

    /**
     * 本期已还罚息
     */
    @TableField(value = "`term_fint_finish`")
    private BigDecimal termFintFinish;

    /**
     * 本期未还罚息
     */
    @TableField(value = "`no_ret_fin`")
    private BigDecimal noRetFin;

    /**
     * 本期已还融担费
     */
    @TableField(value = "`guarantor_fee`")
    private BigDecimal guarantorFee;

    /**
     * 本期未还融担费
     */
    @TableField(value = "`no_guarantor_fee`")
    private BigDecimal noGuarantorFee;

    /**
     * 本期应还服务费
     */
    @TableField(value = "`term_service_fee`")
    private BigDecimal termServiceFee;

    /**
     * 本期已还服务费
     */
    @TableField(value = "`service_fee`")
    private BigDecimal serviceFee;

    /**
     * 本期未还服务费
     */
    @TableField(value = "`no_service_fee`")
    private BigDecimal noServiceFee;

    /**
     * 本期已还融担罚费
     */
    @TableField(value = "`overdue_guarantor_fee`")
    private BigDecimal overdueGuarantorFee;

    /**
     * 本期未还融担罚费
     */
    @TableField(value = "`no_overdue_guarantor_fee`")
    private BigDecimal noOverdueGuarantorFee;

    /**
     * 计划状态 可选值：N - 正常，G - 宽限期，O - 逾期，L - 呆滞（逾期90天以上），B - 呆账（逾期180天以上）。
     若无呆滞呆账状态则作逾期状态返回。
     */
    @TableField(value = "`term_status`")
    private String termStatus;

    /**
     * 结清标志 可选值：RUNNING - 未结 ，CLOSE - 已结清，REPAYING-还款中
     */
    @TableField(value = "`settle_flag`")
    private String settleFlag;

    /**
     * 期数
     */
    @TableField(value = "`repay_term`")
    private String repayTerm;

    /**
     * 还款方式 0：线上还款，1：线下还款
     */
    @TableField(value = "`repay_method`")
    private String repayMethod;

    /**
     * 还款发起时间
     */
    @TableField(value = "`pay_time`")
    private String payTime;

    /**
     * 实际还款日 yyyy-MM-dd格式
     */
    @TableField(value = "`date_pay`")
    private String datePay;

    /**
     * 实际还款日期 
     */
    @TableField(value = "`date_pay_time`")
    private String datePayTime;

    /**
     * 自动扣款标识 1-自动扣款 0-不扣 （api渠道不需要自动发起）
     */
    @TableField(value = "`auto_repay`")
    private Integer autoRepay;

    /**
     * 创建时间
     */
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 分转资方还款计划编号
     */
    @TableField(value = "`plan_id`")
    private String planId;

    /**
     * 分转资方账单id
     */
    @TableField(value = "`bill_id`")
    private String billId;

    /**
     * 代偿标识 0 非代偿 1 代偿 2 回购
     */
    @TableField(value = "`compensation_status`")
    private Integer compensationStatus;
}