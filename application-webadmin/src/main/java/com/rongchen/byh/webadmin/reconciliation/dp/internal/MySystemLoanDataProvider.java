package com.rongchen.byh.webadmin.reconciliation.dp.internal;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rongchen.byh.webadmin.reconciliation.context.ReconciliationContext;
import com.rongchen.byh.webadmin.reconciliation.dataProvider.DataProvider;
import com.rongchen.byh.webadmin.reconciliation.model.NormalizedLoanRecord;
import com.rongchen.byh.webadmin.upms.dao.DisburseDataMapper;
import com.rongchen.byh.webadmin.upms.model.DisburseData;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 我方系统借款数据提供者。
 */
@Component
public class MySystemLoanDataProvider implements DataProvider {
    private static final Logger logger = LoggerFactory.getLogger(MySystemLoanDataProvider.class);
    // 我方系统日期格式，如果与XHY不同，需要单独定义
    private static final DateTimeFormatter OUR_SYSTEM_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter OUR_SYSTEM_DATETIME_FORMATTER = DateTimeFormatter
            .ofPattern("yyyy-MM-dd HH:mm:ss");

    private final DisburseDataMapper disburseDataMapper;

    @Autowired
    public MySystemLoanDataProvider(DisburseDataMapper disburseDataMapper) {
        this.disburseDataMapper = disburseDataMapper;

    }

    @Override
    public List<Map<String, Object>> loadData(ReconciliationContext context) throws Exception {
        logger.warn("MySystemLoanDataProvider.loadData()被调用，但推荐使用loadNormalizedData()获取我方借款数据。");
        throw new UnsupportedOperationException("请使用 loadNormalizedData() 方法获取我方系统的标准化借款数据。");
    }

    @Override
    public List<NormalizedLoanRecord> loadNormalizedData(ReconciliationContext context) throws Exception {
        logger.info("开始从我方系统加载并转换借款数据 (DisburseData)，处理日期: {}", context.getProcessingDate());

        LambdaQueryWrapper<DisburseData> queryWrapper = new LambdaQueryWrapper<DisburseData>()
               //大于等于500 还款中   600 已结清
               .ge(DisburseData::getCreditStatus, 500) // 仅加载借款数据
                .eq(DisburseData::getLoanTime, context.getProcessingDate())
                .orderByAsc(DisburseData::getLoanTime); // 示例排序

        List<DisburseData> disburseEntities = disburseDataMapper.selectList(queryWrapper);

        if (CollectionUtils.isEmpty(disburseEntities)) {
            logger.info("在我方系统未找到处理日期 {} 的借款数据 (DisburseData)。", context.getProcessingDate());
            return new ArrayList<>();
        }
        logger.info("从我方系统加载了 {} 条借款数据 (DisburseData)。", disburseEntities.size());

        return disburseEntities.stream()
                .map(entity -> convertToNormalized(entity, context))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private NormalizedLoanRecord convertToNormalized(DisburseData entity, ReconciliationContext context) {
        try {
            NormalizedLoanRecord norm = new NormalizedLoanRecord();

            // --- 手动映射关键字段 ---
            //  DisburseData 中的字段直接或间接对应 NormalizedLoanRecord
            norm.setPartnerLoanOrderNo(entity.getLoanNo()); // 我方借款单号字段为 loanOrderNo
            norm.setSubmissionDate(parseOurDate(entity.getCreateTime())); // 申请提交日期字段和格式
            norm.setFundingDate(parseOurDate(entity.getLoanTime())); // 放款成功日期字段和格式
            norm.setMaturityDate(null); // 到期日字段和格式
            norm.setPrincipalAmount(entity.getCreditAmount()); // 本金金额字段
            norm.setTermCount(entity.getPeriods()); // 期数字段
            norm.setInterestRateStr(entity.getYearRete()); // 利率字段
            norm.setRepaymentTypeCode(entity.getRepaymentMethod()); // 还款方式代码字段
            norm.setFundingProviderCode(entity.getFundCode()); // 资金方代码字段
          
            // 设置NormalizedTransaction接口字段
            norm.setSourceChannel(getChannelCode()); // MYSYSTEM
            norm.setOriginalRecordId(entity.getId() != null ? String.valueOf(entity.getId()) : null);
            norm.setAdditionalData(new HashMap<>());
            // TODO: 将DisburseData中其他未直接映射的有用字段放入additionalData
            // norm.getAdditionalData().put("ourSpecificField",
            // entity.getOurSpecificField());

            return norm;
        } catch (Exception e) {
            logger.error("将DisburseData实体 (ID: {}) 转换为NormalizedLoanRecord失败: {}", entity.getId(), e.getMessage(), e);
            return null;
        }
    }

    // 辅助方法：将 Date 对象转换为 LocalDate
    private LocalDate parseOurDate(Date date) {
        if (date == null) {
            return null;
        }
        try {
            // 直接将 Date 转换为 LocalDate，避免字符串解析的复杂性
            return date.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();
        } catch (Exception e) {
            logger.warn("将 Date 对象 '{}' 转换为 LocalDate 失败: {}", date, e.getMessage());
            return null;
        }
    }

    // 可选: 解析我方系统中的日期时间字符串 (如果需要)
    private LocalDateTime parseOurDateTime(String dateTimeStr) {
        if (!StringUtils.hasText(dateTimeStr))
            return null;
        try {
            return LocalDateTime.parse(dateTimeStr, OUR_SYSTEM_DATETIME_FORMATTER);
        } catch (DateTimeParseException e) {
            logger.warn("解析我方日期时间字符串 '{}' (格式: yyyy-MM-dd HH:mm:ss) 失败: {}", dateTimeStr, e.getMessage());
            return null;
        }
    }

    @Override
    public String getChannelCode() {
        return "MYSYSTEM";
    }

    @Override
    public String getTransactionType() {
        return "LOAN";
    }

    @Override
    public String getDataSourceType() {
        return "OUR_SIDE";
    }
}