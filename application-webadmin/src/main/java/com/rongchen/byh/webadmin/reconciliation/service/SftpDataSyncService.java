package com.rongchen.byh.webadmin.reconciliation.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rongchen.byh.webadmin.reconciliation.context.ReconciliationContext;
import com.rongchen.byh.webadmin.reconciliation.dataProvider.DataProvider;
import com.rongchen.byh.webadmin.reconciliation.dataProvider.DataProviderFactory;
import com.rongchen.byh.webadmin.reconciliation.util.BatchIdGenerator;
import com.rongchen.byh.webadmin.upms.model.DzBatchExecutionLogEntity;
import com.rongchen.byh.webadmin.upms.model.DzChannelReconConfigEntity;
import com.rongchen.byh.webadmin.upms.service.DzBatchExecutionLogService;
import com.rongchen.byh.webadmin.upms.service.DzChannelReconConfigService;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * SFTP数据同步服务。
 * <p>
 * 提供灵活的SFTP数据同步功能，支持：
 * 1. 按指定日期进行数据同步
 * 2. 按渠道和交易类型进行过滤同步
 * 3. 重新同步已有数据
 * 4. 同步状态查询和管理
 */
@Service
public class SftpDataSyncService {

    private static final Logger logger = LoggerFactory.getLogger(SftpDataSyncService.class);

    private final DataProviderFactory dataProviderFactory;
    private final BatchIdGenerator batchIdGenerator;
    private final DzBatchExecutionLogService dzBatchLogService;
    private final RawDataStorageService rawDataStorageService;
    private final DzChannelReconConfigService dzChannelReconConfigService;

    @Autowired
    public SftpDataSyncService(DataProviderFactory dataProviderFactory,
            BatchIdGenerator batchIdGenerator,
            DzBatchExecutionLogService dzBatchLogService,
            RawDataStorageService rawDataStorageService,
            DzChannelReconConfigService dzChannelReconConfigService) {
        this.dataProviderFactory = dataProviderFactory;
        this.batchIdGenerator = batchIdGenerator;
        this.dzBatchLogService = dzBatchLogService;
        this.rawDataStorageService = rawDataStorageService;
        this.dzChannelReconConfigService = dzChannelReconConfigService;
    }

    /**
     * 为指定日期同步所有启用的SFTP数据。
     *
     * @param processingDate 要同步的业务日期
     * @return 同步结果摘要
     */
    public SyncResult syncDataForDate(LocalDate processingDate) {
        logger.info("开始为日期 {} 执行SFTP数据同步...", processingDate);

        List<DzChannelReconConfigEntity> syncConfigs = getEnabledSftpConfigs();
        if (syncConfigs.isEmpty()) {
            logger.info("日期 {} 没有找到需要执行SFTP同步的渠道配置。", processingDate);
            return SyncResult.empty(processingDate);
        }

        logger.info("日期 {} 发现 {} 条SFTP同步配置项。", processingDate, syncConfigs.size());
        return processSyncConfigs(processingDate, syncConfigs);
    }

    /**
     * 为指定日期和指定渠道/交易类型同步SFTP数据。
     *
     * @param processingDate  要同步的业务日期
     * @param channelCode     渠道编码（可选，为null时同步所有渠道）
     * @param transactionType 交易类型（可选，LOAN, REPAYMENT, CREDIT，为null时同步所有类型）
     * @return 同步结果摘要
     */
    public SyncResult syncDataForDateAndChannel(LocalDate processingDate, String channelCode, String transactionType) {
        logger.info("开始为日期 {} 执行指定条件的SFTP数据同步，渠道: {}, 交易类型: {}",
                processingDate, channelCode, transactionType);

        List<DzChannelReconConfigEntity> syncConfigs = getFilteredSftpConfigs(channelCode, transactionType);
        if (syncConfigs.isEmpty()) {
            logger.info("日期 {} 没有找到符合条件的SFTP同步配置。渠道: {}, 交易类型: {}",
                    processingDate, channelCode, transactionType);
            return SyncResult.empty(processingDate);
        }

        logger.info("日期 {} 发现 {} 条符合条件的SFTP同步配置项。", processingDate, syncConfigs.size());
        return processSyncConfigs(processingDate, syncConfigs);
    }

    /**
     * 重新同步指定日期的数据。
     *
     * @param processingDate 要重新同步的业务日期
     * @param forceResync    是否强制重新同步（即使已有成功记录）
     * @return 同步结果摘要
     */
    public SyncResult resyncDataForDate(LocalDate processingDate, boolean forceResync) {
        logger.info("开始为日期 {} 执行重新同步，强制模式: {}", processingDate, forceResync);

        List<DzChannelReconConfigEntity> syncConfigs = getEnabledSftpConfigs();
        if (syncConfigs.isEmpty()) {
            logger.info("日期 {} 没有找到需要执行重新同步的渠道配置。", processingDate);
            return SyncResult.empty(processingDate);
        }

        // 如果不是强制模式，过滤掉已经成功同步的配置
        if (!forceResync) {
            syncConfigs = filterUnprocessedConfigs(syncConfigs, processingDate);
            logger.info("日期 {} 过滤后剩余 {} 条需要重新同步的配置项。", processingDate, syncConfigs.size());
        }

        return processSyncConfigs(processingDate, syncConfigs);
    }

    /**
     * 获取所有启用SFTP同步的配置。
     */
    private List<DzChannelReconConfigEntity> getEnabledSftpConfigs() {
        return dzChannelReconConfigService.list(
                new LambdaQueryWrapper<DzChannelReconConfigEntity>()
                        .eq(DzChannelReconConfigEntity::getSftpSyncEnabled, true));
    }

    /**
     * 获取符合条件的SFTP同步配置。
     */
    private List<DzChannelReconConfigEntity> getFilteredSftpConfigs(String channelCode, String transactionType) {
        LambdaQueryWrapper<DzChannelReconConfigEntity> queryWrapper = new LambdaQueryWrapper<DzChannelReconConfigEntity>()
                .eq(DzChannelReconConfigEntity::getSftpSyncEnabled, true);

        if (StringUtils.hasText(channelCode)) {
            queryWrapper.eq(DzChannelReconConfigEntity::getChannelCode, channelCode);
        }

        if (StringUtils.hasText(transactionType)) {
            queryWrapper.eq(DzChannelReconConfigEntity::getTransactionType, transactionType);
        }

        return dzChannelReconConfigService.list(queryWrapper);
    }

    /**
     * 过滤掉已经成功处理的配置项。
     */
    private List<DzChannelReconConfigEntity> filterUnprocessedConfigs(
            List<DzChannelReconConfigEntity> configs, LocalDate processingDate) {
        // 这里可以查询批次执行日志，过滤掉已经成功处理的配置
        // 为简化起见，当前返回所有配置，实际项目中可以根据需要实现
        return configs;
    }

    /**
     * 处理同步配置列表。
     */
    private SyncResult processSyncConfigs(LocalDate processingDate, List<DzChannelReconConfigEntity> syncConfigs) {
        SyncResult result = new SyncResult(processingDate);

        for (DzChannelReconConfigEntity config : syncConfigs) {
            try {
                SyncTaskResult taskResult = processSingleConfig(processingDate, config);
                result.addTaskResult(taskResult);
            } catch (Exception e) {
                logger.error("处理配置时发生异常：渠道[{}], 类型[{}], 错误: {}",
                        config.getChannelCode(), config.getTransactionType(), e.getMessage(), e);
                result.addTaskResult(SyncTaskResult.failed(config, e.getMessage()));
            }
        }

        logger.info("日期 {} SFTP数据同步完成。成功: {}, 失败: {}, 跳过: {}",
                processingDate, result.getSuccessCount(), result.getFailureCount(), result.getSkippedCount());

        return result;
    }

    /**
     * 处理单个同步配置。
     */
    private SyncTaskResult processSingleConfig(LocalDate processingDate, DzChannelReconConfigEntity config) {
        String channelCode = config.getChannelCode();
        String transactionType = config.getTransactionType();
        String dataSourceType = config.getDataSourceType() != null ? config.getDataSourceType() : "PARTNER_SIDE";

        // 验证数据源类型
        if (!"PARTNER_SIDE".equalsIgnoreCase(dataSourceType)) {
            logger.warn("SFTP同步配置跳过：渠道[{}], 类型[{}], data_source_type不是PARTNER_SIDE (实际为: {})",
                    channelCode, transactionType, dataSourceType);
            return SyncTaskResult.skipped(config, "数据源类型不是PARTNER_SIDE");
        }

        String syncBatchId = batchIdGenerator.generateBatchId(processingDate, channelCode,
                transactionType );
        logger.info("为渠道[{}], 类型[{}], 源[{}] 生成SFTP同步批次ID: {}",
                channelCode, transactionType, dataSourceType, syncBatchId);

        DzBatchExecutionLogEntity syncLog = startSyncBatchLog(syncBatchId, processingDate, channelCode,
                transactionType);

        try {
            ReconciliationContext context = ReconciliationContext.builder()
                    .processingDate(processingDate)
                    .channelCode(channelCode)
                    .transactionType(transactionType)
                    .batchId(syncBatchId)
                    .build();

            DataProvider dataProvider = dataProviderFactory.getDataProvider(channelCode, transactionType,
                    dataSourceType);
            logger.info("获取到DataProvider: {} 用于同步 {}/{} 数据",
                    dataProvider.getClass().getSimpleName(), channelCode, transactionType);

            List<Map<String, Object>> rawData = dataProvider.loadData(context);
            logger.info("从SFTP成功下载并解析了 {} 条 {}/{} 的原始数据。批次ID: {}",
                    rawData.size(), channelCode, transactionType, syncBatchId);

            if (!rawData.isEmpty()) {
                rawDataStorageService.storeRawData(syncBatchId, channelCode, transactionType, processingDate,
                        rawData);
            } else {
                logger.info("渠道[{}], 类型[{}] 无原始数据下载，无需存储。批次ID: {}", channelCode, transactionType, syncBatchId);
            }

            completeSyncBatchLog(syncLog, "COMPLETED", rawData.size(), null);
            return SyncTaskResult.success(config, syncBatchId, rawData.size());

        } catch (Exception e) {
            logger.error("SFTP数据同步失败。渠道[{}], 类型[{}], 源[{}], 批次ID: {}. 错误: {}",
                    channelCode, transactionType, dataSourceType, syncBatchId, e.getMessage(), e);
            if (syncLog != null) {
                completeSyncBatchLog(syncLog, "FAILED", -1, e.getMessage());
            }
            return SyncTaskResult.failed(config, e.getMessage());
        }
    }

    /**
     * 开始同步批次日志记录。
     */
    private DzBatchExecutionLogEntity startSyncBatchLog(String batchId, LocalDate processingDate, String channelCode,
            String taskType) {
        try {
            DzBatchExecutionLogEntity logEntry = new DzBatchExecutionLogEntity();
            logEntry.setBatchId(batchId);
            if (processingDate != null) {
                logEntry.setProcessingDate(java.sql.Date.valueOf(processingDate));
            }
            logEntry.setChannelCode(channelCode);
            logEntry.setTransactionType(taskType);
            logEntry.setStartTime(java.sql.Timestamp.valueOf(LocalDateTime.now()));
            logEntry.setStatus("PROCESSING");
            boolean saved = dzBatchLogService.save(logEntry);
            if (!saved) {
                logger.error("保存SFTP同步初始批次日志失败, BatchID: {}", batchId);
                return null;
            }
            return logEntry;
        } catch (Exception e) {
            logger.error("记录SFTP同步批次开始日志失败, BatchID: {} : {}", batchId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 完成同步批次日志记录。
     */
    private void completeSyncBatchLog(DzBatchExecutionLogEntity batchLog, String status, int recordCount,
            String errorMessage) {
        if (batchLog == null || batchLog.getId() == null) {
            logger.warn("无法更新SFTP同步批次日志：传入的batchLog对象为null或没有ID。");
            return;
        }
        try {
            batchLog.setEndTime(java.sql.Timestamp.valueOf(LocalDateTime.now()));
            batchLog.setStatus(status);
            if (recordCount >= 0) {
                batchLog.setTotalPartnerRecords(recordCount);
            }
            if (errorMessage != null) {
                batchLog.setErrorMessage(errorMessage.substring(0, Math.min(errorMessage.length(), 2000)));
            }
            boolean updated = dzBatchLogService.updateById(batchLog);
            if (!updated) {
                logger.error("更新SFTP同步最终批次日志失败, BatchID: {}", batchLog.getBatchId());
            }
        } catch (Exception e) {
            logger.error("记录SFTP同步批次结束日志失败, BatchID: {} : {}", batchLog.getBatchId(), e.getMessage(), e);
        }
    }

    /**
     * 同步状态枚举。
     */
    public enum SyncStatus {
        SUCCESS, FAILURE, SKIPPED
    }

    /**
     * 同步结果封装类。
     */
    @Getter
    public static class SyncResult {
        private final LocalDate processingDate;
        private final List<SyncTaskResult> taskResults;

        public SyncResult(LocalDate processingDate) {
            this.processingDate = processingDate;
            this.taskResults = new java.util.ArrayList<>();
        }

        public static SyncResult empty(LocalDate processingDate) {
            return new SyncResult(processingDate);
        }

        public void addTaskResult(SyncTaskResult taskResult) {
            this.taskResults.add(taskResult);
        }

        public long getSuccessCount() {
            return taskResults.stream().filter(SyncTaskResult::isSuccess).count();
        }

        public long getFailureCount() {
            return taskResults.stream().filter(SyncTaskResult::isFailure).count();
        }

        public long getSkippedCount() {
            return taskResults.stream().filter(SyncTaskResult::isSkipped).count();
        }

        public boolean hasFailures() {
            return getFailureCount() > 0;
        }

        public List<SyncTaskResult> getFailedTasks() {
            return taskResults.stream()
                    .filter(SyncTaskResult::isFailure)
                    .collect(Collectors.toList());
        }
    }

    /**
     * 单个同步任务结果。
     */
    public static class SyncTaskResult {
        private final DzChannelReconConfigEntity config;
        private final SyncStatus status;
        private final String batchId;
        private final int recordCount;
        private final String message;

        private SyncTaskResult(DzChannelReconConfigEntity config, SyncStatus status, String batchId,
                int recordCount, String message) {
            this.config = config;
            this.status = status;
            this.batchId = batchId;
            this.recordCount = recordCount;
            this.message = message;
        }

        public static SyncTaskResult success(DzChannelReconConfigEntity config, String batchId, int recordCount) {
            return new SyncTaskResult(config, SyncStatus.SUCCESS, batchId, recordCount, null);
        }

        public static SyncTaskResult failed(DzChannelReconConfigEntity config, String errorMessage) {
            return new SyncTaskResult(config, SyncStatus.FAILURE, null, 0, errorMessage);
        }

        public static SyncTaskResult skipped(DzChannelReconConfigEntity config, String reason) {
            return new SyncTaskResult(config, SyncStatus.SKIPPED, null, 0, reason);
        }

        // Getters
        public DzChannelReconConfigEntity getConfig() {
            return config;
        }

        public SyncStatus getStatus() {
            return status;
        }

        public String getBatchId() {
            return batchId;
        }

        public int getRecordCount() {
            return recordCount;
        }

        public String getMessage() {
            return message;
        }

        public boolean isSuccess() {
            return status == SyncStatus.SUCCESS;
        }

        public boolean isFailure() {
            return status == SyncStatus.FAILURE;
        }

        public boolean isSkipped() {
            return status == SyncStatus.SKIPPED;
        }
    }
}