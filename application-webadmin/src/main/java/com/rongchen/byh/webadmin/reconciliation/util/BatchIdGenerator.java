package com.rongchen.byh.webadmin.reconciliation.util;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.StrUtil;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 对账批次ID生成器。
 */
@Component
public class BatchIdGenerator {

    private static final DateTimeFormatter DATE_PREFIX_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final DateTimeFormatter MONTH_PREFIX_FORMATTER = DateTimeFormatter.ofPattern("yyyyMM");

    // 批次ID格式相关常量
    private static final String BATCH_ID_PREFIX = "BATCH";
    private static final String SEPARATOR = "_";
    private static final int CHANNEL_CODE_MAX_LENGTH = 10;
    private static final int TRANSACTION_TYPE_MAX_LENGTH = 10;
    private static final int SHORT_UUID_LENGTH = 8;

    /**
     * 生成一个唯一的对账批次ID。
     * <p>
     * ID格式: BATCH_yyyyMMdd_CH_TYPE_ShortUUID
     * <p>
     * 格式说明:
     * - BATCH: 固定前缀，标识这是一个批次ID
     * - yyyyMMdd: 处理日期，如20250322
     * - CH: 渠道编码简写，如XHY
     * - TYPE: 交易类型简写，如LOAN、REPA
     * - ShortUUID: 8位随机UUID，保证唯一性
     * <p>
     * 示例: BATCH_20250322_XHY_LOAN_A1B2C3D4
     *
     * @param processingDate  对账的处理日期 (T日)，不能为空
     * @param channelCode     渠道编码，如果为空则使用"DEF"
     * @param transactionType 交易类型，如果为空则使用"UNKN"
     * @return 生成的批次ID字符串
     * @throws IllegalArgumentException 如果 processingDate 为空
     */
    public String generateBatchId(LocalDate processingDate, String channelCode, String transactionType) {
        // 参数验证
        if (processingDate == null) {
            throw new IllegalArgumentException("处理日期不能为空");
        }

        // 格式化日期
        String datePrefix = processingDate.format(DATE_PREFIX_FORMATTER);

        // 标准化渠道编码
        String standardizedChannelCode = standardizeChannelCode(channelCode);

        // 标准化交易类型
        String standardizedTransactionType = StrUtil.isBlank(transactionType) ? "UNKN" : transactionType.toUpperCase();

        // 生成短UUID
        String shortUuid = UUID.randomUUID().toString()
                .replace("-", "")
                .substring(0, SHORT_UUID_LENGTH)
                .toUpperCase();

        // 组装批次ID
        return String.format("%s%s%s%s%s%s%s%s%s",
                BATCH_ID_PREFIX, SEPARATOR,
                datePrefix, SEPARATOR,
                standardizedChannelCode, SEPARATOR,
                standardizedTransactionType, SEPARATOR,
                shortUuid);
    }

    /**
     * 生成一个月度对账批次ID。
     * ID格式: MONTHLY_YYYYMM_Channel_Type_Suffix_ShortUUID
     *
     * @param yearMonth       对账的年月。
     * @param channelCode     渠道编码 (如果为null或空，会用 "ALL_CHANNELS")。
     * @param transactionType 交易类型 (如果为null或空，会用 "ALL_TYPES")。
     * @param suffix          自定义后缀 (例如 "AUTO", "MANUAL_RETRY")。
     * @return 生成的月度批次ID字符串。
     */
    public String generateMonthlyBatchId(YearMonth yearMonth, String channelCode, String transactionType,
            String suffix) {
        if (yearMonth == null) {
            throw new IllegalArgumentException("YearMonth cannot be null for monthly batch ID generation.");
        }
        String monthPrefix = yearMonth.format(MONTH_PREFIX_FORMATTER);
        String chCode = StringUtils.hasText(channelCode) ? channelCode.toUpperCase() : "ALLCH";
        String txType = StringUtils.hasText(transactionType) ? transactionType.toUpperCase() : "ALLTX";
        String sfx = StringUtils.hasText(suffix) ? suffix.toUpperCase() : "DEFAULT";

        String shortUuid = UUID.randomUUID().toString().substring(0, 8).toUpperCase();

        return String.format("MONTHLY%s%s%s%s%s",
                monthPrefix,
                chCode,
                txType,
                sfx,
                shortUuid);
    }

    /**
     * 生成一个更简单的基于日期和序列的批次ID。
     * 格式: RECON_yyyyMMdd_xxxxx (xxxxx是5位序列号)
     *
     * 注意：此方法在分布式环境下可能存在冲突风险，建议优先使用 generateBatchId 方法。
     *
     * @param processingDate 对账的处理日期，不能为空
     * @return 批次ID
     * @throws IllegalArgumentException 如果 processingDate 为空
     */
    public String generateSimpleSequencedBatchId(LocalDate processingDate) {
        if (processingDate == null) {
            throw new IllegalArgumentException("处理日期不能为空");
        }

        String datePrefix = processingDate.format(DATE_PREFIX_FORMATTER);
        // 使用时间戳的后5位作为序列号，减少分布式环境下的冲突概率
        long timestamp = System.currentTimeMillis();
        String sequence = String.format("%05d", timestamp % 100000);

        return String.format("RECON%s%s%s", datePrefix, SEPARATOR, sequence);
    }

    /**
     * 标准化渠道编码。
     *
     * @param channelCode 原始渠道编码
     * @return 标准化后的渠道编码
     */
    private String standardizeChannelCode(String channelCode) {
        if (!StringUtils.hasText(channelCode)) {
            return "DEF"; // 默认渠道编码
        }

        // 移除特殊字符，只保留字母和数字
        String cleaned = channelCode.replaceAll("[^A-Za-z0-9]", "").toUpperCase();

        // 截取前3位
        if (cleaned.length() > CHANNEL_CODE_MAX_LENGTH) {
            return cleaned.substring(0, CHANNEL_CODE_MAX_LENGTH);
        }

        // 如果长度不足3位，用原值
        return cleaned.isEmpty() ? "DEF" : cleaned;
    }


}