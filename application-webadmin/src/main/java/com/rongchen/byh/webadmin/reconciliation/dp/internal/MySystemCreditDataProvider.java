package com.rongchen.byh.webadmin.reconciliation.dp.internal;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rongchen.byh.webadmin.reconciliation.context.ReconciliationContext;
import com.rongchen.byh.webadmin.reconciliation.dataProvider.DataProvider;
import com.rongchen.byh.webadmin.reconciliation.model.NormalizedCreditRecord;
import com.rongchen.byh.webadmin.upms.model.DisburseData;
import com.rongchen.byh.webadmin.upms.service.DisburseDataService;
import com.rongchen.byh.webadmin.upms.dto.CreditDataWithApplyInfo;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 我方系统授信数据提供者。
 */
@Component
@Slf4j
public class MySystemCreditDataProvider implements DataProvider {
    private static final Logger logger = LoggerFactory.getLogger(MySystemCreditDataProvider.class);
    private static final DateTimeFormatter OUR_SYSTEM_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private final DisburseDataService disburseDataService;

    @Autowired
    public MySystemCreditDataProvider(DisburseDataService disburseDataService) {
        this.disburseDataService = disburseDataService;
        logger.info("MySystemCreditDataProvider 初始化完成，已配置 DisburseDataService");
    }

    @Override
    public List<Map<String, Object>> loadData(ReconciliationContext context) throws Exception {
        throw new UnsupportedOperationException("请使用 loadNormalizedData() 方法获取我方系统的标准化授信数据。");
    }

    @Override
    public List<NormalizedCreditRecord> loadNormalizedData(ReconciliationContext context) throws Exception {
        logger.info("开始从我方系统加载并转换授信数据（disburse_data表），处理日期: {}", context.getProcessingDate());

        try {
            // 计算查询日期范围（处理日期当天）
            LocalDate processingDate = context.getProcessingDate();
            Date startDate = Date.from(processingDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            Date endDate = Date.from(processingDate.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());

            // 查询我方授信数据：credit_status = 500（还款中）
            LambdaQueryWrapper<DisburseData> queryWrapper = new LambdaQueryWrapper<DisburseData>()
                    .ge(DisburseData::getCreditStatus, 500)            // 还款中状态
                    .ge(DisburseData::getCreateTime, startDate)        // 创建时间 >= 处理日期开始
                    .lt(DisburseData::getCreateTime, endDate)          // 创建时间 < 处理日期+1天开始
                    .orderByAsc(DisburseData::getCreateTime);          // 按创建时间升序

            List<DisburseData> disburseDataList = disburseDataService.list(queryWrapper);

            if (CollectionUtils.isEmpty(disburseDataList)) {
                logger.info("在我方系统未找到处理日期 {} 的还款中状态数据。", context.getProcessingDate());
                return new ArrayList<>();
            }
            logger.info("从我方系统加载了 {} 条还款中状态数据。", disburseDataList.size());

            // 转换为标准化模型
            return disburseDataList.stream()
                    .map(entity -> convertToNormalized(entity, context))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            logger.error("从我方系统加载授信数据时发生错误: {}", e.getMessage(), e);
            throw new Exception("加载我方系统授信数据失败", e);
        }
    }

    private NormalizedCreditRecord convertToNormalized(DisburseData disburseData, ReconciliationContext context) {
        try {
            NormalizedCreditRecord norm = new NormalizedCreditRecord();

            // --- 映射关键字段 ---
            // 授信单号：使用授信流水号作为授信单号
            norm.setPartnerCreditNo(disburseData.getCreditNo());

            // 授信状态：将我方授信状态转换为标准状态
            // 我方：100-授信中, 200-授信失败, 300-放款中, 400-放款失败, 500-还款中, 600-已结清
            // 标准：使用字符串表示，"500"表示还款中状态
            norm.setCreditStatus(String.valueOf(disburseData.getCreditStatus()));

            // 授信日期：使用授信通过时间作为授信日期，如果没有则使用创建时间
            LocalDate creditDate = disburseData.getCreditTime() != null
                ? convertDateToLocalDate(disburseData.getCreditTime())
                : convertDateToLocalDate(disburseData.getCreateTime());
            norm.setCreditDate(creditDate);

            // 授信金额：使用放款金额
            norm.setCreditAmount(disburseData.getCreditAmount() != null ? disburseData.getCreditAmount() : BigDecimal.ZERO);

            // 通用状态：使用授信状态的字符串形式
            norm.setStatus(String.valueOf(disburseData.getCreditStatus()));

            // --- 设置 NormalizedTransaction 接口字段 ---
            norm.setSourceChannel(getChannelCode()); // MYSYSTEM
            norm.setOriginalRecordId(String.valueOf(disburseData.getId()));

            // 初始化附加数据并存储其他有用信息
            Map<String, Object> additionalData = new HashMap<>();
            additionalData.put("userId", disburseData.getUserId());
            additionalData.put("productId", disburseData.getProductId());
            additionalData.put("creditNo", disburseData.getCreditNo());
            additionalData.put("loanNo", disburseData.getLoanNo());
            additionalData.put("saleNo", disburseData.getSaleNo());
            additionalData.put("creditStatus", disburseData.getCreditStatus());
            additionalData.put("creditTime", disburseData.getCreditTime());
            additionalData.put("loanTime", disburseData.getLoanTime());
            additionalData.put("repaymentTime", disburseData.getRepaymentTime());
            additionalData.put("periods", disburseData.getPeriods());
            additionalData.put("purposeLoan", disburseData.getPurposeLoan());
            additionalData.put("yearRete", disburseData.getYearRete());
            additionalData.put("repaymentMethod", disburseData.getRepaymentMethod());
            additionalData.put("createTime", disburseData.getCreateTime());
            additionalData.put("capitalId", disburseData.getCapitalId());
            additionalData.put("fundCode", disburseData.getFundCode());
            norm.setAdditionalData(additionalData);

            logger.debug("成功转换我方授信记录: id={}, creditStatus={}, userId={}",
                disburseData.getId(), disburseData.getCreditStatus(), disburseData.getUserId());

            return norm;
        } catch (Exception e) {
            logger.error("将我方授信实体 (ID: {}, userId: {}) 转换为NormalizedCreditRecord失败: {}",
                disburseData.getId(), disburseData.getUserId(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 将 Date 对象转换为 LocalDate
     */
    private LocalDate convertDateToLocalDate(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    /**
     * 解析我方系统的日期字符串（保留原有方法以备用）
     */
    private LocalDate parseOurDate(String dateStr) {
        if (!StringUtils.hasText(dateStr))
            return null;
        try {
            return LocalDate.parse(dateStr, OUR_SYSTEM_DATE_FORMATTER);
        } catch (DateTimeParseException e) {
            logger.warn("解析我方日期字符串 '{}' (格式: yyyy-MM-dd) 失败: {}", dateStr, e.getMessage());
            return null;
        }
    }

    @Override
    public String getChannelCode() {
        return "MYSYSTEM";
    }

    @Override
    public String getTransactionType() {
        return "CREDIT";
    }

    @Override
    public String getDataSourceType() {
        return "OUR_SIDE";
    }
}