<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.webadmin.upms.dao.DzReconciliationSummaryMapper">
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.rongchen.byh.webadmin.upms.model.DzReconciliationSummaryEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `dz_reconciliation_summary`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="reconBatchId != null">
        `recon_batch_id`,
      </if>
      <if test="processingDate != null">
        `processing_date`,
      </if>
      <if test="channelCode != null">
        `channel_code`,
      </if>
      <if test="transactionType != null">
        `transaction_type`,
      </if>
      <if test="ourRecordsCount != null">
        `our_records_count`,
      </if>
      <if test="partnerRecordsCount != null">
        `partner_records_count`,
      </if>
      <if test="ourTotalAmount != null">
        `our_total_amount`,
      </if>
      <if test="partnerTotalAmount != null">
        `partner_total_amount`,
      </if>
      <if test="diffRecordsCount != null">
        `diff_records_count`,
      </if>
      <if test="reconStatus != null">
        `recon_status`,
      </if>
      <if test="summaryTime != null">
        `summary_time`,
      </if>
      <if test="remarks != null">
        `remarks`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
      <if test="updateTime != null">
        `update_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="reconBatchId != null">
        #{reconBatchId,jdbcType=VARCHAR},
      </if>
      <if test="processingDate != null">
        #{processingDate,jdbcType=DATE},
      </if>
      <if test="channelCode != null">
        #{channelCode,jdbcType=VARCHAR},
      </if>
      <if test="transactionType != null">
        #{transactionType,jdbcType=VARCHAR},
      </if>
      <if test="ourRecordsCount != null">
        #{ourRecordsCount,jdbcType=INTEGER},
      </if>
      <if test="partnerRecordsCount != null">
        #{partnerRecordsCount,jdbcType=INTEGER},
      </if>
      <if test="ourTotalAmount != null">
        #{ourTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="partnerTotalAmount != null">
        #{partnerTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="diffRecordsCount != null">
        #{diffRecordsCount,jdbcType=INTEGER},
      </if>
      <if test="reconStatus != null">
        #{reconStatus,jdbcType=VARCHAR},
      </if>
      <if test="summaryTime != null">
        #{summaryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remarks != null">
        #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `dz_reconciliation_summary`
    (`recon_batch_id`, `processing_date`, `channel_code`, `transaction_type`, `our_records_count`,
      `partner_records_count`, `our_total_amount`, `partner_total_amount`, `diff_records_count`,
      `recon_status`, `summary_time`, `remarks`, `create_time`, `update_time`)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.reconBatchId,jdbcType=VARCHAR}, #{item.processingDate,jdbcType=DATE}, #{item.channelCode,jdbcType=VARCHAR},
        #{item.transactionType,jdbcType=VARCHAR}, #{item.ourRecordsCount,jdbcType=INTEGER},
        #{item.partnerRecordsCount,jdbcType=INTEGER}, #{item.ourTotalAmount,jdbcType=DECIMAL},
        #{item.partnerTotalAmount,jdbcType=DECIMAL}, #{item.diffRecordsCount,jdbcType=INTEGER},
        #{item.reconStatus,jdbcType=VARCHAR}, #{item.summaryTime,jdbcType=TIMESTAMP}, #{item.remarks,jdbcType=VARCHAR},
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertOrUpdate" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `dz_reconciliation_summary`
    (`recon_batch_id`, `processing_date`, `channel_code`, `transaction_type`, `our_records_count`,
      `partner_records_count`, `our_total_amount`, `partner_total_amount`, `diff_records_count`,
      `recon_status`, `summary_time`, `remarks`, `create_time`, `update_time`)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.reconBatchId,jdbcType=VARCHAR}, #{item.processingDate,jdbcType=DATE}, #{item.channelCode,jdbcType=VARCHAR},
        #{item.transactionType,jdbcType=VARCHAR}, #{item.ourRecordsCount,jdbcType=INTEGER},
        #{item.partnerRecordsCount,jdbcType=INTEGER}, #{item.ourTotalAmount,jdbcType=DECIMAL},
        #{item.partnerTotalAmount,jdbcType=DECIMAL}, #{item.diffRecordsCount,jdbcType=INTEGER},
        #{item.reconStatus,jdbcType=VARCHAR}, #{item.summaryTime,jdbcType=TIMESTAMP}, #{item.remarks,jdbcType=VARCHAR},
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
    on duplicate key update
    recon_batch_id=values(recon_batch_id),
    processing_date=values(processing_date),
    channel_code=values(channel_code),
    transaction_type=values(transaction_type),
    our_records_count=values(our_records_count),
    partner_records_count=values(partner_records_count),
    our_total_amount=values(our_total_amount),
    partner_total_amount=values(partner_total_amount),
    diff_records_count=values(diff_records_count),
    recon_status=values(recon_status),
    summary_time=values(summary_time),
    remarks=values(remarks),
    create_time=values(create_time),
    update_time=values(update_time)
  </insert>
  <resultMap id="BaseResultMap" type="com.rongchen.byh.webadmin.upms.model.DzReconciliationSummaryEntity">
    <!--@mbg.generated-->
    <!--@Table `dz_reconciliation_summary`-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="recon_batch_id" jdbcType="VARCHAR" property="reconBatchId" />
    <result column="processing_date" jdbcType="DATE" property="processingDate" />
    <result column="channel_code" jdbcType="VARCHAR" property="channelCode" />
    <result column="transaction_type" jdbcType="VARCHAR" property="transactionType" />
    <result column="our_records_count" jdbcType="INTEGER" property="ourRecordsCount" />
    <result column="partner_records_count" jdbcType="INTEGER" property="partnerRecordsCount" />
    <result column="our_total_amount" jdbcType="DECIMAL" property="ourTotalAmount" />
    <result column="partner_total_amount" jdbcType="DECIMAL" property="partnerTotalAmount" />
    <result column="diff_records_count" jdbcType="INTEGER" property="diffRecordsCount" />
    <result column="recon_status" jdbcType="VARCHAR" property="reconStatus" />
    <result column="summary_time" jdbcType="TIMESTAMP" property="summaryTime" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `id`, `recon_batch_id`, `processing_date`, `channel_code`, `transaction_type`, `our_records_count`,
    `partner_records_count`, `our_total_amount`, `partner_total_amount`, `diff_records_count`,
    `recon_status`, `summary_time`, `remarks`, `create_time`, `update_time`
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update `dz_reconciliation_summary`
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`recon_batch_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.reconBatchId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`processing_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.processingDate,jdbcType=DATE}
        </foreach>
      </trim>
      <trim prefix="`channel_code` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.channelCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`transaction_type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.transactionType,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`our_records_count` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.ourRecordsCount,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="`partner_records_count` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.partnerRecordsCount,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="`our_total_amount` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.ourTotalAmount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="`partner_total_amount` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.partnerTotalAmount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="`diff_records_count` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.diffRecordsCount,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="`recon_status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.reconStatus,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`summary_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.summaryTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="`remarks` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.remarks,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`create_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="`update_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
    </trim>
    where `id` in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update `dz_reconciliation_summary`
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
    where `id` in
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`recon_batch_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.reconBatchId != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.reconBatchId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`processing_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.processingDate != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.processingDate,jdbcType=DATE}
          </if>
        </foreach>
      </trim>
      <trim prefix="`channel_code` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.channelCode != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.channelCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`transaction_type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.transactionType != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.transactionType,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`our_records_count` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.ourRecordsCount != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.ourRecordsCount,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="`partner_records_count` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.partnerRecordsCount != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.partnerRecordsCount,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="`our_total_amount` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.ourTotalAmount != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.ourTotalAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="`partner_total_amount` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.partnerTotalAmount != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.partnerTotalAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="`diff_records_count` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.diffRecordsCount != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.diffRecordsCount,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="`recon_status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.reconStatus != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.reconStatus,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`summary_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.summaryTime != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.summaryTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="`remarks` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.remarks != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.remarks,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`create_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="`update_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
    </trim>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.rongchen.byh.webadmin.upms.model.DzReconciliationSummaryEntity">
    <!--@mbg.generated-->
    update `dz_reconciliation_summary`
    <set>
      <if test="reconBatchId != null">
        `recon_batch_id` = #{reconBatchId,jdbcType=VARCHAR},
      </if>
      <if test="processingDate != null">
        `processing_date` = #{processingDate,jdbcType=DATE},
      </if>
      <if test="channelCode != null">
        `channel_code` = #{channelCode,jdbcType=VARCHAR},
      </if>
      <if test="transactionType != null">
        `transaction_type` = #{transactionType,jdbcType=VARCHAR},
      </if>
      <if test="ourRecordsCount != null">
        `our_records_count` = #{ourRecordsCount,jdbcType=INTEGER},
      </if>
      <if test="partnerRecordsCount != null">
        `partner_records_count` = #{partnerRecordsCount,jdbcType=INTEGER},
      </if>
      <if test="ourTotalAmount != null">
        `our_total_amount` = #{ourTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="partnerTotalAmount != null">
        `partner_total_amount` = #{partnerTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="diffRecordsCount != null">
        `diff_records_count` = #{diffRecordsCount,jdbcType=INTEGER},
      </if>
      <if test="reconStatus != null">
        `recon_status` = #{reconStatus,jdbcType=VARCHAR},
      </if>
      <if test="summaryTime != null">
        `summary_time` = #{summaryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remarks != null">
        `remarks` = #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        `update_time` = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.rongchen.byh.webadmin.upms.model.DzReconciliationSummaryEntity">
    <!--@mbg.generated-->
    update `dz_reconciliation_summary`
    set `recon_batch_id` = #{reconBatchId,jdbcType=VARCHAR},
      `processing_date` = #{processingDate,jdbcType=DATE},
      `channel_code` = #{channelCode,jdbcType=VARCHAR},
      `transaction_type` = #{transactionType,jdbcType=VARCHAR},
      `our_records_count` = #{ourRecordsCount,jdbcType=INTEGER},
      `partner_records_count` = #{partnerRecordsCount,jdbcType=INTEGER},
      `our_total_amount` = #{ourTotalAmount,jdbcType=DECIMAL},
      `partner_total_amount` = #{partnerTotalAmount,jdbcType=DECIMAL},
      `diff_records_count` = #{diffRecordsCount,jdbcType=INTEGER},
      `recon_status` = #{reconStatus,jdbcType=VARCHAR},
      `summary_time` = #{summaryTime,jdbcType=TIMESTAMP},
      `remarks` = #{remarks,jdbcType=VARCHAR},
      `create_time` = #{createTime,jdbcType=TIMESTAMP},
      `update_time` = #{updateTime,jdbcType=TIMESTAMP}
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectAll" resultType="com.rongchen.byh.webadmin.reconciliation.vo.ReconciliationSummaryListVo">
    select dz_reconciliation_summary.id,
           recon_batch_id                              as reconBatchId,
           processing_date                             as processingDate,
           transaction_type                            as transactionType,
           channel_data.name                           as channelName,
           our_records_count                           as ourRecordsCount,
           partner_records_count                       as partnerRecordsCount,
           our_total_amount                            as ourTotalAmount,
           partner_total_amount                        as partnerTotalAmount,
           recon_status                                as reconStatus,
           IF(recon_status = 'BALANCED', '已处理', '差错账') as reconStatusName,
           CASE transaction_type
             WHEN 'LOAN' THEN '放款'
             WHEN 'REPAYMENT' THEN '还款'
             WHEN 'CREDIT' THEN '授信'
             ELSE '其他'
             END                                       as transactionTypeName
    from dz_reconciliation_summary
           left join channel_data on dz_reconciliation_summary.channel_code = channel_data.secret
    <where>
      <if test="transactionType != null and transactionType != ''">
        and transaction_type = #{transactionType}
      </if>
      <if test="reconBatchId != null and reconBatchId != ''">
        and recon_batch_id = #{reconBatchId}
      </if>
      <if test="channelCode != null and channelCode != ''">
        and channel_code = #{channelCode}
      </if>
      <if test="reconStatus != null and reconStatus != ''">
        and recon_status = #{reconStatus}
      </if>
      <if test="processingBeginDate != null and processingEndDate != null">
        and processing_date between #{processingBeginDate}
          and #{processingEndDate}
      </if>
    </where>
  </select>
</mapper>