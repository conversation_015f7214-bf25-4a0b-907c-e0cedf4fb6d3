package com.rongchen.byh.webadmin.reconciliation.controller;

import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.webadmin.upms.model.DzFieldMappingRulesEntity;
import com.rongchen.byh.webadmin.upms.model.DzReconciliationRulesEntity;
import com.rongchen.byh.webadmin.upms.service.DzFieldMappingRulesService;
import com.rongchen.byh.webadmin.upms.service.DzReconciliationRulesService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 对账规则管理API控制器。
 * <p>
 * 提供用于管理字段映射规则 (field_mapping_rules) 和对账规则 (reconciliation_rules) 的接口。
 */
@RestController
@RequestMapping("/admin/reconciliation/rules") // 基础路径
public class RuleManagementController {

    private final DzFieldMappingRulesService fieldMappingRulesService;
    private final DzReconciliationRulesService reconciliationRulesService;

    @Autowired
    public RuleManagementController(DzFieldMappingRulesService fieldMappingRulesService,
            DzReconciliationRulesService reconciliationRulesService) {
        this.fieldMappingRulesService = fieldMappingRulesService;
        this.reconciliationRulesService = reconciliationRulesService;
    }

    // --- 字段映射规则管理 (Field Mapping Rules) ---

    @PostMapping("/field-mappings")
    public ResponseResult<DzFieldMappingRulesEntity> createFieldMappingRule(@RequestBody DzFieldMappingRulesEntity ruleEntity) {
        // TODO: 添加校验逻辑
        fieldMappingRulesService.save(ruleEntity); // 假设Service有save方法
        return ResponseResult.success(ruleEntity);
    }

    @GetMapping("/field-mappings")
    public ResponseResult<List<DzFieldMappingRulesEntity>> getAllFieldMappingRules() {
        return ResponseResult.success(fieldMappingRulesService.list()); // 假设Service有list方法返回所有
    }

    @GetMapping("/field-mappings/channel/{channelCode}/type/{transactionType}")
    public ResponseResult<List<DzFieldMappingRulesEntity>> getFieldMappingRulesByChannelAndType(
            @PathVariable String channelCode,
            @PathVariable String transactionType) {
        // 假设Service有此查询方法 (之前在RecordMapperService中用到过类似的)
        return ResponseResult.success(fieldMappingRulesService.findActiveRulesByChannelAndType(channelCode, transactionType));
    }

    @PutMapping("/field-mappings/{id}")
    public ResponseResult<DzFieldMappingRulesEntity> updateFieldMappingRule(@PathVariable Long id,
            @RequestBody DzFieldMappingRulesEntity ruleEntity) {
        // TODO: 校验ID和实体，确保ID一致性，以及记录存在
        ruleEntity.setId(id);
        fieldMappingRulesService.updateById(ruleEntity); // 假设Service有updateById方法
        return ResponseResult.success(ruleEntity);
    }

    @DeleteMapping("/field-mappings/{id}")
    public ResponseResult<Void> deleteFieldMappingRule(@PathVariable Long id) {
        fieldMappingRulesService.removeById(id); // 假设Service有removeById方法
        return ResponseResult.success();
    }

    // --- 对账规则管理 (Reconciliation Rules) ---

    @PostMapping("/reconciliation-definitions")
    public ResponseResult<DzReconciliationRulesEntity> createReconciliationRule(@RequestBody DzReconciliationRulesEntity ruleEntity) {
        // TODO: 添加校验逻辑，特别是 rule_content_json 的格式和内容
        reconciliationRulesService.save(ruleEntity);
        return ResponseResult.success(ruleEntity);
    }

    @GetMapping("/reconciliation-definitions")
    public ResponseResult<List<DzReconciliationRulesEntity>> getAllReconciliationRules() {
        return ResponseResult.success(reconciliationRulesService.list());
    }

    @GetMapping("/reconciliation-definitions/channel/{channelCode}/type/{transactionType}/active")
    public ResponseResult<DzReconciliationRulesEntity> getActiveReconciliationRule(
            @PathVariable String channelCode,
            @PathVariable String transactionType) {
        // 假设Service有此查询方法 (之前在ReconciliationEngineService中用到过类似的)
        return ResponseResult.success(reconciliationRulesService.findActiveRuleByChannelAndType(channelCode, transactionType));
    }

    @PutMapping("/reconciliation-definitions/{id}")
    public ResponseResult<DzReconciliationRulesEntity> updateReconciliationRule(@PathVariable Long id,
            @RequestBody DzReconciliationRulesEntity ruleEntity) {
        ruleEntity.setId(id);
        reconciliationRulesService.updateById(ruleEntity);
        return ResponseResult.success(ruleEntity);
    }

    @DeleteMapping("/reconciliation-definitions/{id}")
    public ResponseResult<Void> deleteReconciliationRule(@PathVariable Long id) {
        reconciliationRulesService.removeById(id);
        return ResponseResult.success();
    }

    // TODO: 可能还需要规则版本管理、激活/禁用规则的接口
}