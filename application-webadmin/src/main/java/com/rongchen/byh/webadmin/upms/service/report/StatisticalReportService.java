package com.rongchen.byh.webadmin.upms.service.report;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.rongchen.byh.common.core.object.MyPageData;
import com.rongchen.byh.common.core.object.MyPageDataObject;
import com.rongchen.byh.common.core.object.MyPageParam;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.webadmin.upms.dto.export.StatisticalReportDto;
import com.rongchen.byh.webadmin.upms.vo.export.*;

public interface StatisticalReportService {
    /**
     * 授信信息和还款信息统计
     *
     * @param dto 筛选条件
     * */
    ResponseResult<MyPageData<StatisticalReportListVo.Root>> statisticalReportList(StatisticalReportDto dto, MyPageParam pageParam);

    ResponseResult<MyPageData<ReportDataVo>> lateReportList(StatisticalReportDto dto, MyPageParam pageParam);

    ResponseResult<MyPageData<MobUserRateVO>> lateMOBUserRate(StatisticalReportDto dto, MyPageParam pageParam);

    ResponseResult<MyPageData<MobAmountRateVO>> lateMOBAmountRate(StatisticalReportDto dto, MyPageParam pageParam);

    ResponseResult<MyPageData<DailyWithdrawalVo>> dailyWithdrawalList(StatisticalReportDto dto, MyPageParam pageParam);

    ResponseResult<MyPageData<DailyCreditLendingVo>> dailyCreditLendinglList(StatisticalReportDto dto, MyPageParam pageParam);

    ResponseResult<MyPageDataObject<OverdueUserDetailsVo, OverdueUserDetailsVo.Summary>> overdueUserCountDaysList(StatisticalReportDto dto, MyPageParam pageParam);

    ResponseResult<MyPageDataObject<OverdueAmountDetailsVo, OverdueAmountDetailsVo.Summary>> overdueAmountDaysList(StatisticalReportDto dto, MyPageParam pageParam);

    ResponseResult<MyPageData<OverDayUserCountRateVo>> recoveryDaysList(StatisticalReportDto dto, MyPageParam pageParam);

    ResponseResult<MyPageData<OverDayUserAmountRateVo>> recoveryDaysAmountList(StatisticalReportDto dto, MyPageParam pageParam);

    ResponseResult<MyPageData<OutstandingLoanBalanceVo>> loanBalanceList(StatisticalReportDto dto, MyPageParam pageParam);
}
