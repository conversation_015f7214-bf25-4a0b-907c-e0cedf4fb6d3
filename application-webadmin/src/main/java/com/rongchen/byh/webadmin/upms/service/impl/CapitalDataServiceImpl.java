package com.rongchen.byh.webadmin.upms.service.impl;

import com.github.pagehelper.page.PageMethod;
import com.rongchen.byh.common.core.object.MyPageData;
import com.rongchen.byh.common.core.object.MyPageParam;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.MyPageUtil;
import com.rongchen.byh.webadmin.upms.dao.CapitalDataMapper;
import com.rongchen.byh.webadmin.upms.model.CapitalData;
import com.rongchen.byh.webadmin.upms.model.GroupData;
import com.rongchen.byh.webadmin.upms.service.CapitalDataService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 资方信息表(CapitalData)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-03 17:30:00
 */
@Service
public class CapitalDataServiceImpl implements CapitalDataService {
    @Resource
    private CapitalDataMapper capitalDataMapper;

    @Override
    public ResponseResult<MyPageData<CapitalData>> capitalList(CapitalData capitalData, MyPageParam pageParam) {
        if (pageParam != null) {
            PageMethod.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        }
        List<CapitalData> capitalDataList = capitalDataMapper.selectList(null);
        return ResponseResult.success(MyPageUtil.makeResponseData(capitalDataList, CapitalData.class));
    }

    /**
     * 新增数据
     *
     * @param capitalData 实例对象
     * @return 实例对象
     */
    @Override
    public boolean insert(CapitalData capitalData) {

        return capitalDataMapper.insert(capitalData)> 0;
    }

    /**
     * 修改数据
     *
     * @param capitalData 实例对象
     * @return 实例对象
     */
    @Override
    public boolean update(CapitalData capitalData) {
        return capitalDataMapper.updateById(capitalData) > 0;
    }

    @Override
    public List<CapitalData> capitalDictList() {
        List<CapitalData> capitalDataList = capitalDataMapper.selectList(null);
        return capitalDataList;
    }

}
