package com.rongchen.byh.webadmin.upms.dto;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 授信数据与申请信息联合查询DTO
 * 包含disburse_data和user_loan_apply表的关键字段
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-XX
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreditDataWithApplyInfo implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    // === disburse_data表字段 ===
    /**
     * 支用记录ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 产品ID
     */
    private Long productId;
    
    /**
     * 授信流水号
     */
    private String creditNo;
    
    /**
     * 放款编号
     */
    private String loanNo;
    
    /**
     * 赊销编号
     */
    private String saleNo;
    
    /**
     * 放款金额
     */
    private BigDecimal creditAmount;
    
    /**
     * 总利息
     */
    private BigDecimal grossInterest;
    
    /**
     * 授信状态 100-授信中 200-授信失败 300-放款中 400-放款失败 500-还款中 600-已结清
     */
    private Integer creditStatus;
    
    /**
     * 授信通过时间
     */
    private Date creditTime;
    
    /**
     * 放款通过时间
     */
    private Date loanTime;
    
    /**
     * 结清时间
     */
    private Date repaymentTime;
    
    /**
     * 放款期数（月）
     */
    private Integer periods;
    
    /**
     * 资金方编码
     */
    private String fundCode;
    
    /**
     * 借款用途
     */
    private String purposeLoan;
    
    /**
     * 年利率
     */
    private String yearRete;
    
    /**
     * 还款方式
     */
    private String repaymentMethod;
    
    /**
     * 赊销金额
     */
    private BigDecimal saleRepayAmount;
    
    /**
     * 开户行
     */
    private String bankName;
    
    /**
     * 支用记录创建时间
     */
    private Date createTime;
    
    /**
     * 预留手机号
     */
    private String bankPhone;
    
    /**
     * 银行卡号
     */
    private String bankAccount;
    
    /**
     * 咨询费用扣款状态
     */
    private Integer consultFee;
    
    /**
     * 资方授信ID
     */
    private Long capitalRecordId;
    
    /**
     * 资方ID
     */
    private Long capitalId;
    
    /**
     * 资方订单号
     */
    private String contractId;
    
    /**
     * 行方贷款合同号
     */
    private String fundOrderId;
    
    /**
     * 赊销订单类型
     */
    private String saleModel;
    
    /**
     * 赊销供应商编码
     */
    private String saleChannel;
    
    // === user_loan_apply表字段 ===
    /**
     * 申请记录ID
     */
    private Long applyId;
    
    /**
     * 授信ID
     */
    private String creditId;
    
    /**
     * API授信编号
     */
    private String apiCreditNo;
    
    /**
     * 申请类型 0-初筛申请 1-授信申请
     */
    private Integer applyType;
    
    /**
     * 审核状态 0-待审核 1-审核通过 2-审核不通过 4-转人工
     */
    private Integer auditsStatus;
    
    /**
     * 线上线下类型 0-线上 1-线下 2-空中 5-空中仅注册 6-线上仅注册 7-线下仅注册
     */
    private Integer onlineType;
    
    /**
     * 提交申请信息
     */
    private String userApplyInfo;
    
    /**
     * 申请创建时间
     */
    private Date applyCreateTime;
    
    /**
     * 申请更新时间
     */
    private Date applyUpdateTime;
}
