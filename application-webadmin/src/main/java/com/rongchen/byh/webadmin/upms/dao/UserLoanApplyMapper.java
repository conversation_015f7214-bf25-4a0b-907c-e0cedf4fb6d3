package com.rongchen.byh.webadmin.upms.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rongchen.byh.webadmin.upms.model.UserLoanApplyEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**

 * 项目名称：byh_java
 * 文件名称: UserLoanApplyMapper
 * 创建时间: 2025-06-05 16:36
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.webadmin.upms.dao
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
public interface UserLoanApplyMapper extends BaseMapper<UserLoanApplyEntity> {
    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(UserLoanApplyEntity record);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(UserLoanApplyEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(UserLoanApplyEntity record);

    int updateBatch(@Param("list") List<UserLoanApplyEntity> list);

    int updateBatchSelective(@Param("list") List<UserLoanApplyEntity> list);

    int batchInsert(@Param("list") List<UserLoanApplyEntity> list);

    int batchInsertOrUpdate(@Param("list") List<UserLoanApplyEntity> list);
}