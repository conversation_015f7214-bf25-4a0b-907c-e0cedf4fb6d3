package com.rongchen.byh.webadmin.upms.vo.export;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class OverDayUserAmountVo {
    @Schema(description = "放款日期", example = "2023-04-15")
    private String loanDate;

    @Schema(description = "渠道号", example = "xhy")
    private String channelName;

    @Schema(description = "回收率", example = "2.00%")
    private String overdue;

    @Schema(description = "逾期1天金额", example = "10000.00")
    private BigDecimal overdue1DayAmount;

    @Schema(description = "逾期2天金额", example = "8000.00")
    private BigDecimal overdue2DayAmount;

    @Schema(description = "逾期3天金额", example = "6000.00")
    private BigDecimal overdue3DayAmount;

    @Schema(description = "逾期4天金额", example = "4000.00")
    private BigDecimal overdue4DayAmount;

    @Schema(description = "逾期5天金额", example = "3000.00")
    private BigDecimal overdue5DayAmount;

    @Schema(description = "逾期6天金额", example = "2000.00")
    private BigDecimal overdue6DayAmount;

    @Schema(description = "逾期7天金额", example = "1500.00")
    private BigDecimal overdue7DayAmount;

    @Schema(description = "逾期8天金额", example = "1000.00")
    private BigDecimal overdue8DayAmount;

    @Schema(description = "逾期9天金额", example = "800.00")
    private BigDecimal overdue9DayAmount;

    @Schema(description = "逾期10天金额", example = "600.00")
    private BigDecimal overdue10DayAmount;

    @Schema(description = "逾期11天金额", example = "500.00")
    private BigDecimal overdue11DayAmount;

    @Schema(description = "逾期12天金额", example = "400.00")
    private BigDecimal overdue12DayAmount;

    @Schema(description = "逾期13天金额", example = "300.00")
    private BigDecimal overdue13DayAmount;

    @Schema(description = "逾期14天金额", example = "200.00")
    private BigDecimal overdue14DayAmount;

    @Schema(description = "逾期15天金额", example = "100.00")
    private BigDecimal overdue15DayAmount;

    @Schema(description = "逾期16天金额", example = "50.00")
    private BigDecimal overdue16DayAmount;
}
