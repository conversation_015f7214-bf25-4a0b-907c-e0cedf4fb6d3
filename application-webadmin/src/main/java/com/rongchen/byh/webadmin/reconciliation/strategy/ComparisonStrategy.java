package com.rongchen.byh.webadmin.reconciliation.strategy;

import com.rongchen.byh.webadmin.reconciliation.context.ReconciliationContext;
import com.rongchen.byh.webadmin.reconciliation.model.FieldDifferenceDetail;
import com.rongchen.byh.webadmin.reconciliation.model.NormalizedTransaction;
import java.util.List;
import java.util.Map;

/**
 * 对账比较策略接口。
 * <p>
 * 定义了如何比较两条已匹配的标准化交易记录（一条我方，一条资方），
 * 并找出它们之间的字段差异。
 * <p>
 * 实现此接口可以支持基于JSON DSL的动态规则比较，或基于Java代码的固定复杂逻辑比较。
 */
public interface ComparisonStrategy {

    /**
     * 比较我方记录和资方记录的字段。
     *
     * @param ourRecord          我方标准化记录。
     *                           注意：当前类型为 NormalizedTransaction，如果需要支持多种交易类型，
     *                           应考虑使用一个通用的基类或接口 (例如 NormalizedTransaction)。
     * @param partnerRecord      资方标准化记录。类型说明同上。
     * @param strategyParameters 来自规则定义 (reconciliation_rules表的rule_content_json)
     *                           的策略特定参数。
     *                           例如，对于JSON DSL策略，这里可能是空的；对于Java类策略，这里可能包含配置。
     * @param context            当前的对账上下文信息。
     * @return 字段差异详情列表 ({@link FieldDifferenceDetail})。如果无差异，则返回空列表。
     * @throws Exception 如果比较过程中发生不可恢复的错误。
     */
    List<FieldDifferenceDetail> compare(
            NormalizedTransaction ourRecord,
            NormalizedTransaction partnerRecord,
            Map<String, Object> strategyParameters,
            ReconciliationContext context) throws Exception;

    /**
     * 获取此比较策略的名称。
     * <p>
     * 对于基于Java类的策略，通常返回类名。
     * 对于基于JSON DSL的策略，可能返回一个固定的名称如 "JsonDslComparisonStrategy" 或规则ID。
     *
     * @return 策略名称。
     */
    String getStrategyName();

}