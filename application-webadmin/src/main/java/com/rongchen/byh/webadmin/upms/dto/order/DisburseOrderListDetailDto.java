package com.rongchen.byh.webadmin.upms.dto.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName CreditOrderListDetailDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/6 16:15
 * @Version 1.0
 **/
@Data
public class DisburseOrderListDetailDto {

    @Schema(description = "订单号")
    private Long disburseId;

    @Schema(description = "客户id"  , required = true)
    private String userId;
}
