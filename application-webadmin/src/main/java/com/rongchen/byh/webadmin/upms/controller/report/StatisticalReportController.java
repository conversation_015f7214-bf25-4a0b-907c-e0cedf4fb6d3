package com.rongchen.byh.webadmin.upms.controller.report;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.rongchen.byh.common.core.annotation.MyRequestBody;
import com.rongchen.byh.common.core.object.MyPageData;
import com.rongchen.byh.common.core.object.MyPageDataObject;
import com.rongchen.byh.common.core.object.MyPageParam;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.webadmin.upms.dto.export.StatisticalReportDto;
import com.rongchen.byh.webadmin.upms.service.report.StatisticalReportService;
import com.rongchen.byh.webadmin.upms.vo.export.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Tag(name = "统计报表相关接口")
@RestController
@RequestMapping("/admin/upms/reportForm")
public class StatisticalReportController {

    @Resource
    private StatisticalReportService statisticalReportService;

    /**
     * 查询汇总统计数据
     *
     * @param dto   筛选条件
     * @return 查询结果
     */
    @Operation(summary = "查询汇总统计数据")
    @PostMapping("/statisticalReportList")
    @ResponseStatus(HttpStatus.OK)
    public ResponseResult<MyPageData<StatisticalReportListVo.Root>> statisticalReportList(@MyRequestBody StatisticalReportDto dto, @MyRequestBody MyPageParam pageParam)  {
       return statisticalReportService.statisticalReportList(dto,pageParam);
    }


    /**
     * 逾期汇总统计每日人数和金额数据
     *
     * @param dto   筛选条件
     * @return 查询结果
     */
    @Operation(summary = "逾期汇总统计每日人数和金额数据")
    @PostMapping("/lateReportList")
    public ResponseResult<MyPageData<ReportDataVo>> lateReportList(@MyRequestBody StatisticalReportDto dto, @MyRequestBody MyPageParam pageParam) {
        return statisticalReportService.lateReportList(dto,pageParam);
    }


    /**
     * 逾期汇总MOB月份统计人数数据比例
     *
     * @param dto   筛选条件
     * @return 查询结果
     */
    @Operation(summary = "逾期汇总MOB月份统计人数数据比例")
    @PostMapping("/lateMOBUserRate")
    public ResponseResult<MyPageData<MobUserRateVO>> lateMOBUserRate(@MyRequestBody StatisticalReportDto dto, @MyRequestBody MyPageParam pageParam) {
        return statisticalReportService.lateMOBUserRate(dto,pageParam);
    }

    /**
     * 逾期汇总MOB月份统计金额数据比例
     *
     * @param dto   筛选条件
     * @return 查询结果
     */
    @Operation(summary = "逾期汇总MOB月份统计金额数据比例")
    @PostMapping("/lateMOBAmountRate")
    public ResponseResult<MyPageData<MobAmountRateVO>> lateMOBAmountRate(@MyRequestBody StatisticalReportDto dto, @MyRequestBody MyPageParam pageParam) {
        return statisticalReportService.lateMOBAmountRate(dto,pageParam);
    }

    @Operation(summary = "查询每日提现率")
    @PostMapping("/dailyWithdrawalList")
    public ResponseResult<MyPageData<DailyWithdrawalVo>> dailyWithdrawalList(@MyRequestBody StatisticalReportDto dto, @MyRequestBody MyPageParam pageParam) {
        return statisticalReportService.dailyWithdrawalList(dto,pageParam);
    }

    @Operation(summary = "查询每日授信放款")
    @PostMapping("/dailyCreditLendinglList")
    public ResponseResult<MyPageData<DailyCreditLendingVo>> dailyCreditLendinglList(@MyRequestBody StatisticalReportDto dto, @MyRequestBody MyPageParam pageParam) {
        return statisticalReportService.dailyCreditLendinglList(dto,pageParam);
    }


    @Operation(summary = "逾期天数 - 人数明细")
    @PostMapping("/overdueUserCountDaysList")
    public ResponseResult<MyPageDataObject<OverdueUserDetailsVo, OverdueUserDetailsVo.Summary>> overdueUserCountDaysList(@MyRequestBody StatisticalReportDto dto, @MyRequestBody MyPageParam pageParam) {
        return statisticalReportService.overdueUserCountDaysList(dto,pageParam);
    }

    @Operation(summary = "逾期天数 - 金额明细")
    @PostMapping("/overdueAmountDaysList")
    public ResponseResult<MyPageDataObject<OverdueAmountDetailsVo, OverdueAmountDetailsVo.Summary>> overdueAmountDaysList(@MyRequestBody StatisticalReportDto dto, @MyRequestBody MyPageParam pageParam) {
        return statisticalReportService.overdueAmountDaysList(dto,pageParam);
    }

    @Operation(summary = "回收率 - 人数明细")
    @PostMapping("/recoveryDaysList")
    public ResponseResult<MyPageData<OverDayUserCountRateVo>> recoveryDaysList(@MyRequestBody StatisticalReportDto dto, @MyRequestBody MyPageParam pageParam) {
        return statisticalReportService.recoveryDaysList(dto,pageParam);
    }

    @Operation(summary = "回收率 - 金额明细")
    @PostMapping("/recoveryDaysAmountList")
    public ResponseResult<MyPageData<OverDayUserAmountRateVo>> recoveryDaysAmountList(@MyRequestBody StatisticalReportDto dto, @MyRequestBody MyPageParam pageParam) {
        return statisticalReportService.recoveryDaysAmountList(dto,pageParam);
    }

    @Operation(summary = "在贷余额报表")
    @PostMapping("/loanBalanceList")
    public ResponseResult<MyPageData<OutstandingLoanBalanceVo>> loanBalanceList(@MyRequestBody StatisticalReportDto dto, @MyRequestBody MyPageParam pageParam) {
        return statisticalReportService.loanBalanceList(dto,pageParam);
    }

}
