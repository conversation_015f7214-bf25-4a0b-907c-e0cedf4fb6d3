package com.rongchen.byh.webadmin.upms.model.constant;

import lombok.Getter;

@Getter
public enum FundFeeEnum {
    XJ_HB2("XJ_HB2", "湖北消金", 0.0020, 360),
    XJ_WP("XJ_WP", "唯品消金", 0.0020, 360),
    XJ_ZY_DY("XJ_ZY_DY", "中原东营联合贷", 0.0095, 365),
    YH_HY_HE("YH_HY_HE", "韩亚银行", 0.02, 360),
    YH_MS("YH_MS", "蒙商消金", 0.0025, 360),
    YH_ZX("YH_ZX", "振兴银行", 0.0020, 360),
    YH_LH_HE("YH_LH_HE", "海尔蓝海银行", 0.0070, 360),
    YH_ZB_JHX("YH_ZB_JHX", "众邦银行嘉合兴", 0.0, 0),
    DEFAULT("", "未知资金方", 0.0, 0);

    private final String fundCode;
    private final String fundName;
    private final Double rate;
    private final Integer daysBase;

    FundFeeEnum(String fundCode, String fundName, Double rate, Integer daysBase) {
        this.fundCode = fundCode;
        this.fundName = fundName;
        this.rate = rate;
        this.daysBase = daysBase;
    }

    public static FundFeeEnum fromFundCode(String fundCode) {
        for (FundFeeEnum enumValue : FundFeeEnum.values()) {
            if (enumValue.fundCode.equals(fundCode)) {
                return enumValue;
            }
        }
        return DEFAULT;
    }
}
