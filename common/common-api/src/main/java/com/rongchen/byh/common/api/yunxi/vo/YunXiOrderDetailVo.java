package com.rongchen.byh.common.api.yunxi.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 订单详情对象
 */
@Data
public class YunXiOrderDetailVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 权益平台订单号
     */
    private String orderNum;

    /**
     * 合作方订单号
     */
    private String externalOrderNum;

    /**
     * 支付单号
     */
    private String paymentNo;

    /**
     * 订单金额 单位元
     */
    private String orderAmount;

    /**
     * 合作方用户号
     */
    private String openId;

    /**
     * 合作方用户手机号(AES加密)
     */
    private String userMobile;

    /**
     * 支付方式
     * BAOFU_PROXY_PAY-宝付
     */
    private String payWay;

    /**
     * 券包号
     */
    private String couponPackageId;

    /**
     * 券包名
     */
    private String couponPackageName;

    /**
     * 权益订单下单时间
     * 格式 yyyy-MM-dd HH:mm:ss
     */
    private String createTime;

    /**
     * 支付完成时间
     * yyyy-MM-dd HH:mm:ss
     */
    private String payTime;

    /**
     * 权益失效时间
     * yyyy-MM-dd HH:mm:ss
     */
    private String expireTime;

    /**
     * 订单状态
     * PAY_ING-支付中
     * PAY_SUCCESS-支付成功
     * REFUND_ING-退款中
     * REFUND_SUCCESS-退款成功
     * REFUND_FAIL-退款失败
     */
    private String orderStatus;

    /**
     * 可退状态
     * true-可退
     * false-不可退
     */
    private Boolean refundable;

    /**
     * 退款金额 单位元
     */
    private BigDecimal returnAmount;

    /**
     * 退款时间
     * yyyy-MM-dd HH:mm:ss
     */
    private String returnTime;

    /**
     * 时间周期
     */
    private Object timePeriods;

}