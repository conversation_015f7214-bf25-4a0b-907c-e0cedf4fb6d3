package com.rongchen.byh.common.api.zifang.dto;

import lombok.Data;

import java.util.List;

/**
 * @ClassName SaleApplyDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/9 18:07
 * @Version 1.0
 **/
@Data
public class SaleApplyDto {

    /**
     * 借款单号，必填，用于唯一标识一笔借款业务。
     */
    private String loanNo;
    /**
     * 赊销订单号，必填，在融担和保理双订单时，赊销订单号要区分开，用于标识对应的赊销订单。
     */
    private String saleNo;
    /**
     * 赊销订单金额，必填，精确到小数点后两位，代表赊销订单涉及的金额数目。
     */
    private String applyAmt;
    /**
     * 申请期数，必填，用于表示赊销订单申请的期数情况。
     */
    private String applyTerm;
    /**
     * 商品名称，非必填，用于记录对应的商品名称（若有）。
     */
    private String goodsName;
    /**
     * 商品编码，非必填，用于标识对应的商品编码（若有）。
     */
    private String goodsCode;
    /**
     * 赊销订单费率，必填，格式为0.0610，保留4位小数，代表赊销订单的费率情况。
     */
    private String saleRate;
    /**
     * 赊销订单类型，必填，取值有DYRD（融担）、ZKBBL（保理），用于区分赊销订单的具体类型。
     */
    private String saleModel;

    List<SaleApplyPkgDto> pkgList;



    /**
     * 用户手机号
     */
    private String mobile;
    /**
     * 用户id
     */
    private String userId;

    /**
     * 支付流水号
     */
    private String paymentNo;
}
