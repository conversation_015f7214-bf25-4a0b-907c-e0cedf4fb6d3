package com.rongchen.byh.common.api.idCardVerify.vo;


import lombok.Data;

@Data
public class IdCardResult {

    /**
     * 姓名（人像面）
     * 示例值：李明
     */
    private String name;

    /**
     * 性别（人像面）
     * 示例值：男
     */
    private String sex;

    /**
     * 民族（人像面）
     * 示例值：汉
     */
    private String nation;

    /**
     * 出生日期（人像面）
     * 示例值：1987/1/1
     */
    private String birth;

    /**
     * 地址（人像面）
     * 示例值：北京市石景山区高新技术园腾讯大楼
     */
    private String address;

    /**
     * 身份证号（人像面）
     * 示例值：******************
     */
    private String idNum;

    /**
     * 发证机关（国徽面）
     * 示例值：深圳市公安局南山分局
     */
    private String authority;

    /**
     * 证件有效期（国徽面）
     * 示例值：2017.08.12-2037.08.12
     */
    private String validDate;
}
