package com.rongchen.byh.common.api.bankCredit.utils;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025/2/22 16:51:46
 */
@Slf4j
public class DocumentTemplateUtil {

    private static final int BUFFER_SIZE = 8192;
  


    /**
     * 处理文件转换
     */
    public static File processFileConversion(File tempFile, ProcessOptions options) throws Exception {
        // 1. PDF转换
        if (options.isConvertToPdf()) {
            tempFile = convertToPdfFile(tempFile);
            // 2. PDF压缩
            if (options.isCompressPdf()) {
                compressPdf(tempFile.getAbsolutePath(), options.getMaxPdfSize());
            }
        }

        // 3. ZIP压缩
        if (options.isZipCompress()) {
            tempFile = compressToZipFile(tempFile);
        }

        return tempFile;
    }

    /**
     * 转换为PDF文件
     */
    private static File convertToPdfFile(File docxFile) throws Exception {
        String pdfPath = docxFile.getAbsolutePath().replace(".docx", ".pdf");
        convertToPdf(docxFile.getAbsolutePath(), pdfPath);
        docxFile.delete();
        return new File(pdfPath);
    }

    /**
     * 压缩为ZIP文件
     */
    private static File compressToZipFile(File sourceFile) throws Exception {
        //耗时
        long startTime = System.currentTimeMillis();
        String zipPath = sourceFile.getAbsolutePath() + ".zip";
        compressToZip(sourceFile.getAbsolutePath(), zipPath);
        sourceFile.delete();
        long endTime = System.currentTimeMillis();
        log.info("压缩为ZIP文件完成，耗时: {} ms", endTime - startTime);
        return new File(zipPath);
    }

    /**
     * 处理输出
     */
    public static String processOutput(File tempFile, ProcessOptions options) throws Exception {
        String result;
        if (StrUtil.isNotBlank(options.getOutputPath())) {
            result = handleOutputPath(tempFile, options);
        } else {
            result = options.isBase64Encode() ? Base64.encode(tempFile) : tempFile.getAbsolutePath();
        }
        return result;
    }

    /**
     * 处理输出路径
     */
    private static String handleOutputPath(File tempFile, ProcessOptions options) throws Exception {
        String outputPath = options.getOutputPath();
        String result;

        // 1. 处理文件扩展名
        outputPath = adjustOutputPath(tempFile, outputPath);

        // 2. 验证输出路径
        validateOutputPath(outputPath);

        // 3. 创建目标文件
        File targetFile = createTargetFile(outputPath);

        // 4. 复制文件
        FileUtil.copy(tempFile, targetFile, true);

        // 5. 清理临时文件
        cleanupTempFile(tempFile);

        // 6. 返回结果
        result = generateResult(targetFile, options.isBase64Encode());

        return result;
    }

    /**
     * 调整输出路径
     */
    private static String adjustOutputPath(File tempFile, String outputPath) {
        String actualExtension = FileUtil.extName(tempFile.getName()).toLowerCase();
        String expectedExtension = FileUtil.extName(outputPath).toLowerCase();

        if (!actualExtension.equals(expectedExtension)) {
            String originalPath = outputPath;
            outputPath = outputPath.substring(0, outputPath.lastIndexOf(".")) + "." + actualExtension;
            log.warn("输出文件格式与实际格式不匹配。期望格式: {}, 实际格式: {}, 已自动调整输出路径从 {} 到 {}",
                    expectedExtension, actualExtension, originalPath, outputPath);
        }

        return outputPath;
    }

    /**
     * 创建目标文件
     */
    private static File createTargetFile(String outputPath) throws IOException {
        File targetFile = new File(outputPath);
        File parentDir = targetFile.getParentFile();

        if (parentDir != null && !parentDir.exists()) {
            if (!parentDir.mkdirs()) {
                throw new IOException("无法创建目标目录: " + parentDir.getAbsolutePath());
            }
        }

        return targetFile;
    }

    /**
     * 清理临时文件
     */
    private static void cleanupTempFile(File tempFile) {
        if (tempFile.exists() && !tempFile.delete()) {
            log.warn("无法删除临时文件: {}", tempFile.getAbsolutePath());
            tempFile.deleteOnExit();
        }
    }

    /**
     * 生成结果
     */
    private static String generateResult(File targetFile, boolean isBase64Encode) throws IOException {
        // 如果需要Base64编码，则进行Base64编码
        if (isBase64Encode) {
            // 如果文件大于10MB，则分块进行Base64编码
            return targetFile.length() > 10 * 1024 * 1024 ? // 10MB
                    encodeFileToBase64InChunks(targetFile) : Base64.encode(targetFile);
        }
        // 如果不需要Base64编码，则返回文件路径
        return targetFile.getAbsolutePath();
    }





    /**
     * 将Word文档转换为PDF
     */
    private static void convertToPdf(String docxPath, String pdfPath) throws Exception {
        //耗时
        long startTime = System.currentTimeMillis();
        DocumentConvertUtil.wordToPdf(docxPath, pdfPath);
        long endTime = System.currentTimeMillis();
        log.info("将Word文档转换为PDF完成，耗时: {} ms", endTime - startTime);
    }

    /**
     * 压缩PDF文件
     */
    private static void compressPdf(String pdfPath, long maxSize) throws Exception {
        //耗时
        long startTime = System.currentTimeMillis();
        DocumentConvertUtil.compressPdf(pdfPath, maxSize);
        long endTime = System.currentTimeMillis();
        log.info("压缩PDF文件完成，耗时: {} ms", endTime - startTime);
    }

    /**
     * 将文件压缩为ZIP
     */
    private static void compressToZip(String filePath, String zipPath) throws Exception {
        try (FileInputStream fis = new FileInputStream(filePath);
             FileOutputStream fos = new FileOutputStream(zipPath);
             ZipOutputStream zos = new ZipOutputStream(fos)) {

            ZipEntry zipEntry = new ZipEntry(new File(filePath).getName());
            zos.putNextEntry(zipEntry);

            byte[] bytes = new byte[BUFFER_SIZE];
            int length;
            while ((length = fis.read(bytes)) >= 0) {
                zos.write(bytes, 0, length);
            }
        }
    }

    /**
     * 文档处理选项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProcessOptions {
        // 是否转换为PDF
        private boolean convertToPdf;
        // 是否压缩PDF
        private boolean compressPdf;
        // PDF压缩最大大小，单位为字节 默认为10MB
        private long maxPdfSize;
        // 是否压缩为ZIP
        private boolean zipCompress;
        // 是否使用Base64编码返回，默认false，返回文件路径
        private boolean base64Encode;
        // 输出路径，如果为空，则返回绝对文件路径
        private String outputPath;
    }


    /**
     * 验证输出路径的安全性
     */
    private static void validateOutputPath(String outputPath) throws IOException {
        if (outputPath == null || outputPath.trim().isEmpty()) {
            throw new IllegalArgumentException("输出路径不能为空");
        }

        File file = new File(outputPath);
        String canonicalPath = file.getCanonicalPath();
        String absolutePath = file.getAbsolutePath();

        // 检查路径规范化
        if (!canonicalPath.equals(absolutePath)) {
            throw new IllegalArgumentException("检测到非法的路径: " + outputPath);
        }

        // 检查文件扩展名
        String extension = FileUtil.extName(outputPath).toLowerCase();
        if (!isAllowedExtension(extension)) {
            throw new IllegalArgumentException("不支持的文件类型: " + extension);
        }
    }

    /**
     * 检查文件扩展名是否允许
     */
    private static boolean isAllowedExtension(String extension) {
        return "docx".equals(extension) || "pdf".equals(extension) || "zip".equals(extension);
    }

    /**
     * 分块进行Base64编码
     */
    private static String encodeFileToBase64InChunks(File file) throws IOException {
        StringBuilder base64String = new StringBuilder();
        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = fis.read(buffer)) != -1) {
                if (bytesRead < buffer.length) {
                    byte[] temp = new byte[bytesRead];
                    System.arraycopy(buffer, 0, temp, 0, bytesRead);
                    base64String.append(Base64.encode(temp));
                } else {
                    base64String.append(Base64.encode(buffer));
                }
            }
        }
        return base64String.toString();
    }

}
