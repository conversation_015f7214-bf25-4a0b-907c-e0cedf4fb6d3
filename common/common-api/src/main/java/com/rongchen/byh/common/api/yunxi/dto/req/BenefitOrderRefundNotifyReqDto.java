package com.rongchen.byh.common.api.yunxi.dto.req;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;

/**
 * 权益订单退款通知请求
 * API Code: AP106
 */
@Data
public class BenefitOrderRefundNotifyReqDto extends YunXiBashReqDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * API编码
     */
    public static final String API_CODE = "AP106";

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 签名
     */
    private String sign;

    /**
     * 合作方订单号
     */
    private String externalOrderNum;

    /**
     * 渠道退款单号
     */
    private String paymentNo;

    /**
     * 原渠道交易流水号
     */
    private String oriPaymentNo;

    /**
     * 退款金额，单位元
     */
    private BigDecimal orderAmount;

    /**
     * 退款原因
     */
    private String description;

}