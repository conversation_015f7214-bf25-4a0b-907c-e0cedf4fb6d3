package com.rongchen.byh.common.api.baofu.vo.req;

import lombok.Data;

/**
 * 银行卡绑定请求VO
 */
@Data
public class BankCardBindReqVo {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 银行卡号
     * 测试挡板规则:
     * 测试环境只支持工农招建中、平安等银行；支付交易测试挡板规则：根据卡号最后一位号码模拟不同交易结果
     * 1：余额不足
     * 3：卡状态失败
     * 5：交易失败
     * 7：处理中  2分钟后失败
     * 9：处理中  2分钟后成功
     * 0、2、4、6、8：成功"
     */
    private String bankCardNo;

    /**
     * 持卡人姓名
     */
    private String cardholderName;

    /**
     *
     * 证件类型- id_card_type
     * 类型编码	含义
     * 01	身份证
     * 12	港澳居民居住证
     * 13	台湾居民居住证
     * 09	外国人永久居住证
     */
    private String idCardType;

    /**
     * 证件号码
     */
    private String idCardNo;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 卡类型
     * 类型编码	含义
     * 101	借记卡
     * 102	信用卡
     */
    private String cardType;
}