package com.rongchen.byh.common.api.zifang.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName QueryBindBankResultDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/9 11:57
 * @Version 1.0
 **/
@Data
public class QueryBindBankResultDto {

    /**
     *进件单号
     */
    @Schema(description = "进件单号" , hidden = true)
    private String orderNo;

    /**
     * 短信验证码流水号
     */
    @Schema(description = "短信验证码流水号")
    private String messageNo;

    /**
     * 银行卡号
     */
    @Schema(description = "银行卡号")
    private String bankCardNo;

    /**
     * 银行预留手机号
     */
    @Schema(description = "银行预留手机号")
    private String phone;

    /**
     * 证件号
     */
    @Schema(description = "证件号")
    private String idCardNo;

    /**
     * 姓名
     */
    @Schema(description = "姓名")
    private String name;

    @Schema(description = "支付渠道")
    private String payChannel;

    @Schema(description = "用户ID")
    private String userId;

}
