package com.rongchen.byh.common.api.zifang.vo;

import lombok.Data;

import java.util.List;

/**
 * @ClassName SaleRepayPlanVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/10 10:29
 * @Version 1.0
 **/
@Data
public class SaleRepayPlanVo {


    /**
     * 必填，响应码，备注：用于标识相关操作返回的结果状态代码，不同代码对应不同情况。
     */
    private String responseCode;
    /**
     * 非必填，响应消息，备注：用于对响应码对应的具体情况做更详细的文字描述解释。
     */
    private String responseMsg;
    /**
     * 必填，赊销单号，备注：用于唯一标识一笔赊销业务对应的单号，方便后续业务关联与查询等操作。
     */
    private String saleNo;

    List<SaleRepayPlanRepayVo> repayPlanList;
}
