package com.rongchen.byh.common.api.notice.service.impl;

import cn.hutool.core.util.StrUtil;
import com.rongchen.byh.common.api.notice.config.NoticeSmsContent;
import com.rongchen.byh.common.api.notice.dto.NoticeSmsDto;
import com.rongchen.byh.common.api.notice.dto.SendAirH5Dto;
import com.rongchen.byh.common.core.util.MyModelUtil;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 空中放款支用h5
 * @date 2025/2/25 18:36:14
 */
@Service
public class SendAirH5NoticeService extends AbstractNoticeJsonSmsService {
    @Override
    protected String getContent(NoticeSmsDto noticeSmsDto) {
        SendAirH5Dto dto = (SendAirH5Dto) noticeSmsDto;
        return StrUtil.format(NoticeSmsContent.DICT_MAP.get(NoticeSmsContent.SENDAIRH5), MyModelUtil.beanToMap(dto));
    }

}
