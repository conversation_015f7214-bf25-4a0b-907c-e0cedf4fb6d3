logging:
  level:
    # 这里设置的日志级别优先于logback-spring.xml文件Loggers中的日志级别。
    com.rongchen.byh: info
  config: classpath:logback-spring.xml

server:
  port: 8088
  tomcat:
    uri-encoding: UTF-8
    threads:
      max: 100
      min-spare: 10
  servlet:
    encoding:
      force: true
      charset: UTF-8
      enabled: true

# spring相关配置
spring:
  application:
    name: application-app
  profiles:
    active: localdev
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
  mvc:
    converters:
      preferred-json-mapper: fastjson
  main:
    allow-circular-references: true
  groovy:
    template:
      check-template-location: false
nacos:
  config:
    bootstrap:
      #开启系统启动时预读取nacos的配置，用于满足@Value注入数据的场景
      enable: true
    #配置所属命名空间的id,此处我们配置名称为dev的id，可以在命名空间列表查看id的值
    namespace: 254a41f6-d70a-4358-8034-efc1be9bd8b7
    #配置所属分组
    group: DEFAULT_GROUP
    #配置ID
    data-id: qyc-test
    #配置文件类型,对应nacos配置页面的配置格式，默认是properties
    type: yaml
    #nacos服务器地址
    server-addr: **********:8848
    #开启自动刷新nacos配置
    auto-refresh: true
    #针对配置项同名的情况，是否允许nacos的配置覆盖本地的配置
    remote-first: true

# --------------------
# 外部合作伙伴 API 配置
# --------------------
partner:
  api:
    # 合作伙伴 API 基础 URL (请替换为实际值)
    baseUrl: https://partner.example.com/api
    # 合作方分配的 App ID (请替换为实际值)
    appId: YOUR_APP_ID_PLACEHOLDER
    # 我方 RSA 私钥 (Base64 编码, PKCS#8 格式) (请替换为实际值 - 警告: 不建议直接放配置文件!)
    privateKey: YOUR_BASE64_PRIVATE_KEY_PLACEHOLDER
    # 合作方 RSA 公钥 (Base64 编码, X.509 格式) (测试环境公钥)
    partnerPublicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCpNSmsx0DQ5Vyl2gj0Iu1vf1Zwyu5rJKUuLfXSqUAArg8J41/TProj5j2UZAU8WJK9u8G9U4sssRQSJzOplcvhplis9tYpGgzjnNA9NxbGntz0hPugr/zovEbazX3ga+V0X5gCaaT/MmBRBq9sTrPRU+W9A3OhlokSN8neAiiRYQIDAQAB

# --------------------
# 聚有渠道特定配置
# --------------------
juyou:
  # 聚有提供的 RSA 公钥 (Base64, X.509), 用于验签聚有请求 和 加密我们响应中的AES Key
  publicKey: JUYOU_RSA_PUBLIC_KEY_PLACEHOLDER
  # 我方提供的 RSA 私钥 (Base64, PKCS#8), 用于解密聚有请求中的AES Key 和 签名我们响应
  # 注意: 这可能与上面 partner.api.privateKey 相同或不同，取决于密钥管理策略
  privateKey: OUR_RSA_PRIVATE_KEY_FOR_JUYOU_PLACEHOLDER
  # 我方提供给聚有的 appId (如果需要，聚有文档中可能称为合作方 ID)
  # appId: OUR_APP_ID_FOR_JUYOU_PLACEHOLDER # 如果聚有文档中的 appId 指的是我方 ID，则取消注释并配置