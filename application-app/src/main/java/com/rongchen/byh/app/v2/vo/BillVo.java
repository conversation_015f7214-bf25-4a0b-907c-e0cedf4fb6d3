package com.rongchen.byh.app.v2.vo;

import com.rongchen.byh.app.v2.entity.ApiRepayPlan;
import lombok.Data;

import java.util.List;

@Data
public class BillVo {
    /** 订单编号 */
    private String order_no;

    /** 银行名称（中文名，非代码） */
    private String open_bank;

    /** 还款银行卡号 */
    private String bank_card;

    /** 放款总金额（单位：元） */
    private Float loan_amount;

    /** 放款成功时间（10位时间戳） */
    private Integer loan_success_time;

    /** 还款总金额（固定值，不随还款减少） */
    private Float total_amount;

    /** 还款计划（二维数组） */
    private List<ApiRepayPlan> repayment_plan;

    /** 结算人群类型（默认0，需提前沟通） */
    private Integer group_type = 0;

    /** 是否支持提前全部结清（多期产品需传，默认0=不支持） */
    private Integer can_prepay = 0;

    /** 可提前结清的开始时间（10位时间戳，支持提前结清时必传） */
    private Long can_prepay_time;

}
