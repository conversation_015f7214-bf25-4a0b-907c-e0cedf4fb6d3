package com.rongchen.byh.app.web.api.vo.juyou.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class JYCreditApplyDto {
    /**
     * 授信申请信息
     */
    @Schema(description = "授信申请信息")
    private CreditApplyInfo creditApplyInfo;

    /**
     * 用户基本信息
     */
    @Schema(description = "用户基本信息")
    private PersonalInfo personalInfo;

    /**
     * 用户身份证信息
     */
    @Schema(description = "用户身份证信息")
    private IdInfo idInfo;

    /**
     * 用户人脸信息
     */
    @Schema(description = "用户人脸信息")
    private FaceInfo faceInfo;

    /**
     * 公司信息
     */
    @Schema(description = "公司信息")
    private CompanyInfo companyInfo;

    /**
     * 联系人信息列表
     */
    @Schema(description = "联系人信息列表")
    private List<ContactInfo> contactInfoList;

    /**
     * 银行卡信息（可选，绑卡场景时提供）
     */
    @Schema(description = "银行卡信息（可选，绑卡场景时提供）")
    private BankCardInfo bankCardInfo;

    /**
     * 授信申请信息子对象
     */
    @Data
    public static class CreditApplyInfo {
        @Schema(description = "授信申请号，业务系统唯一标识")
        private String creditApplyNo;

        @Schema(description = "用户手机号，明文传输")
        private String mobile;

        @Schema(description = "申请时间，格式：yyyyMMddHHmmss")
        private String applyTime;

        @Schema(description = "借款用途，参考附录二（01：日常消费等）")
        private String loanUsage;
    }

    /**
     * 用户基本信息子对象
     */
    @Data
    public static class PersonalInfo {
        @Schema(description = "用户号，业务系统用户唯一标识")
        private String userId;

        @Schema(description = "婚姻状态，取值：0（其他）/1（未婚）/2（已婚）/3（丧偶）/4（离异）/9（未说明）")
        private String maritalStatus;

        @Schema(description = "职业类型，取值：A（国家机关负责人）/B（管理人员）等，详见附录二职业列表")
        private String profession;

        @Schema(description = "学历，取值：00（研究生及以上）/10（本科）等，详见附录二学历列表")
        private String education;

        @Schema(description = "性别，取值：0（女）/1（男）/2（其他）")
        private String sex;

        @Schema(description = "居住地-省，例如：北京市")
        private String liveProvince;

        @Schema(description = "居住地-市，例如：北京市")
        private String liveCity;

        @Schema(description = "居住地-区，例如：朝阳区")
        private String liveArea;

        @Schema(description = "居住地详细地址，例如：朝阳区XX街道XX号")
        private String liveAddress;

        @Schema(description = "家庭地址-省（可选），例如：上海市")
        private String homeProvince;

        @Schema(description = "家庭地址-市（可选），例如：上海市")
        private String homeCity;

        @Schema(description = "家庭地址-区（可选），例如：浦东新区")
        private String homeArea;

        @Schema(description = "家庭地址细地址（可选），例如：浦东新区XX街道XX号")
        private String homeAddress;

        @Schema(description = "月收入，取值：L6（20000以上）/L5（10001-20000）等，详见附录二月收入列表")
        private String monthIncome;

        @Schema(description = "用户来源标识（可选），业务系统自定义标识")
        private String sourceFlag;
    }

    /**
     * 用户身份证信息子对象
     */
    @Data
    public static class IdInfo {
        @Schema(description = "姓名，与身份证一致")
        private String idName;

        @Schema(description = "身份证号，18位数字")
        private String idNo;

        @Schema(description = "性别，取值：0（女）/1（男）/2（其他），需与身份证一致")
        private String sex;

        @Schema(description = "民族，例如：汉族")
        private String nation;

        @Schema(description = "身份证有效期开始日期，格式：yyyy.MM.dd（长期有效填2099.12.31）")
        private String idStartDate;

        @Schema(description = "身份证有效期结束日期，格式：yyyy.MM.dd（长期有效填2099.12.31）")
        private String idEndDate;

        @Schema(description = "身份证头像面图片URL，需可公开访问")
        private String idFrontUrl;

        @Schema(description = "身份证国徽面图片URL，需可公开访问")
        private String idBackUrl;

        @Schema(description = "签发机构，例如：北京市公安局朝阳分局")
        private String issuer;

        @Schema(description = "生日，格式：yyyy.MM.dd")
        private String birthday;

        @Schema(description = "身份证地址（可选），与身份证上地址一致")
        private String address;
    }

    /**
     * 用户人脸信息子对象
     */
    @Data
    public static class FaceInfo {
        @Schema(description = "人脸图片URL，需可公开访问")
        private String faceImageUrl;

        @Schema(description = "人脸识别服务商（可选），例如：阿里云FaceAPI")
        private String faceSupplier;

        @Schema(description = "人脸识别分（可选），范围0-100")
        private BigDecimal faceScore;
    }

    /**
     * 公司信息子对象
     */
    @Data
    public static class CompanyInfo {
        @Schema(description = "单位名称（可选），例如：XX科技有限公司")
        private String companyName;

        @Schema(description = "单位地址（可选），例如：北京市朝阳区XX号")
        private String companyAddress;

        @Schema(description = "单位固话（可选），格式：区号-号码（例如：010-12345678）")
        private String companyPhone;

        @Schema(description = "单位邮箱（可选），例如：<EMAIL>")
        private String companyEmail;

        @Schema(description = "单位地址省名称（可选），例如：北京市")
        private String companyProvinceName;

        @Schema(description = "单位地址市名称（可选），例如：北京市")
        private String companyCityName;

        @Schema(description = "单位地址区名称（可选），例如：朝阳区")
        private String companyAreaName;

        @Schema(description = "部门（可选），例如：技术部")
        private String department;

        @Schema(description = "行业类型（可选），取值：A（农、林、牧、渔业）/B（采掘业）等，详见附录二行业列表")
        private String industry;

        @Schema(description = "公司性质（可选），取值：A（机关事业单位）/F（国有企业）等，详见附录二公司性质列表")
        private String companyNature;
    }

    /**
     * 联系人信息子对象
     */
    @Data
    public static class ContactInfo {
        @Schema(description = "联系人手机，11位数字")
        private String contactMobile;

        @Schema(description = "联系人姓名，真实姓名")
        private String contactName;

        @Schema(description = "联系人关系，取值：0（配偶）/1（父母）/2（子女）等，详见附录二联系人关系列表")
        private String contactRelation;
    }

    /**
     * 银行卡信息子对象（可选）
     */
    @Data
    public static class BankCardInfo {
        @Schema(description = "银行名称（可选），例如：中国工商银行")
        private String bankName;

        @Schema(description = "银行编码（可选），参考附录三银行列表，例如：ICBC")
        private String bankCode;

        @Schema(description = "银行卡号（可选），16-19位数字")
        private String cardNo;

        @Schema(description = "预留手机号（可选），需与银行开户时一致")
        private String cardPhone;
    }
}