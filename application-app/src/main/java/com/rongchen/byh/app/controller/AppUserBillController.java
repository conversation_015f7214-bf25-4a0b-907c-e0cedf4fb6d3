package com.rongchen.byh.app.controller;

import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.rongchen.byh.app.dto.RepaymentDto;
import com.rongchen.byh.app.dto.app.BillDetailDto;
import com.rongchen.byh.app.dto.app.BillRepayApplyDto;
import com.rongchen.byh.app.service.AppUserBillService;
import com.rongchen.byh.app.vo.app.BillDetailVo;
import com.rongchen.byh.app.vo.app.BillListVo;
import com.rongchen.byh.app.vo.app.BillRepayResultVo;
import com.rongchen.byh.app.vo.app.RepaymentDetailVo;
import com.rongchen.byh.common.core.object.ResponseResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description app 账单相关接口
 * @date 2024/12/14 16:22:55
 */
@Tag(name = "app 账单相关接口")
@ApiSupport(order = 3)
@RestController
@RequestMapping("/userApi/bill")
public class AppUserBillController {

    @Resource
    AppUserBillService appUserBillService;

    @Operation(summary = "账单列表")
    @PostMapping("/billList")
    public ResponseResult<List<BillListVo>> billList() {
        return appUserBillService.billList();
    }

    @Operation(summary = "账单还款详情")
    @PostMapping("/billDetailList")
    public ResponseResult<BillDetailVo> billDetailList(@RequestBody BillDetailDto billDetailDto) {
        return appUserBillService.billDetail(billDetailDto);
    }

    @Operation(summary = "当期账单还款申请")
    @PostMapping("/repayment")
    public ResponseResult repayment(@RequestBody RepaymentDto repaymentDto) {
        return appUserBillService.billApply(repaymentDto);
    }

    @Operation(summary = "当期账单还款信息详情")
    @PostMapping("/repaymentDetail")
    public ResponseResult<RepaymentDetailVo> repaymentDetail(@RequestBody RepaymentDto repaymentDto) {
        return appUserBillService.repaymentDetail(repaymentDto);
    }

    @Operation(summary = "查询还款结果")
    @PostMapping("/getRepaymentResult")
    public ResponseResult<BillRepayResultVo> getRepaymentResult(@RequestBody RepaymentDto repaymentDto) {
        return appUserBillService.getBillRepaymentResult(repaymentDto);
    }
}
