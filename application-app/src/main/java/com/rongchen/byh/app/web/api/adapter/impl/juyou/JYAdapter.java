package com.rongchen.byh.app.web.api.adapter.impl.juyou;

import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.app.exceptions.ExternalApiException;
import com.rongchen.byh.app.service.AdmittanceCheckService;
import com.rongchen.byh.app.web.api.adapter.AbstractChannelAdapter;
import com.rongchen.byh.app.web.api.adapter.impl.juyou.config.JuyouApiConfig;
import com.rongchen.byh.app.web.api.adapter.impl.juyou.utils.CryptoUtils;
import com.rongchen.byh.app.web.api.adapter.impl.juyou.utils.SignatureUtils;
import com.rongchen.byh.app.web.api.common.ApiChannel;
import com.rongchen.byh.app.web.api.common.ApiOperation;
import com.rongchen.byh.app.web.api.service.juyou.JYCardService;
import com.rongchen.byh.app.web.api.service.juyou.JYCreditSerivce;
import com.rongchen.byh.app.web.api.vo.juyou.req.JYQueryDto;
import com.rongchen.byh.app.web.api.vo.juyou.req.JuyouUserAccessReqVo;
import com.rongchen.byh.app.web.api.vo.juyou.res.JYCreditQueryLimitVo;
import com.rongchen.byh.app.web.api.vo.juyou.res.JYCreditQueryResultVo;
import com.rongchen.byh.app.web.api.vo.juyou.res.JuyouUserAccessResVo;
import com.rongchen.byh.common.core.util.JsonUtils;
import java.nio.charset.StandardCharsets;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;

/**
 * 聚友渠道 - 适配器实现
 * <p>
 * 通过组合方式注入具体的业务操作处理器 (Handler)。
 */
@Slf4j
@Component
public class JYAdapter extends AbstractChannelAdapter {

    @Resource
    JYCreditSerivce jyCreditSerivce;

    @Resource
    JYCardService jyCardSerivce;

    @Resource
    AdmittanceCheckService admittanceCheckService;

    @Resource
    private JuyouApiConfig juyouApiConfig;

    /**
     * 构造函数。 初始化适配器并设置其支持的渠道代码。
     *
     * @throws IllegalArgumentException 如果渠道代码为 null 或空。
     */
    protected JYAdapter() {
        super(ApiChannel.JUYOU.getCode());
    }

    /**
     * -- GETTER -- 获取此适配器支持的渠道代码 (小写)
     *
     * @return 渠道代码 (例如 "JUYOU")
     */
    @Override
    public String getSupportedChannelCode() {
        log.info("渠道[{}] 初始化完成，已设置 Credit Handler.", ApiChannel.JUYOU.getCode());
        return ApiChannel.JUYOU.getCode();
    }

    /**
     * 前置处理 - 处理聚有请求
     * <p>
     * 根据聚有文档，实现请求的解析、验签和解密。
     * <ul>
     * <li>请求参数包括：appId, requestNo, method, data, key, sign, version,
     * requestTime</li>
     * <li>协议要求：data字段为AES加密，key为RSA加密的AES密钥，sign为SHA256withRSA签名</li>
     * <li>当前实现：仅做参数校验，未实现实际加解密和验签（需在TODO处补充）</li>
     * </ul>
     *
     * @param channel   渠道标识（如"JUYOU"）
     * @param operation 操作类型（如user.access）
     * @param request   HTTP请求对象，body为JSON格式
     * @return 解析后的请求数据（JSONObject），包含原始参数
     * @throws Exception 参数校验失败、JSON解析失败等
     */
    @Override
    protected JSONObject preProcess(String channel, ApiOperation operation, HttpServletRequest request)
        throws Exception {
        log.info("[{}] JUYOU preProcess for operation: {}", channel, operation.getCode());
        String requestBody = StreamUtils.copyToString(request.getInputStream(), StandardCharsets.UTF_8);
        log.debug("[{}] JUYOU original request body: {}", channel, requestBody);

        // 1. 解析请求JSON
        JSONObject requestJson;
        try {
            requestJson = JSONObject.parseObject(requestBody);
        } catch (Exception e) {
            log.error("[{}] JUYOU 请求JSON解析失败: {}，原始内容: {}", channel, e.getMessage(), requestBody);
            throw new IllegalArgumentException("请求格式错误: 非法的JSON格式", e);
        }

        // 2. 基本参数校验
        validateRequiredParams(requestJson, channel);

        // 3. 验证签名
        String sign = requestJson.getString("sign");
        // 构建签名源字符串（升序，去除sign）
        java.util.Map<String, Object> signParams = new java.util.HashMap<>(requestJson);
        signParams.remove("sign");
        String signString = SignatureUtils.buildSignString(signParams);
        boolean isSignValid = SignatureUtils.verifySHA256withRSA(signString, sign, juyouApiConfig.getPublicKey());
        if (!isSignValid) {
            log.error("[{}] JUYOU 请求签名验证失败, requestNo={}", channel, requestJson.getString("requestNo"));
            throw new IllegalArgumentException("签名验证失败");
        }

        // 4. 解密数据
        String encryptedKey = requestJson.getString("key");
        String aesKey = CryptoUtils.decryptRSA(encryptedKey,
            juyouApiConfig.getPrivateKey());
        String encryptedData = requestJson.getString("data");
        String decryptedData = CryptoUtils.decryptAES(encryptedData, aesKey);
        requestJson.put("data", decryptedData);

        log.info("[{}] JUYOU preProcess completed, requestNo={}, method={}", channel,
            requestJson.getString("requestNo"), requestJson.getString("method"));
        return requestJson;
    }

    /**
     * 验证请求中的必要参数
     *
     * @param requestJson 请求JSON
     * @param channel     渠道标识
     * @throws IllegalArgumentException 如果缺少必要参数
     */
    private void validateRequiredParams(JSONObject requestJson, String channel) {
        String[] requiredParams = {"appId", "requestNo", "method", "data", "key", "sign", "version", "requestTime"};
        for (String param : requiredParams) {
            if (!requestJson.containsKey(param) || requestJson.getString(param) == null
                || requestJson.getString(param).isEmpty()) {
                log.error("[{}] JUYOU 请求缺少必要参数: {}", channel, param);
                throw new IllegalArgumentException("请求缺少必要参数: " + param);
            }
        }
    }

    /**
     * 后置处理 - 处理聚有响应
     * <p>
     * 根据聚有文档，实现响应的加密和签名。
     * <ul>
     * <li>响应参数包括：code, msg, data, sign, key, responseTime</li>
     * <li>协议要求：data字段为AES加密，key为RSA加密的AES密钥，sign为SHA256withRSA签名</li>
     * <li>当前实现：未做实际加密和签名，仅返回明文data（需在TODO处补充）</li>
     * </ul>
     *
     * @param channel        渠道标识
     * @param operation      操作类型
     * @param businessResult 业务处理结果（明文JSON）
     * @return 最终响应数据（JSONObject），包含协议所有字段
     * @throws Exception 处理异常
     */
    @Override
    protected JSONObject postProcess(String channel, ApiOperation operation, JSONObject businessResult)
        throws Exception {
        log.info("[{}] JUYOU postProcess for operation: {}, businessResult: {}", channel, operation.getCode(),
            businessResult);

        // 1. 确保有效的业务结果
        JSONObject result = (businessResult != null) ? businessResult : new JSONObject();

        // 2. 构建最终响应结构
        JSONObject finalResponse = new JSONObject();
        finalResponse.put("code", "0000"); // 成功的错误码
        finalResponse.put("msg", "success"); // 成功的描述
        finalResponse.put("responseTime", System.currentTimeMillis()); // 当前时间戳

        // 3. 加密数据
        String aesKey = CryptoUtils.generateRandomAesKey();
        String encryptedData = CryptoUtils.encryptAES(result.toJSONString(), aesKey);
        String encryptedKey = CryptoUtils.encryptRSA(aesKey, juyouApiConfig.getPublicKey());
        finalResponse.put("data", encryptedData);
        finalResponse.put("key", encryptedKey);

        // 4. 生成签名
        java.util.Map<String, Object> signParams = new java.util.HashMap<>(finalResponse);
        String signString = SignatureUtils.buildSignString(signParams);
        String signature = SignatureUtils.signSHA256withRSA(signString, juyouApiConfig.getPrivateKey());
        finalResponse.put("sign", signature);

        log.info("[{}] JUYOU postProcess completed, responseTime={}, method={}", channel,
            finalResponse.getLong("responseTime"), operation.getCode());
        return finalResponse;
    }

    /**
     * 用户准入检查（聚有 user.access）
     * <p>
     * 实现聚有用户准入接口，参数和返回值严格遵循聚有协议，逻辑以我方准入服务为准。
     * <ul>
     * </ul>
     *
     * @param channel              渠道标识
     * @param processedRequestData 预处理后的请求数据（含data字段）
     * @return 准入结果（JSONObject），字段access/msg符合聚有协议
     * @throws Exception 参数校验、反序列化、准入逻辑异常
     */
    @Override
    public JSONObject userAccess(String channel, JSONObject processedRequestData) throws Exception {
        log.info("[{}] Handler: Executing JUYOU userAccess logic... Data: {}", channel, processedRequestData);
        try {
            // 1. 解析请求参数
            String requestDataStr = processedRequestData.getString("data");
            if (requestDataStr == null || requestDataStr.isEmpty()) {
                log.error("[{}] 用户准入检查失败: 请求数据为空", channel);
                throw new ExternalApiException("请求数据为空");
            }

            // 2. 将请求数据转换为VO对象
            JuyouUserAccessReqVo reqVo;
            try {
                reqVo = JsonUtils.parseObject(requestDataStr, JuyouUserAccessReqVo.class);
            } catch (Exception e) {
                log.error("[{}] 用户准入检查失败: 请求数据解析失败 - {}", channel, e.getMessage());
                throw new ExternalApiException("请求数据格式错误");
            }

            // 3. 参数校验 - 至少需要手机号或身份证号之一
            if (reqVo != null && (reqVo.getMobileNoMd5() == null || reqVo.getMobileNoMd5().isEmpty()) &&
                (reqVo.getIdNoMd5() == null || reqVo.getIdNoMd5().isEmpty())) {
                log.error("[{}] 用户准入检查失败: 手机号MD5和身份证MD5至少需要提供一个", channel);
                throw new ExternalApiException("手机号MD5和身份证MD5至少需要提供一个");
            }

            // 4. 执行准入检查逻辑
            boolean isRejected = admittanceCheckService.isRejected(null, reqVo.getIdNoMd5(), reqVo.getMobileNoMd5());

            // 5. 构建响应
            JuyouUserAccessResVo resVo = new JuyouUserAccessResVo();
            if (isRejected) {
                // 拒绝
                resVo.setAccess("1");
                resVo.setMsg("用户不符合准入条件");
            } else {
                // 通过
                resVo.setAccess("0");
                resVo.setMsg(null);
            }
            JSONObject result = JsonUtils.convertValue(resVo, JSONObject.class);
            log.info("[{}] 用户准入检查完成: 结果={}", channel, result);
            return result;
        } catch (Exception e) {
            log.error("[{}] 用户准入检查异常: {}", channel, e.getMessage(), e);
            // 统一异常处理，返回聚有协议错误码
            return buildErrorResponse(null, e.getMessage(), e);
        }
    }

    /**
     * 信用申请
     *
     * @param channel
     * @param processedRequestData
     * @return
     * @throws Exception
     */
    @Override
    public JSONObject creditApply(String channel, JSONObject processedRequestData) throws Exception {
        log.info("[{}] Handler: Executing JUYOU creditApply logic... Data: {}", channel, processedRequestData);
        try {
            String requestDataStr = processedRequestData.getString("data");
            if (requestDataStr == null || requestDataStr.isEmpty()) {
                log.error("[{}] 信用申请检查失败: 请求数据为空", channel);
                throw new ExternalApiException("请求数据为空");
            }
            JSONObject creditApply = jyCreditSerivce.creditApply(processedRequestData, channel);
            log.info("[{}] Handler: Internal service (creditApply) result: {}", channel, creditApply);
            return creditApply;
        } catch (Exception e) {
            log.error("[{}] 信用申请检查异常: {}", channel, e.getMessage(), e);
            // 统一异常处理，返回聚有协议错误码
            return buildErrorResponse(null, e.getMessage(), e);
        }
    }

    /**
     * 授信结果查询
     *
     * @param channel
     * @param processedRequestData
     * @return
     * @throws Exception
     */
    @Override
    public JSONObject creditQueryResult(String channel, JSONObject processedRequestData) throws Exception {
        log.info("[{}] Handler: Executing JUYOU creditQueryResult logic... Data: {}", channel,
                processedRequestData);
        try {
            String requestDataStr = processedRequestData.getString("data");
            if (requestDataStr == null || requestDataStr.isEmpty()) {
                log.error("[{}] 授信结果查询: 请求数据为空", channel);
                throw new ExternalApiException("请求数据为空");
            }
            JYQueryDto jyQueryDto = JsonUtils.parseObject(requestDataStr, JYQueryDto.class);
            if (jyQueryDto != null && StringUtils.isBlank(jyQueryDto.getCreditApplyNo())) {
                log.error("[{}] 授信结果查询: 请求数据为空", channel);
                throw new ExternalApiException("9901", "授信申请流水号 为空");
            }
            JYCreditQueryResultVo jyCreditQueryResultVo = jyCreditSerivce.creditQueryResult(jyQueryDto.getCreditApplyNo(), channel);
            log.info("[{}] Handler: Internal service (creditQueryResult) result: {}", channel, requestDataStr);
            return JsonUtils.convertValue(jyCreditQueryResultVo, JSONObject.class);
        } catch (Exception e) {
            log.error("[{}] 信用申请检查异常: {}", channel, e.getMessage(), e);
            // 统一异常处理，返回聚有协议错误码
            return buildErrorResponse(null, e.getMessage(), e);
        }
    }

    /**
     * 授信额度查询
     *
     * @param channel
     * @param processedRequestData
     * @return
     * @throws Exception
     */
    @Override
    public JSONObject creditLimitQuery(String channel, JSONObject processedRequestData) throws Exception {
        log.info("[{}] Handler: Executing JUYOU creditLimitQuery logic... Data: {}", channel,
            processedRequestData);
        try {
            String requestDataStr = processedRequestData.getString("data");
            if (requestDataStr == null || requestDataStr.isEmpty()) {
                log.error("[{}] 授信额度查询: 请求数据为空", channel);
                throw new ExternalApiException("请求数据为空");
            }
            JYQueryDto jyQueryDto = JsonUtils.parseObject(requestDataStr, JYQueryDto.class);
            if (jyQueryDto != null && StringUtils.isBlank(jyQueryDto.getCreditApplyNo())) {
                log.error("[{}] 授信额度查询: 请求数据为空", channel);
                throw new ExternalApiException("9901", "授信申请流水号 为空");
            }
            JYCreditQueryLimitVo jyCreditQueryLimitVo = jyCreditSerivce.creditQueryLimit(jyQueryDto.getCreditApplyNo(), channel);
            log.info("[{}] Handler: Internal service (creditLimitQuery) result: {}", channel,
                    requestDataStr);
            return JsonUtils.convertValue(jyCreditQueryLimitVo, JSONObject.class);
        } catch (ExternalApiException e) {
            log.error("[{}] 授信额度查询 检查异常: {}", channel, e.getMessage(), e);
            // 统一异常处理，返回聚有协议错误码
            return buildErrorResponse(null, e.getMessage(), e);
        }
    }

    /**
     * 处理借款试算的核心逻辑。
     * <p>
     * 子类应覆盖此方法以实现具体的借款试算处理。 默认实现抛出 {@link UnsupportedOperationException}。
     *
     * @param channel              渠道标识
     * @param processedRequestData 经过 preProcess 处理后的请求数据
     * @return 借款试算的处理结果
     * @throws Exception 处理过程中可能发生的异常
     */
    @Override
    protected JSONObject loanTrial(String channel, JSONObject processedRequestData) throws Exception {
        return super.loanTrial(channel, processedRequestData);
    }

    /**
     * 处理借款申请的核心逻辑。
     * <p>
     * 子类应覆盖此方法以实现具体的借款申请处理。 默认实现抛出 {@link UnsupportedOperationException}。
     *
     * @param channel              渠道标识
     * @param processedRequestData 经过 preProcess 处理后的请求数据
     * @return 借款申请的处理结果
     * @throws Exception 处理过程中可能发生的异常
     */
    @Override
    protected JSONObject loanApply(String channel, JSONObject processedRequestData) throws Exception {
        return super.loanApply(channel, processedRequestData);
    }

    /**
     * 处理借款结果查询的核心逻辑。
     * <p>
     * 子类应覆盖此方法以实现具体的借款结果查询处理。 默认实现抛出 {@link UnsupportedOperationException}。
     *
     * @param channel              渠道标识
     * @param processedRequestData 经过 preProcess 处理后的请求数据
     * @re turn 借款结果查询的处理结果
     * @throws Exception 处理过程中可能发生的异常
     */
    @Override
    protected JSONObject loanResultQuery(String channel, JSONObject processedRequestData) throws Exception {
        // 查询用户状态是放款中或者审批通过的订单
        return super.loanResultQuery(channel, processedRequestData);
    }

    /**
     * 处理还款申请 (主动/预还款) 的核心逻辑。
     * <p>
     * 子类应覆盖此方法以实现具体的还款申请处理。 默认实现抛出 {@link UnsupportedOperationException}。
     *
     * @param channel              渠道标识
     * @param processedRequestData 经过 preProcess 处理后的请求数据
     * @return 还款申请的处理结果
     * @throws Exception 处理过程中可能发生的异常
     */
    @Override
    protected JSONObject repayApply(String channel, JSONObject processedRequestData) throws Exception {
        return super.repayApply(channel, processedRequestData);
    }

    /**
     * 处理还款计划查询的核心逻辑。
     * <p>
     * 子类应覆盖此方法以实现具体的还款计划查询处理。 默认实现抛出 {@link UnsupportedOperationException}。
     *
     * @param channel              渠道标识
     * @param processedRequestData 经过 preProcess 处理后的请求数据
     * @return 还款计划查询的处理结果
     * @throws Exception 处理过程中可能发生的异常
     */
    @Override
    protected JSONObject repayPlanQuery(String channel, JSONObject processedRequestData) throws Exception {
        JSONObject resultObject = new JSONObject();
        return null;
    }

    /**
     * 处理还款结果查询的核心逻辑。
     * <p>
     * 子类应覆盖此方法以实现具体的还款结果查询处理。 默认实现抛出 {@link UnsupportedOperationException}。
     *
     * @param channel              渠道标识
     * @param processedRequestData 经过 preProcess 处理后的请求数据
     * @return 还款结果查询的处理结果
     * @throws Exception 处理过程中可能发生的异常
     */
    @Override
    protected JSONObject repayResultQuery(String channel, JSONObject processedRequestData) throws Exception {
        return super.repayResultQuery(channel, processedRequestData);
    }
    // === 特殊接口 Special ===

    /**
     * 处理获取借款 H5 页面 URL 的核心逻辑。
     * <p>
     * 子类应覆盖此方法。默认抛出 {@link UnsupportedOperationException}。
     * </p>
     *
     * @param channel              渠道标识
     * @param processedRequestData 处理后的请求数据
     * @return 获取借款 H5 URL 的处理结果
     * @throws Exception 处理异常
     */
    @Override
    protected JSONObject getLoanH5Url(String channel, JSONObject processedRequestData) throws Exception {
        JSONObject jsonObject = new JSONObject();
        return jsonObject;
    }

    /**
     * 处理获取还款 H5 页面 URL 的核心逻辑。
     * <p>
     * 子类应覆盖此方法。默认抛出 {@link UnsupportedOperationException}。
     * </p>
     *
     * @param channel              渠道标识
     * @param processedRequestData 处理后的请求数据
     * @return 获取还款 H5 URL 的处理结果
     * @throws Exception 处理异常
     */
    @Override
    protected JSONObject getRepayH5Url(String channel, JSONObject processedRequestData) throws Exception {
        JSONObject jsonObject = new JSONObject();
        return jsonObject;
    }

    /**
     * 获取订单状态
     */
    @Override
    protected JSONObject orderStatus(String channel, JSONObject processedRequestData) throws Exception {
        JSONObject resultObject = new JSONObject();
        return resultObject;
    }

    /**
     * 处理还款试算的核心逻辑。
     * <p>
     * 子类应覆盖此方法。默认抛出 {@link UnsupportedOperationException}。
     * </p>
     *
     * @param channel              渠道标识
     * @param processedRequestData 处理后的请求数据
     * @return 还款试算结果
     * @throws Exception 处理异常
     */
    @Override
    protected JSONObject repayTrial(String channel, JSONObject processedRequestData) throws Exception {
        return super.repayTrial(channel, processedRequestData);
    }

    /**
     * 处理支持银行卡列表查询的核心 辑。
     * <p>
     * 子类应覆盖此方法。默认抛出 {@link UnsupportedOperationException}。
     * </p>
     *
     * @param channel              渠道标识
     * @param processedRequestData 处理后的请求数据
     * @return 查询结果
     */
    @Override
    protected JSONObject cardSupportList(String channel, JSONObject processedRequestData) throws Exception {
        log.info("[{}] Handler: Executing JUYOU cardSupportList logic... Data: {}", channel, processedRequestData);
        try {
            String requestDataStr = processedRequestData.getString("data");
            if (requestDataStr == null || requestDataStr.isEmpty()) {
                log.error("[{}] 银行卡列表查询: 请求数据为空", channel);
                throw new ExternalApiException("请求数据为空");
            }
            JYQueryDto jyQueryDto = JsonUtils.parseObject(requestDataStr, JYQueryDto.class);
            if (jyQueryDto != null && StringUtils.isBlank(jyQueryDto.getCreditApplyNo())) {
                log.error("[{}] 银行卡列表查询: 请求数据为空", channel);
                throw new ExternalApiException("9901", "授信申请流水号 为空");
            }
            JSONObject object = jyCardSerivce.cardSupportList(jyQueryDto.getCreditApplyNo());
            log.info("[{}] Handler: Internal service (cardSupportList) result: {}", channel,
                    requestDataStr);
            return JsonUtils.convertValue(object, JSONObject.class);
        } catch (ExternalApiException e) {
            log.error("[{}] 银行卡列表查询 检查异常: {}", channel, e.getMessage(), e);
            // 统一异常处理，返回聚有协议错误码
            return buildErrorResponse(null, e.getMessage(), e);
        }
    }

    /**
     * 处理绑卡短信发送的核心逻辑。
     * <p>
     * 子类应覆盖此方法。默认抛出 {@link UnsupportedOperationException}。
     * </p>
     *
     * @param channel              渠道标识
     * @param processedRequestData 处理后的请求数据
     * @return 短信发送结果
     */
    @Override
    protected JSONObject cardBindSms(String channel, JSONObject processedRequestData) throws Exception {
        log.info("[{}] Handler: Executing JUYOU cardBindSms logic... Data: {}", channel, processedRequestData);
        try {
            String requestDataStr = processedRequestData.getString("data");
            if (requestDataStr == null || requestDataStr.isEmpty()) {
                log.error("[{}] 银行卡列表查询: 请求数据为空", channel);
                throw new ExternalApiException("请求数据为空");
            }
            JYQueryDto jyQueryDto = JsonUtils.parseObject(requestDataStr, JYQueryDto.class);
            if (jyQueryDto != null && StringUtils.isBlank(jyQueryDto.getCreditApplyNo())) {
                log.error("[{}] 银行卡列表查询: 请求数据为空", channel);
                throw new ExternalApiException("9901", "授信申请流水号 为空");
            }
            JSONObject object = jyCardSerivce.cardSupportList(jyQueryDto.getCreditApplyNo());
            log.info("[{}] Handler: Internal service (cardBindSms) result: {}", channel,
                    requestDataStr);
            return JsonUtils.convertValue(object, JSONObject.class);
        } catch (ExternalApiException e) {
            log.error("[{}] 银行卡列表查询 检查异常: {}", channel, e.getMessage(), e);
            // 统一异常处理，返回聚有协议错误码
            return buildErrorResponse(null, e.getMessage(), e);
        }
    }

    /**
     * 处理绑卡提交验证的核心逻辑。
     * <p>
     * 子类应覆盖此方法。默认抛出 {@link UnsupportedOperationException}。
     * </p>
     *
     * @param channel              渠道标识
     * @param processedRequestData 处理后的请求数据
     * @return 绑卡验证结果
     */
    @Override
    protected JSONObject cardBindValidate(String channel, JSONObject processedRequestData) throws Exception {
        return super.cardBindValidate(channel, processedRequestData);
    }

    /**
     * 处理银行卡绑卡查询的核心逻辑
     * <p>
     * 子类应覆盖此方法。默认抛出 {@link UnsupportedOperationException}。
     * </p>
     *
     * @param channel              渠道标识
     * @param processedRequestData 处理后的请求数据
     * @return 查询结果
     */
    @Override
    protected JSONObject cardBindQuery(String channel, JSONObject processedRequestData) throws Exception {
        return super.cardBindQuery(channel, processedRequestData);
    }

    /**
     * 处理已绑卡列表查询的核心逻辑
     * <p>
     * 子类应覆盖此方法。默认抛出 {@link UnsupportedOperationException}。
     * </p>
     *
     * @param channel              渠道标识
     * @param processedRequestData 处理后的请求数据
     * @return 列表结果
     */
    @Override
    protected JSONObject cardBindList(String channel, JSONObject processedRequestData) throws Exception {
        return super.cardBindList(channel, processedRequestData);
    }

    /**
     * 构建错误响应
     * <p>
     * 根据聚有文档，实现错误响应的构建。
     * 错误码定义：
     * - 0000: 成功
     * - 9901: 参数错误
     * - 9903: 交易不存在
     * - 9910: 数据解密失败
     * - 9911: 加密失败
     * - 9912: 解密密钥key失败
     * - 9913: 密钥key加密失
     * - 9914: 验签失败
     * - 9915: 签名失败
     * - 9999: 系统错误
     *
     * @param errorCode    错误码
     * @param errorMessage 错误消息
     * @param exception    异常信息
     * @return 错误响应JSON对象
     */
    @Override
    public JSONObject buildErrorResponse(String errorCode, String errorMessage, Exception exception) {
        // 根据异常类型映射到聚有的错误码
        String juyouErrorCode;
        String juyouErrorMsg;

        // 根据异常类型或错误码映射到聚有的错误码
        if (exception instanceof IllegalArgumentException) {
            // 参数错误
            juyouErrorCode = "9901";
            juyouErrorMsg = errorMessage != null ? errorMessage : "参数错误";
        } else if (errorCode != null && errorCode.contains("DECRYPT")) {
            // 解密失败
            juyouErrorCode = "9910";
            juyouErrorMsg = "数据解密失败";
        } else if (errorCode != null && errorCode.contains("ENCRYPT")) {
            // 加密失败
            juyouErrorCode = "9911";
            juyouErrorMsg = "加密失败";
        } else if (errorCode != null && errorCode.contains("SIGNATURE")) {
            // 签名验证失败
            juyouErrorCode = "9914";
            juyouErrorMsg = "验签失败";
        } else {
            // 其他系统错误
            juyouErrorCode = "9999";
            juyouErrorMsg = errorMessage != null ? errorMessage : "系统错误";
        }

        // 构建错误响应
        JSONObject result = new JSONObject();
        result.put("code", juyouErrorCode);
        result.put("msg", juyouErrorMsg);
        result.put("data", new JSONObject());
        result.put("responseTime", System.currentTimeMillis());

        log.error("[聚有渠道] 错误响应: code={}, msg={}, 原始错误={}",
            juyouErrorCode, juyouErrorMsg, errorMessage);

        return result;
    }

    /**
     * 构建一个通用的成功响应 JSONObject。
     * <p>
     * 根据聚有文档，实现成功响应的构建。
     * 成功响应格式：
     * - code: "0000" (成功的错误码)
     * - msg: "success" (成功的描述)
     * - data: 业务数据 (实际实现中需要加密)
     * <p>
     * 这个方法的返回值随后将传递给 {@link #postProcess} 方法进行最终处理 (例如签名、加密)。
     *
     * @param channel        渠道标识
     * @param operation      操作类型
     * @param businessResult 核心业务逻辑的执行结果
     * @return 结构化的成功响应，准备被进一步处理 (签名、加密等)
     */
    @Override
    protected JSONObject buildSuccessResponse(String channel, ApiOperation operation, JSONObject businessResult) {
        // 根据聚有文档构建成功响应结构
        JSONObject result = new JSONObject();
        result.put("code", "0000"); // 成功的错误码
        result.put("msg", "success"); // 成功的描述

        // 如果业务结果为空，使用空JSON对象
        JSONObject data = (businessResult != null) ? businessResult : new JSONObject();
        result.put("data", data);

        log.info("[聚有渠道] 构建成功响应: operation={}, data={}",
            operation.getCode(), data);

        return result;
    }
}
