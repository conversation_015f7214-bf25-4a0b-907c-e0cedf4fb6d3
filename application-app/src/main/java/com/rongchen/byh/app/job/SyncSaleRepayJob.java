package com.rongchen.byh.app.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rongchen.byh.app.config.ZifangFactory;
import com.rongchen.byh.app.constant.ZiFangBeanConstant;
import com.rongchen.byh.app.dao.CapitalDataMapper;
import com.rongchen.byh.app.dao.DisburseDataMapper;
import com.rongchen.byh.app.dao.SaleScheduleMapper;
import com.rongchen.byh.app.entity.CapitalData;
import com.rongchen.byh.app.entity.DisburseData;
import com.rongchen.byh.app.entity.SaleSchedule;
import com.rongchen.byh.app.service.SaleScheduleService;
import com.rongchen.byh.app.utils.NumberUtil;
import com.rongchen.byh.common.api.zifang.dto.SaleRepayPlanDto;
import com.rongchen.byh.common.api.zifang.service.OtherApi;
import com.rongchen.byh.common.api.zifang.vo.SaleRepayPlanRepayVo;
import com.rongchen.byh.common.api.zifang.vo.SaleRepayPlanVo;
import com.rongchen.byh.common.core.constant.SettleFlagConstant;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.MDCUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 同步赊销还款计划定时任务
 * @date 2025/5/16 13:46:27
 */
@Component
@Slf4j
public class SyncSaleRepayJob {
    @Resource
    private SaleScheduleMapper saleScheduleMapper;
    @Resource
    private CapitalDataMapper capitalDataMapper;
    @Resource
    private ZifangFactory zifangFactory;
    @Resource
    private SaleScheduleService saleScheduleService;
    @Resource
    private DisburseDataMapper disburseDataMapper;
    @Resource
    SyncSaleRepayJob syncSaleRepayJob;


    String JOB_NAME = "赊销同步还款计划定时任务";

    @XxlJob("syncSaleRepayJobHandler")
    public void syncSaleRepayJobHandler() {
        try {
            log.info("【{}】手动同步任务处理完成。", JOB_NAME);
            MDCUtil.setTraceId(IdUtil.fastSimpleUUID());
            // 需要更新的disburse_id，使用逗号拼接
            // 格式 1,2,3
            String param = XxlJobHelper.getJobParam();
            List<Long> list = null;
            // disburse_id 为空，查询所有订单
            if (StrUtil.isEmpty(param)) {
                list = saleScheduleMapper.selectDisburseIdList(1);
            } else {
                List<String> split = StrUtil.split(param, ",");
                list = split.stream().map(Long::parseLong).collect(Collectors.toList());
            }
            if (CollectionUtil.isEmpty(list)) {
                log.info("【{}】未提供或查询到需要同步的 disburse_id 列表数据为空", JOB_NAME);
                return;
            }
            LambdaQueryWrapper<DisburseData> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(DisburseData::getId, list);
            List<DisburseData> disburseDataList = disburseDataMapper.selectList(queryWrapper);

            log.info("【{}】查询到 {} 条支用记录需要处理。", JOB_NAME, disburseDataList.size());
            disburseDataList.forEach(data -> {
                this.syncSaleRepayPlan(data);
            });
            log.info("【{}】手动同步任务处理完成。", JOB_NAME);
        } catch (Exception e) {
            log.error("【{}】执行异常", JOB_NAME, e);
        } finally {
            // 清除MDC上下文
            MDCUtil.clear();
        }
    }


    public void syncSaleRepayPlan(DisburseData disburseData) {
        // 获取我方的还款计划
        List<SaleSchedule> saleScheduleList = saleScheduleMapper.selectByDisburseId(disburseData.getId());
        if (CollUtil.isEmpty(saleScheduleList)) {
            log.info("===========没有查询到该笔订单的还款计划，提前结束======");
            return;
        }
        String saleNo = disburseData.getSaleNo();
        // 获取资方的还款计划
        CapitalData capitalData = capitalDataMapper.selectById(disburseData.getCapitalId());
        if (ObjectUtil.isEmpty(capitalData)) {
            log.warn("【{}】借款编号 [{}] 资方未匹配 (Capital ID: {}), 跳过处理", JOB_NAME, saleNo, disburseData.getCapitalId());
            return;
        }
        SaleRepayPlanDto queryDto = new SaleRepayPlanDto();
        queryDto.setSaleNo(saleNo);
        if (capitalData.getBeanName().equals(ZiFangBeanConstant.FENZHUAN)) {
            log.info("【{}】借款编号 [{}] 资方为分转资方，跳过处理", JOB_NAME, saleNo);
            return;
        }

        OtherApi otherApi = zifangFactory.getApi(capitalData.getBeanName(), OtherApi.class);
        ResponseResult<SaleRepayPlanVo> result = otherApi.getSaleRepayPlan(queryDto);
        if (!result.isSuccess()) {
            log.error("【{}】借款编号 [{}] 查询资方赊销还款计划接口失败: {}", JOB_NAME, saleNo, result.getErrorMessage());
            return;
        }
        List<SaleRepayPlanRepayVo> repayPlanList = result.getData().getRepayPlanList();
        if (CollectionUtil.isEmpty(repayPlanList)) {
            log.error("【{}】借款编号 [{}] 资方返回还款计划为空", JOB_NAME, saleNo);
            return;
        }
        if (repayPlanList.size() < 3) {
            // 全部结清
            syncSaleRepayJob.syncAllCleanRepayPlan(repayPlanList, saleScheduleList, disburseData);
        } else {
            syncSaleRepayJob.syncRepayPlan(repayPlanList, saleScheduleList, disburseData);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void syncAllCleanRepayPlan(List<SaleRepayPlanRepayVo> repayPlanList, List<SaleSchedule> saleScheduleList, DisburseData disburseData) {
        List<SaleSchedule> updateList = new ArrayList<>();
        Map<String, SaleSchedule> saleScheduleMap = saleScheduleList.stream().collect(Collectors.toMap(SaleSchedule::getRepayTerm, item -> item));
        BigDecimal totalAmt = BigDecimal.ZERO;
        for (SaleRepayPlanRepayVo saleRepayPlanRepayVo : repayPlanList) {
            SaleSchedule saleSchedule = saleScheduleMap.get(saleRepayPlanRepayVo.getRepayTerm());
            SaleSchedule upSaleSchedule = new SaleSchedule();
            upSaleSchedule.setId(saleSchedule.getId());
            if (ObjectUtil.isEmpty(saleSchedule)) {
                log.info("【{}】查询不到订单第：{}期的还款计划，跳过处理", JOB_NAME, saleRepayPlanRepayVo.getRepayTerm());
                upSaleSchedule.setTotalAmt(BigDecimal.ZERO);
                upSaleSchedule.setTermRetPrin(BigDecimal.ZERO);
                upSaleSchedule.setDeratePrin(BigDecimal.ZERO);
                upSaleSchedule.setVipDeratePrin(BigDecimal.ZERO);
                upSaleSchedule.setSettleFlag(SettleFlagConstant.CLOSE);
            } else {
                upSaleSchedule.setRepayOwnbDate(saleRepayPlanRepayVo.getRepayOwnbDate());
                upSaleSchedule.setRepayOwneDate(saleRepayPlanRepayVo.getRepayOwneDate());
                upSaleSchedule.setRepayIntbDate(saleRepayPlanRepayVo.getRepayIntbDate());
                upSaleSchedule.setRepayInteDate(saleRepayPlanRepayVo.getRepayInteDate());
                upSaleSchedule.setTotalAmt(NumberUtil.safeParseBigDecimal(saleRepayPlanRepayVo.getTotalAmt()));
                upSaleSchedule.setTermRetPrin(NumberUtil.safeParseBigDecimal(saleRepayPlanRepayVo.getTermRetPrin()));
                upSaleSchedule.setDeratePrin(NumberUtil.safeParseBigDecimal(saleRepayPlanRepayVo.getDeratePrin()));
                upSaleSchedule.setVipDeratePrin(NumberUtil.safeParseBigDecimal(saleRepayPlanRepayVo.getVipDeratePrin()));
                if ("1".equals(saleRepayPlanRepayVo.getStatus())) {
                    upSaleSchedule.setSettleFlag(SettleFlagConstant.CLOSE);
                    if (ObjectUtil.isNotEmpty(saleRepayPlanRepayVo.getRepaySuccTime())) {
                        upSaleSchedule.setDatePay(saleRepayPlanRepayVo.getRepaySuccTime().substring(0, 10));
                        upSaleSchedule.setDatePayTime(saleRepayPlanRepayVo.getRepaySuccTime());
                    }
                } else {
                    upSaleSchedule.setSettleFlag(SettleFlagConstant.RUNNING);
                }
                if (StrUtil.isEmpty(saleSchedule.getRepayApplyNo()) && StrUtil.isNotEmpty(saleRepayPlanRepayVo.getRepayApplyNo())) {
                    upSaleSchedule.setRepayApplyNo(saleRepayPlanRepayVo.getRepayApplyNo());
                }
            }
            totalAmt = totalAmt.add(upSaleSchedule.getTotalAmt());
            updateList.add(upSaleSchedule);
        }
        saleScheduleService.updateBatchById(updateList);
        DisburseData updateDisburseData = new DisburseData();
        updateDisburseData.setId(disburseData.getId());
        updateDisburseData.setSaleRepayAmount(totalAmt);
        disburseDataMapper.updateById(updateDisburseData);
    }

    @Transactional(rollbackFor = Exception.class)
    public void syncRepayPlan(List<SaleRepayPlanRepayVo> repayPlanList, List<SaleSchedule> saleScheduleList, DisburseData disburseData) {
        List<SaleSchedule> updateList = new ArrayList<>();
        Map<String, SaleSchedule> saleScheduleMap = saleScheduleList.stream().collect(Collectors.toMap(SaleSchedule::getRepayTerm, item -> item));
        BigDecimal totalAmt = BigDecimal.ZERO;
        for (SaleRepayPlanRepayVo saleRepayPlanRepayVo : repayPlanList) {
            SaleSchedule saleSchedule = saleScheduleMap.get(saleRepayPlanRepayVo.getRepayTerm());
            if (ObjectUtil.isEmpty(saleSchedule)) {
                log.info("【{}】查询不到订单第：{}期的还款计划，跳过处理", JOB_NAME, saleRepayPlanRepayVo.getRepayTerm());
                continue;
            }
            SaleSchedule upSaleSchedule = new SaleSchedule();
            upSaleSchedule.setId(saleSchedule.getId());
            upSaleSchedule.setRepayOwnbDate(saleRepayPlanRepayVo.getRepayOwnbDate());
            upSaleSchedule.setRepayOwneDate(saleRepayPlanRepayVo.getRepayOwneDate());
            upSaleSchedule.setRepayIntbDate(saleRepayPlanRepayVo.getRepayIntbDate());
            upSaleSchedule.setRepayInteDate(saleRepayPlanRepayVo.getRepayInteDate());
            upSaleSchedule.setTotalAmt(NumberUtil.safeParseBigDecimal(saleRepayPlanRepayVo.getTotalAmt()));
            upSaleSchedule.setTermRetPrin(NumberUtil.safeParseBigDecimal(saleRepayPlanRepayVo.getTermRetPrin()));
            upSaleSchedule.setDeratePrin(NumberUtil.safeParseBigDecimal(saleRepayPlanRepayVo.getDeratePrin()));
            upSaleSchedule.setVipDeratePrin(NumberUtil.safeParseBigDecimal(saleRepayPlanRepayVo.getVipDeratePrin()));
            if ("1".equals(saleRepayPlanRepayVo.getStatus())) {
                upSaleSchedule.setSettleFlag(SettleFlagConstant.CLOSE);
                if (ObjectUtil.isNotEmpty(saleRepayPlanRepayVo.getRepaySuccTime())) {
                    upSaleSchedule.setDatePay(saleRepayPlanRepayVo.getRepaySuccTime().substring(0, 10));
                    upSaleSchedule.setDatePayTime(saleRepayPlanRepayVo.getRepaySuccTime());
                }
            } else {
                upSaleSchedule.setSettleFlag(SettleFlagConstant.RUNNING);
            }
            if (StrUtil.isEmpty(saleSchedule.getRepayApplyNo()) && StrUtil.isNotEmpty(saleRepayPlanRepayVo.getRepayApplyNo())) {
                upSaleSchedule.setRepayApplyNo(saleRepayPlanRepayVo.getRepayApplyNo());
            }
            updateList.add(upSaleSchedule);
            totalAmt = totalAmt.add(upSaleSchedule.getTotalAmt());
        }
        saleScheduleService.updateBatchById(updateList);
        DisburseData updateDisburseData = new DisburseData();
        updateDisburseData.setId(disburseData.getId());
        updateDisburseData.setSaleRepayAmount(totalAmt);
        disburseDataMapper.updateById(updateDisburseData);
    }
}
