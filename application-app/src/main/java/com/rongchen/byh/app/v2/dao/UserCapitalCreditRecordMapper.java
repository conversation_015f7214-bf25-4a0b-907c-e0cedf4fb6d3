package com.rongchen.byh.app.v2.dao;

import com.rongchen.byh.app.v2.entity.UserCapitalCreditRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【user_capital_credit_record(用户资方授信结果记录表)】的数据库操作Mapper
* @createDate 2025-04-23 14:23:29
* @Entity com.rongchen.byh.app.v2/entity.UserCapitalCreditRecord
*/
@Mapper
public interface UserCapitalCreditRecordMapper extends BaseMapper<UserCapitalCreditRecord> {

    List<UserCapitalCreditRecord> selectListByResultId(Long resultId);

    UserCapitalCreditRecord selectByOrderNo(String orderNo);

    List<UserCapitalCreditRecord> selectInProcessList(Integer day);
}




