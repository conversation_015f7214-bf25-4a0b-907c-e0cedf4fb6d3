package com.rongchen.byh.app.web.api.adapter.impl;

import cn.hutool.core.io.IORuntimeException;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.gson.JsonNull;
import com.rongchen.byh.app.api.service.strategy.impl.MaYiOutApiProcess;
import com.rongchen.byh.app.config.FileUtilService;
import com.rongchen.byh.app.dao.*;
import com.rongchen.byh.app.dto.CreateTokenDto;
import com.rongchen.byh.app.dto.app.BackVo;
import com.rongchen.byh.app.entity.*;
import com.rongchen.byh.app.utils.UserTokenUtil;
import com.rongchen.byh.app.v2.common.RongMethod;
import com.rongchen.byh.app.entity.DisburseData;
import com.rongchen.byh.app.entity.RepaySchedule;
import com.rongchen.byh.app.entity.SaleSchedule;
import com.rongchen.byh.app.entity.UserCreditData;
import com.rongchen.byh.app.v2.dao.ApiPushLogMapper;
import com.rongchen.byh.app.v2.entity.*;
import com.rongchen.byh.app.v2.service.*;
import com.rongchen.byh.app.v2.utils.RequestUtil;
import com.rongchen.byh.app.v2.vo.DisburseRelationVo;
import com.rongchen.byh.app.v2.service.IApiCreditRelationService;
import com.rongchen.byh.app.v2.service.IApiDisburseRelationService;
import com.rongchen.byh.app.v2.service.IApiPushRecordService;
import com.rongchen.byh.app.v2.service.IRongOrderInfoService;
import com.rongchen.byh.app.web.api.adapter.AbstractChannelAdapter;
import com.rongchen.byh.app.web.api.common.ApiChannel;
import com.rongchen.byh.app.web.api.common.ApiOperation;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.rongchen.byh.common.api.rong.config.RongProperties;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.constant.SettleFlagConstant;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.rabbitmq.constants.QueueConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;

/**
 * 融360渠道 - 适配器实现
 * <p>
 * 通过组合方式注入具体的业务操作处理器 (Handler)。
 */
@Slf4j
@Component
public class Rong360Adapter extends AbstractChannelAdapter {

    @Resource
    MaYiOutApiProcess maYiOutApiProcess;
    @Resource
    IRongOrderInfoService rongOrderInfoService;
    @Resource
    DisburseDataMapper disburseDataMapper;
    @Resource
    RepayScheduleMapper repayScheduleMapper;
    @Resource
    IApiPushRecordService pushRecordService;
    @Resource
    IApiCreditRelationService creditRelationService;
    @Resource
    UserCreditDataMapper userCreditDataMapper;
    @Resource
    IApiDisburseRelationService disburseRelationService;
    @Resource
    private SaleScheduleMapper saleScheduleMapper;
    @Resource
    UserBankCardMapper userBankCardMapper;
    @Resource
    UserDataMapper userDataMapper;
    @Resource
    RongProperties rongProperties;
    @Resource
    RequestUtil requestUtil;
    @Resource
    FileUtilService fileUtilService;
    @Resource
    IApiCallBackService callBackService;
    @Resource
    private ApiPushLogMapper apiPushLogMapper;

    @Resource
    protected RabbitTemplate rabbitTemplate;

    /**
     * 构造函数。 初始化适配器并设置其支持的渠道代码。
     *
     * @throws IllegalArgumentException 如果渠道代码为 null 或空。
     */
    protected Rong360Adapter() {
        super(ApiChannel.RONG360.getCode());
    }

    /**
     * -- GETTER -- 获取此适配器支持的渠道代码 (小写)
     *
     * @return 渠道代码 (例如 "rong360")
     */
    @Override
    public String getSupportedChannelCode() {
        log.info("渠道[{}] 初始化完成，已设置 Credit Handler.", ApiChannel.RONG360.getCode());
        return ApiChannel.RONG360.getCode();
    }

    @Override
    protected JSONObject preProcess(String channel, ApiOperation operation, HttpServletRequest request)
            throws Exception {
        log.info("[{}] Rong360 preProcess for operation: {}", channel, operation.getCode());
        String requestBody = StreamUtils.copyToString(request.getInputStream(), StandardCharsets.UTF_8);
        log.debug("[{}] Rong360 original request body: {}", channel, requestBody);

        JSONObject processedData = JSONObject.parseObject(requestBody);
        log.info("[{}] Rong360 preProcess completed.", channel);
        return processedData;
    }

    @Override
    protected JSONObject postProcess(String channel, ApiOperation operation, JSONObject businessResult)
            throws Exception {
        log.info("[{}] Rong360 postProcess for operation: {}, businessResult: {}", channel, operation.getCode(),
                businessResult);
        JSONObject finalResult = (businessResult != null) ? businessResult : new JSONObject();

        log.info("[{}] Rong360 postProcess completed.", channel);
        return finalResult;
    }

    /**
     * 信用申请
     *
     * @param channel
     * @param processedRequestData
     * @return
     * @throws Exception
     */
    @Override
    public JSONObject creditApply(String channel, JSONObject processedRequestData) throws Exception {
        log.info("[{}] Handler: Executing Rong360 creditApply logic... Data: {}", channel, processedRequestData);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("code", 200);
        jsonObject.put("msg", "成功");
        String data = processedRequestData.getString("biz_data");
        JSONObject bizData = JSONObject.parseObject(data);
        JSONObject orderInfo = bizData.getJSONObject("orderInfo");

        try {
            //5.存api_push_record
            ApiPushRecord apiPush = new ApiPushRecord();
            apiPush.setOutOrderNo(orderInfo.getString("order_no"));
            pushRecordService.insert(apiPush);
            //6.存api_credit_relation
            ApiCreditRelation creditRelation = new ApiCreditRelation();
            creditRelation.setApiPushRecordId(apiPush.getId());
            creditRelation.setCreateTime(new Date());
            creditRelationService.insert(creditRelation);
            bizData.put("channel", channel);
            bizData.put("creditRelation", creditRelation);
            rabbitTemplate.convertAndSend(QueueConstant.CREDIT_APPLY_QUEUE, bizData.toJSONString());
        } catch (DuplicateKeyException e) {
            // 重复索引
            log.error("Rong360 creditApply 插入数据库异常 error: ", e);
        } catch (Exception e) {
            log.error("Rong360 creditApply error: ", e);
            jsonObject.put("code", 400);
            jsonObject.put("msg", "系统异常");
        }
        return jsonObject;
    }

    /**
     * 信用额度查询结果
     *
     * @param channel
     * @param processedRequestData
     * @return
     * @throws Exception
     */
    @Override
    public JSONObject creditQueryResult(String channel, JSONObject processedRequestData) throws Exception {
        log.info("[{}] Handler: Executing Rong360 creditQueryResult logic... Data: {}", channel,
                processedRequestData);
//        JSONObject result = maYiOutApiProcess.creditQueryResult(processedRequestData);
        JSONObject resultObject = new JSONObject();
        //判断是否存在改订单号
        JSONObject biz_data = processedRequestData.getJSONObject("biz_data");
        String orderNo = biz_data.getString("order_no");
        ApiPushRecord pushRecord = pushRecordService.selectByOrderNo(orderNo);
        if (pushRecord == null) {
            throw new UnsupportedOperationException(ErrorCodeEnum.NOORDERNO.getErrorMessage());
        } else {
            //根据pushRecord.id查询授信/审批状态
            ApiCreditRelation creditRelation = creditRelationService.selectByPushRecordId(pushRecord.getId());
            if (creditRelation == null || creditRelation.getStatus() == 1) {
                //授信中
                log.info("[{}] Handler: Internal service (creditQueryResult) result: {}", channel, resultObject);
                return resultObject;
            } else if (creditRelation.getStatus() == 3) {
                //授信失败
                resultObject.put("order_no", orderNo);
                resultObject.put("conclusion", 40);
                resultObject.put("process_flag", 0);
                resultObject.put("remark", creditRelation.getFailReason());
                resultObject.put("refuse_time", creditRelation.getUpdateTime().getTime() / 1000);
                log.info("[{}] Handler: Internal service (creditQueryResult) result: {}", channel, resultObject);
                return resultObject;
            } else {
                resultObject.put("order_no", orderNo);
                resultObject.put("conclusion", 10);
                resultObject.put("process_flag", 0);
                resultObject.put("approval_time", creditRelation.getUpdateTime().getTime() / 1000);
                resultObject.put("amount_type", 0);
                UserCreditData userCreditData = userCreditDataMapper.queryByUserId(pushRecord.getUserId());
                resultObject.put("approval_amount", userCreditData.getCreditAmount());
                resultObject.put("term_unit", 2);
                resultObject.put("term_type", 0);
                resultObject.put("approval_term", 12);
                log.info("[{}] Handler: Internal service (creditQueryResult) result: {}", channel, resultObject);
                return resultObject;
            }
        }

    }

    /**
     * 信用额度结果推送
     *
     * @param channel
     * @param processedRequestData
     * @return
     * @throws Exception
     */
    @Override
    public ResponseResult<JSONObject> creditResultNotice(String channel, JSONObject processedRequestData) throws Exception {
        String result = null;
        JSONObject resultObject = new JSONObject();
        try {
            log.info("[{}] Handler: Executing Rong360 creditQueryResult logic... Data: {}", channel,
                    processedRequestData);
            //获取订单号
            String orderNo = processedRequestData.getString("outOrderNo");
            //根据pushRecord.id查询授信/审批状态
            ApiCreditRelation creditRelation = creditRelationService.selectByPushRecordId(processedRequestData.getLong("id"));
            if (creditRelation == null || creditRelation.getStatus() == 1) {
                //授信中
                log.info("[{}] Handler: Internal service (creditQueryResult) result: {}", channel, resultObject);
                return ResponseResult.success(resultObject);
            } else if (creditRelation.getStatus() == 3) {
                //授信失败
                resultObject.put("order_no", orderNo);
                resultObject.put("conclusion", 40);
                resultObject.put("process_flag", 0);
                resultObject.put("remark", creditRelation.getFailReason());
                resultObject.put("refuse_time", creditRelation.getUpdateTime().getTime() / 1000);
                log.info("[{}] Handler: Internal service (creditQueryResult) result: {}", channel, resultObject);
            } else {
                resultObject.put("order_no", orderNo);
                resultObject.put("conclusion", 10);
                resultObject.put("process_flag", 0);
                resultObject.put("approval_time", creditRelation.getUpdateTime().getTime() / 1000);
                resultObject.put("amount_type", 0);
                UserCreditData userCreditData = userCreditDataMapper.queryByUserId(processedRequestData.getLong("userId"));
                resultObject.put("approval_amount", userCreditData.getCreditAmount());
                resultObject.put("term_unit", 2);
                resultObject.put("term_type", 0);
                resultObject.put("approval_term", 12);
                log.info("[{}] Handler: Internal service (creditQueryResult) result: {}", channel, resultObject);
            }
            result = requestUtil.doRequest(RongMethod.APPROVAL_METHOD, resultObject, null);
        } catch (IORuntimeException e) {
            resultObject.put("result", 3);
            return ResponseResult.success(resultObject);
        } catch (Exception e) {
            log.error("回调360还款计划通知异常", e);
            resultObject.put("result", 3);
            return ResponseResult.success(resultObject);
        }
        JSONObject data = JSONObject.parseObject(result);
        if ("200".equals(data.getString("error"))) {
            resultObject.put("result", 1);
        } else {
            resultObject.put("result", 2);
        }
        return ResponseResult.success(resultObject);


    }


    /**
     * 用户准入检查
     *
     * @param channel
     * @param processedRequestData
     * @return
     * @throws Exception
     */
    @Override
    public JSONObject userAccess(String channel, JSONObject processedRequestData) throws Exception {
        log.info("[{}] Handler: Executing Rong360 userAccess logic... Data: {}", channel, processedRequestData);
        log.warn("[{}] Handler: userAccess not implemented yet.", channel);
//        JSONObject biz_data = processedRequestData.getJSONObject("biz_data");
        JSONObject result = new JSONObject();
//        String mobileMd5 = biz_data.getString("mobile_md5");
//        String lowerMobileMd5 = mobileMd5.toLowerCase();// 使用传入的MD5 (转小写)
//        LambdaQueryWrapper<UserData> wrapper = new LambdaQueryWrapper<>();
//        wrapper.eq(UserData::getMobileMd, lowerMobileMd5).ne(UserData::getSourceMode, SourceMode.RONG360);
//        UserData userData = userDataMapper.selectOne(wrapper);
//        //todo确认是否需要判断是否是老用户
//        if(userData==null){
//            result.put("is_clearance", 0);
//        }else{
//            throw new UnsupportedOperationException(ErrorCodeEnum.USEREXIST.getErrorMessage());
//        }
        result.put("is_clearance", 0);
        return result;
    }

    /**
     * 协议查询
     *
     * @param channel
     * @param processedRequestData
     * @return
     * @throws Exception
     */
    @Override
    public JSONObject protocolQuery(String channel, JSONObject processedRequestData) throws Exception {
        log.info("[{}] Handler: Executing Rong360 protocolQuery logic... Data: {}", channel, processedRequestData);
        log.warn("[{}] Handler: protocolQuery not implemented yet.", channel);
        JSONObject result = new JSONObject();
        JSONObject biz_data = processedRequestData.getJSONObject("biz_data");
        String userId = biz_data.getString("user_id");
        String orderNo = biz_data.getString("order_no");
        ApiPushRecord apiPushRecord = pushRecordService.selectByOrderNo(orderNo);
        UserData userData = userDataMapper.selectById(apiPushRecord.getUserId());
        String url = rongProperties.getProtocolUrl()+"?uid="+userId + "&phone="+userData.getMobile();
        result.put("contract_url", url);
        return result;
    }

    /**
     * 信用额度查询
     *
     * @param channel
     * @param processedRequestData
     * @return
     * @throws Exception
     */
    @Override
    public JSONObject creditLimitQuery(String channel, JSONObject processedRequestData) throws Exception {
        log.info("[{}] Handler: Executing Rong360 creditLimitQuery logic... Data: {}", channel,
                processedRequestData);
        JSONObject result = maYiOutApiProcess.creditQueryResult(processedRequestData);
        log.info("[{}] Handler: Internal service (creditLimitQuery using creditQueryResult) result: {}", channel,
                result);
        return result;
    }

    /**
     * 处理借款试算的核心逻辑。
     * <p>
     * 子类应覆盖此方法以实现具体的借款试算处理。 默认实现抛出 {@link UnsupportedOperationException}。
     *
     * @param channel              渠道标识
     * @param processedRequestData 经过 preProcess 处理后的请求数据
     * @return 借款试算的处理结果
     * @throws Exception 处理过程中可能发生的异常
     */
    @Override
    protected JSONObject loanTrial(String channel, JSONObject processedRequestData) throws Exception {
        return super.loanTrial(channel, processedRequestData);
    }

    /**
     * 处理借款申请的核心逻辑。
     * <p>
     * 子类应覆盖此方法以实现具体的借款申请处理。 默认实现抛出 {@link UnsupportedOperationException}。
     *
     * @param channel              渠道标识
     * @param processedRequestData 经过 preProcess 处理后的请求数据
     * @return 借款申请的处理结果
     * @throws Exception 处理过程中可能发生的异常
     */
    @Override
    protected JSONObject loanApply(String channel, JSONObject processedRequestData) throws Exception {
        return super.loanApply(channel, processedRequestData);
    }

    /**
     * 处理借款结果查询的核心逻辑。
     * <p>
     * 子类应覆盖此方法以实现具体的借款结果查询处理。 默认实现抛出 {@link UnsupportedOperationException}。
     *
     * @param channel              渠道标识
     * @param processedRequestData 经过 preProcess 处理后的请求数据
     * @return 借款结果查询的处理结果
     * @throws Exception 处理过程中可能发生的异常
     */
    @Override
    protected JSONObject loanResultQuery(String channel, JSONObject processedRequestData) throws Exception {
        String biz_data = processedRequestData.getString("biz_data");
        JSONObject data = JSONObject.parseObject(biz_data);
        String orderNo = data.getString("order_no");
        ApiPushRecord pushRecord = pushRecordService.selectByOrderNo(orderNo);
        //获取支用
        DisburseRelationVo vo = disburseRelationService.selecDisbursetByPushRecordId(pushRecord.getId());
        //查询用户状态是放款中或者审批通过的订单
        JSONObject resObject = new JSONObject();
        resObject.put("order_no", processedRequestData.getString("orderNo"));
        resObject.put("loan_amount", vo.getCreditAmount());
        resObject.put("loan_order_no", vo.getLoanNo());
        resObject.put("loan_term", 12);
        return resObject;
    }
    /**
     * 处理借款结果通知的核心逻辑。
     * <p>
     * 子类应覆盖此方法以实现具体的借款结果查询处理。 默认实现抛出 {@link UnsupportedOperationException}。
     *
     * @param channel              渠道标识
     * @param processedRequestData 经过 preProcess 处理后的请求数据
     * @return 借款结果查询的处理结果
     * @throws Exception 处理过程中可能发生的异常
     */
    @Override
    public ResponseResult<Void> loanResultNotice(String channel, JSONObject processedRequestData) throws Exception {

        //获取支用
        DisburseRelationVo vo = disburseRelationService.selecDisbursetByPushRecordId(processedRequestData.getLong("id"));
        //查询用户状态是放款中或者审批通过的订单
        JSONObject resObject = new JSONObject();
        resObject.put("order_no", processedRequestData.getString("orderNo"));
        resObject.put("loan_amount", vo.getCreditAmount());
        resObject.put("loan_order_no", vo.getLoanNo());
        resObject.put("loan_term", 12);
        String result = requestUtil.doRequest(RongMethod.LOAN_METHOD, resObject, null);
        ApiPushLog apiPushLog = new ApiPushLog();
        apiPushLog.setApiPushRecordId(processedRequestData.getLong("id"));
        apiPushLog.setType(RongMethod.LOAN_NOTICE);

        apiPushLog.setResponseData(result);

        JSONObject data = JSONObject.parseObject(result);
        if ("200".equals(data.getString("error"))) {
            apiPushLog.setStatus(1);
            apiPushLogMapper.insert(apiPushLog);
            //修改api_push_record中下款通知状态
            ApiPushRecord pushRecord = new ApiPushRecord();
            pushRecord.setId(processedRequestData.getLong("id"));
            pushRecord.setDisburseNotify(1);
            pushRecordService.update(pushRecord);
            return ResponseResult.success();
        }
        apiPushLog.setStatus(0);
        apiPushLogMapper.insert(apiPushLog);
        return ResponseResult.error(ErrorCodeEnum.FAIL);
    }

    /**
     * 处理还款申请 (主动/预还款) 的核心逻辑。
     * <p>
     * 子类应覆盖此方法以实现具体的还款申请处理。 默认实现抛出 {@link UnsupportedOperationException}。
     *
     * @param channel              渠道标识
     * @param processedRequestData 经过 preProcess 处理后的请求数据
     * @return 还款申请的处理结果
     * @throws Exception 处理过程中可能发生的异常
     */
    @Override
    protected JSONObject repayApply(String channel, JSONObject processedRequestData) throws Exception {
        return super.repayApply(channel, processedRequestData);
    }

    /**
     * 处理还款计划查询的核心逻辑。
     * <p>
     * 子类应覆盖此方法以实现具体的还款计划查询处理。 默认实现抛出 {@link UnsupportedOperationException}。
     *
     * @param channel              渠道标识
     * @param processedRequestData 经过 preProcess 处理后的请求数据
     * @return 还款计划查询的处理结果
     * @throws Exception 处理过程中可能发生的异常
     */
    @Override
    protected JSONObject repayPlanQuery(String channel, JSONObject processedRequestData) throws Exception {
        JSONObject resultObject = new JSONObject();
        JSONObject biz_data = processedRequestData.getJSONObject("biz_data");
        //判断是否存在改订单号
        String orderNo = biz_data.getString("order_no");
        resultObject.put("order_no",orderNo);
        ApiPushRecord pushRecord = pushRecordService.selectByOrderNo(orderNo);
        if (pushRecord == null) {
            throw new UnsupportedOperationException(ErrorCodeEnum.NOORDERNO.getErrorMessage());
        } else {
            //1.添加银行卡相关参数
            BackVo backVo = userBankCardMapper.queryBackByUserId(pushRecord.getUserId());
            resultObject.put("open_bank",backVo.getBankName());
            resultObject.put("bank_card",backVo.getBankAccount());
            //2.添加金额相关参数
            ApiDisburseRelation disburseRelation = disburseRelationService.selectByPushRecordId(pushRecord.getId());
            DisburseData disburseData = disburseDataMapper.selectById(disburseRelation.getDisburseId());
            resultObject.put("loan_amount",disburseData.getCreditAmount().floatValue());
            resultObject.put("loan_success_time",disburseData.getLoanTime().getTime()/1000);
            BigDecimal totalRepayAmount = disburseData.getCreditAmount().add(disburseData.getGrossInterest().add(disburseData.getSaleRepayAmount()));
            resultObject.put("total_amount",totalRepayAmount.floatValue());
            resultObject.put("can_prepay",0);
            //3.添加每月的还款计划
            //3.1查询本金计划表
            List<ApiRepayPlan> repaySchedules = repayScheduleMapper.queryListByDisburseId(disburseData.getId());
            //3.2查询赊销计划表
            List<ApiRepayPlan> saleSchedules = saleScheduleMapper.queryListByDisburseId(disburseData.getId());
            for (int i = 0; i < repaySchedules.size(); i++) {
                ApiRepayPlan repayPlan = repaySchedules.get(i);
                String repStatus = repayPlan.getBillStatusStr();
                if(i<3){
                    ApiRepayPlan salePlan = saleSchedules.get(i);

                    String saleStatus = salePlan.getBillStatusStr();
                    repayPlan.setInterest(repayPlan.getInterest().add(salePlan.getInterest()));
                    repayPlan.setOverdue_fee(repayPlan.getOverdue_fee().add(salePlan.getOverdue_fee()));
                    repayPlan.setDue_time(getTime(repayPlan.getDueTimeStr() + " 00:00:00"));
                    repayPlan.setAmount(repayPlan.getAmount().add(salePlan.getAmount()));
                    repayPlan.setPaid_amount(repayPlan.getPaid_amount().add(salePlan.getPaid_amount()));
                    String remark = "含本金"+repayPlan.getPrincipal()+"元，利息"+repayPlan.getInterest()+"元，逾期费"+repayPlan.getOverdue_fee()+"元，其他"+repayPlan.getService_fee().add(salePlan.getAmount())+"元";
                    repayPlan.setRemark(remark);
                    Integer repayStatus = 1;
                    if(repStatus.equals("3") || saleStatus.equals("3")){
                        repayStatus=3;
                    }else if(repStatus.equals("2") && saleStatus.equals("2")){
                        repayStatus=2;
                    }
                    repayPlan.setBill_status(repayStatus);
                    if(repayStatus==2){
                        repayPlan.setSuccess_time(getTime(repayPlan.getSuccessTimeStr() + " 00:00:00"));
                    }else {
                        repayPlan.setSuccess_time(null);
                    }
                    repayPlan.setCan_repay_time(getTime(repayPlan.getCanRepayTimeStr() + " 00:00:00"));
                }else{
                    String remark = "含本金"+repayPlan.getPrincipal()+"元，利息"+repayPlan.getInterest()+"元，逾期费"+repayPlan.getOverdue_fee()+"元，其他"+repayPlan.getService_fee()+"元";
                    repayPlan.setRemark(remark);
                    repayPlan.setBill_status(Integer.valueOf(repayPlan.getBillStatusStr()));
                    repayPlan.setDue_time(getTime(repayPlan.getDueTimeStr() + " 00:00:00"));
                    if(repStatus.equals("2")){
                        repayPlan.setSuccess_time(getTime(repayPlan.getSuccessTimeStr() + " 00:00:00"));
                    }else{
                        repayPlan.setSuccess_time(null);
                    }
                    repayPlan.setCan_repay_time(getTime(repayPlan.getCanRepayTimeStr() + " 00:00:00"));
                }
            }
            resultObject.put("repayment_plan",repaySchedules);
        }
        return resultObject;
    }
    /**
     * 处理还款计划通知的核心逻辑。
     * <p>
     * 子类应覆盖此方法。默认抛出 {@link UnsupportedOperationException}。
     * </p>
     *
     * @param channel              渠道标识
     * @param processedRequestData 处理后的请求数据
     * @return 还款计划通知结果
     * @throws Exception 处理异常
     */
    @Override
    public ResponseResult<JSONObject> repayPlanNotice(String channel, JSONObject processedRequestData) throws Exception {
        ApiPushRecord pushRecord = null;
        String result = null;
        JSONObject resultObject = new JSONObject();
        try {
            pushRecord = processedRequestData.toJavaObject(ApiPushRecord.class);
            //判断是否存在改订单号
            resultObject.put("order_no", pushRecord.getOutOrderNo());
            //1.添加银行卡相关参数
            BackVo backVo = userBankCardMapper.queryBackByUserId(pushRecord.getUserId());
            resultObject.put("open_bank", backVo.getBankName());
            resultObject.put("bank_card", backVo.getBankAccount());
            //2.添加金额相关参数
            ApiDisburseRelation disburseRelation = disburseRelationService.selectByPushRecordId(pushRecord.getId());
            DisburseData disburseData = disburseDataMapper.selectById(disburseRelation.getDisburseId());
            resultObject.put("loan_amount", disburseData.getCreditAmount().floatValue());
            resultObject.put("loan_success_time", disburseData.getLoanTime().getTime() / 1000);
            BigDecimal totalRepayAmount = disburseData.getCreditAmount().add(disburseData.getGrossInterest().add(disburseData.getSaleRepayAmount()));
            resultObject.put("total_amount", totalRepayAmount.floatValue());
            resultObject.put("can_prepay", 0);
            //3.添加每月的还款计划
            //3.1查询本金计划表
            List<ApiRepayPlan> repaySchedules = repayScheduleMapper.queryListByDisburseId(disburseData.getId());
            //3.2查询赊销计划表
            List<ApiRepayPlan> saleSchedules = saleScheduleMapper.queryListByDisburseId(disburseData.getId());
            for (int i = 0; i < repaySchedules.size(); i++) {
                ApiRepayPlan repayPlan = repaySchedules.get(i);
                String repStatus = repayPlan.getBillStatusStr();
                repayPlan.setIs_able_defer(0);
                if (i < 3) {
                    ApiRepayPlan salePlan = saleSchedules.get(i);
                    String saleStatus = salePlan.getBillStatusStr();
                    repayPlan.setInterest(repayPlan.getInterest().add(salePlan.getInterest()));
                    repayPlan.setOverdue_fee(repayPlan.getOverdue_fee().add(salePlan.getOverdue_fee()));
                    repayPlan.setDue_time(getTime(repayPlan.getDueTimeStr() + " 00:00:00"));
                    repayPlan.setAmount(repayPlan.getAmount().add(salePlan.getAmount()));
                    repayPlan.setPaid_amount(repayPlan.getPaid_amount().add(salePlan.getPaid_amount()));
                    Integer repayStatus = 1;
                    if (repStatus.equals("3") || saleStatus.equals("3")) {
                        repayStatus = 3;
                    } else if (repStatus.equals("2") && saleStatus.equals("2")) {
                        repayStatus = 2;
                    }
                    repayPlan.setBill_status(repayStatus);
                    if (repayStatus == 2) {
                        repayPlan.setSuccess_time(getTime(repayPlan.getSuccessTimeStr() + " 00:00:00"));
                    } else {
                        repayPlan.setSuccess_time(null);
                    }
                    repayPlan.setCan_repay_time(getTime(repayPlan.getCanRepayTimeStr() + " 00:00:00"));
                    String remark = "含本金"+repayPlan.getPrincipal()+"元，利息"+repayPlan.getInterest()+"元，逾期费"+repayPlan.getOverdue_fee()+"元，其他"+repayPlan.getService_fee().add(salePlan.getAmount())+"元";
                    repayPlan.setRemark(remark);
                } else {
                    String remark = "含本金"+repayPlan.getPrincipal()+"元，利息"+repayPlan.getInterest()+"元，逾期费"+repayPlan.getOverdue_fee()+"元，其他"+repayPlan.getService_fee()+"元";
                    repayPlan.setRemark(remark);
                    repayPlan.setBill_status(Integer.valueOf(repayPlan.getBillStatusStr()));
                    repayPlan.setDue_time(getTime(repayPlan.getDueTimeStr() + " 00:00:00"));
                    if(repStatus.equals("2")){
                        repayPlan.setSuccess_time(getTime(repayPlan.getSuccessTimeStr() + " 00:00:00"));
                    }else {
                        repayPlan.setSuccess_time(null);
                    }
                    repayPlan.setCan_repay_time(getTime(repayPlan.getCanRepayTimeStr() + " 00:00:00"));
                }
            }
            resultObject.put("repayment_plan", repaySchedules);
            result = requestUtil.doRequest(RongMethod.REPAY_PLAN_METHOD, resultObject, null);
        } catch (IORuntimeException e) {
            resultObject.put("result", 3);
            return ResponseResult.success(resultObject);
        } catch (Exception e) {
            log.error("回调360还款计划通知异常", e);
            return ResponseResult.error(ErrorCodeEnum.FAIL);
        }

        JSONObject data = JSONObject.parseObject(result);
        if ("200".equals(data.getString("error"))) {
            resultObject.put("result", 1);
            return ResponseResult.success(resultObject);
        }
        resultObject.put("result", 2);
        return ResponseResult.success(resultObject);
    }

    @Override
    public ResponseResult<JSONObject> orderStatusNotice(String channel, JSONObject processedRequestData) throws Exception {
        ApiPushRecord pushRecord = null;
        String result = null;
        JSONObject resultObject = new JSONObject();
        try {
            pushRecord = processedRequestData.toJavaObject(ApiPushRecord.class);
            JSONObject biz_data = processedRequestData.getJSONObject("biz_data");
            //判断是否存在改订单号
            String orderNo = biz_data.getString("order_no");
            resultObject.put("order_no",orderNo);
            if (pushRecord == null) {
                throw new UnsupportedOperationException(ErrorCodeEnum.NOORDERNO.getErrorMessage());
            } else {
                //根据pushRecord.id查询授信/审批状态
                ApiCreditRelation creditRelation = creditRelationService.selectByPushRecordId(pushRecord.getId());
                Integer status = creditRelation.getStatus();
                if (status == 1) {
                    //授信中
                    resultObject.put("update_time", System.currentTimeMillis() / 1000);
                    resultObject.put("order_status", JsonNull.INSTANCE);
                } else if (status == 3) {
                    //授信失败
                    resultObject.put("update_time", creditRelation.getUpdateTime().getTime() / 1000);
                    resultObject.put("order_status", 110);
                } else {
                    //授信成功
                    //1.查询支用中间表查看支用状态
                    ApiDisburseRelation disburseRelation = disburseRelationService.selectByPushRecordId(pushRecord.getId());
                    if (disburseRelation == null) {
                        resultObject.put("update_time", creditRelation.getUpdateTime().getTime() / 1000);
                        resultObject.put("order_status", 100);
                    } else {
                        Integer disStatus = disburseRelation.getStatus();
                        if (disStatus == 600) {
                            //结清
                            resultObject.put("update_time", disburseRelation.getUpdateTime().getTime() / 1000);
                            resultObject.put("order_status", 200);
                        } else if (disStatus == 300) {
                            //待放款/放款中300-》151
                            resultObject.put("update_time", disburseRelation.getUpdateTime().getTime() / 1000);
                            resultObject.put("order_status", 151);
                        } else if (disStatus == 400) {
                            //放款失败400-》169
                            resultObject.put("update_time", disburseRelation.getUpdateTime().getTime() / 1000);
                            resultObject.put("order_status", 169);
                        } else if (disStatus == 500) {
                            boolean isOverdue = false;
                            //放款成功/还款中500-》170
                            //判断是否有逾期的期数，本金逾期或者赊销逾期
                            LambdaQueryWrapper<RepaySchedule> wrapper = new LambdaQueryWrapper<>();
                            wrapper.eq(RepaySchedule::getDisburseId, disburseRelation.getDisburseId())
                                    .eq(RepaySchedule::getTermStatus, "O")
                                    .and(
                                            inner -> inner.eq(RepaySchedule::getSettleFlag, SettleFlagConstant.RUNNING)
                                                    .or()
                                                    .eq(RepaySchedule::getSettleFlag, SettleFlagConstant.REPAYING)
                                    )
                                    .orderByAsc(RepaySchedule::getRepayTerm);
                            //判断本金是否逾期
                            List<RepaySchedule> repayList = repayScheduleMapper.selectList(wrapper);
                            if (repayList != null && repayList.size() > 0) {
                                isOverdue = true;
                                long upTime = getTime(repayList.get(0).getRepayInteDate() + " 00:00:00");
                                resultObject.put("update_time", upTime);
                            }
                            //若本金没有逾期判断赊销是否逾期
                            LambdaQueryWrapper<SaleSchedule> saleWrapper = new LambdaQueryWrapper<SaleSchedule>()
                                    .eq(SaleSchedule::getDisburseId, disburseRelation.getDisburseId())
                                    .eq(SaleSchedule::getTermStatus, "O")
                                    .and(
                                            inner -> inner.eq(SaleSchedule::getSettleFlag, SettleFlagConstant.RUNNING)
                                                    .or()
                                                    .eq(SaleSchedule::getSettleFlag, SettleFlagConstant.REPAYING)
                                    )
                                    .orderByAsc(SaleSchedule::getRepayTerm);
                            List<SaleSchedule> saleList = saleScheduleMapper.selectList(saleWrapper);
                            if (saleList != null && saleList.size() > 0) {
                                isOverdue = true;
                                long upTime = getTime(saleList.get(0).getRepayInteDate() + " 00:00:00");
                                resultObject.put("update_time", upTime);
                            }

                            if (isOverdue) {
                                resultObject.put("order_status", 180);
                            } else {
                                resultObject.put("order_status", 170);
                                resultObject.put("update_time", disburseRelation.getUpdateTime().getTime() / 1000);
                            }

                        }

                    }
                }
            }
            result = requestUtil.doRequest(RongMethod.ORDER_STATE_METHOD, resultObject, null);
        } catch (IORuntimeException e) {
            resultObject.put("result", 3);
            return ResponseResult.error(ErrorCodeEnum.FAIL);
        } catch (Exception e) {
            log.error("回调360订单状态接口失败：", e);
            return ResponseResult.error(ErrorCodeEnum.FAIL);
        }
        JSONObject data = JSONObject.parseObject(result);
        if ("200".equals(data.getString("error"))) {
            resultObject.put("result", 1);
            return ResponseResult.success(resultObject);
        }
        resultObject.put("result", 1);
        return ResponseResult.success(resultObject);
    }
    /**
     * 处理还款结果查询的核心逻辑。
     * <p>
     * 子类应覆盖此方法以实现具体的还款结果查询处理。 默认实现抛出 {@link UnsupportedOperationException}。
     *
     * @param channel              渠道标识
     * @param processedRequestData 经过 preProcess 处理后的请求数据
     * @return 还款结果查询的处理结果
     * @throws Exception 处理过程中可能发生的异常
     */
    @Override
    protected JSONObject repayResultQuery(String channel, JSONObject processedRequestData) throws Exception {
        return super.repayResultQuery(channel, processedRequestData);
    }
    // === 特殊接口 Special ===

    /**
     * 处理获取借款 H5 页面 URL 的核心逻辑。
     * <p>
     * 子类应覆盖此方法。默认抛出 {@link UnsupportedOperationException}。
     * </p>
     *
     * @param channel              渠道标识
     * @param processedRequestData 处理后的请求数据
     * @return 获取借款 H5 URL 的处理结果
     * @throws Exception 处理异常
     */
    @Override
    protected JSONObject getLoanH5Url(String channel, JSONObject processedRequestData) throws Exception {
        String biz_data = processedRequestData.getString("biz_data");
        RongOrderInfo info = checkOrderNo(biz_data);
        //存地址
        inserUrl(biz_data,RongMethod.LAON_URL);
        if (info != null) {
            JSONObject jsonObject = new JSONObject();
            //查询用户信息勇于生成token
            UserData userData = userDataMapper.selectById(info.getUserId());
            //查询用户的授信金额
            UserCreditData userCredit = userCreditDataMapper.queryByUserId(info.getUserId());
            //拼接参数
            String token =  createToken(userData.getMobile(), userData.getId(), userData.getSourceMode());
            boolean isApi = true;
            String orderNo = info.getOrderNo();
            BigDecimal creditAmount = userCredit.getCreditAmount();
            jsonObject.put("url", rongProperties.getLoanH5() + "?token="+token+"&orderNo="+orderNo+"&amount="+creditAmount+"&isApi="+isApi);
            return jsonObject;
        } else {
            throw new UnsupportedOperationException("系统错误");
        }

    }

    /**
     * 处理获取还款 H5 页面 URL 的核心逻辑。
     * <p>
     * 子类应覆盖此方法。默认抛出 {@link UnsupportedOperationException}。
     * </p>
     *
     * @param channel              渠道标识
     * @param processedRequestData 处理后的请求数据
     * @return 获取还款 H5 URL 的处理结果
     * @throws Exception 处理异常
     */
    @Override
    protected JSONObject getRepayH5Url(String channel, JSONObject processedRequestData) throws Exception {
        String biz_data = processedRequestData.getString("biz_data");
        RongOrderInfo info = checkOrderNo(biz_data);
        //存地址
        inserUrl(biz_data,RongMethod.REPAY_URL);
        if (info != null) {
            JSONObject jsonObject = new JSONObject();
            //查询用户信息勇于生成token
            UserData userData = userDataMapper.selectById(info.getUserId());
            //查询用户的授信金额
            UserCreditData userCredit = userCreditDataMapper.queryByUserId(info.getUserId());
            //拼接参数
            String token =  createToken(userData.getMobile(), userData.getId(), userData.getSourceMode());
            boolean isApi = true;
            String orderNo = info.getOrderNo();
            BigDecimal creditAmount = userCredit.getCreditAmount();
            String url = rongProperties.getOrderH5() + "?token="+token+"&orderNo="+orderNo+"&amount="+creditAmount+"&isApi="+isApi;
            jsonObject.put("url", url);
            return jsonObject;
        } else {
            throw new UnsupportedOperationException("系统错误");
        }
    }

    //创建token
    private String createToken(String mobile,Long userId,Integer sourceMode) {
        CreateTokenDto createTokenDto = new CreateTokenDto();
        createTokenDto.setAccount(mobile);
        createTokenDto.setUserId(userId);
        createTokenDto.setProductId(sourceMode);
        UserTokenUtil.createToken(createTokenDto);
        String tokenValue = createTokenDto.getToken();
        return tokenValue;
    }

    private void inserUrl(String biz_data, Integer type) {
        JSONObject data = JSONObject.parseObject(biz_data);
        ApiCallBack apiCallBack = new ApiCallBack();
        apiCallBack.setOutOrderNo(data.getString("order_no"));
        apiCallBack.setType(type);
        apiCallBack.setRedirectUrl(data.getString("return_url"));
        apiCallBack.setCreateTime(new Date());
        callBackService.insert(apiCallBack);
    }

    /**
     * 获取订单状态
     */
    @Override
    protected JSONObject orderStatus(String channel, JSONObject processedRequestData) throws Exception {
        JSONObject resultObject = new JSONObject();
        JSONObject biz_data = processedRequestData.getJSONObject("biz_data");
        //判断是否存在改订单号
        String orderNo = biz_data.getString("order_no");
        resultObject.put("order_no",orderNo);
        ApiPushRecord pushRecord = pushRecordService.selectByOrderNo(orderNo);
        if (pushRecord == null) {
            throw new UnsupportedOperationException(ErrorCodeEnum.NOORDERNO.getErrorMessage());
        } else {
            //根据pushRecord.id查询授信/审批状态
            ApiCreditRelation creditRelation = creditRelationService.selectByPushRecordId(pushRecord.getId());
            Integer status = creditRelation.getStatus();
            if(status==1){
                //授信中
                resultObject.put("update_time",System.currentTimeMillis()/1000);
                resultObject.put("order_status", JsonNull.INSTANCE);
                return resultObject;
            }else if(status==3){
                //授信失败
                resultObject.put("update_time",creditRelation.getUpdateTime().getTime()/1000);
                resultObject.put("order_status", 110);
                return resultObject;
            }else{
                //授信成功
                //1.查询支用中间表查看支用状态
                ApiDisburseRelation disburseRelation = disburseRelationService.selectByPushRecordId(pushRecord.getId());
                if(disburseRelation==null){
                    resultObject.put("update_time",creditRelation.getUpdateTime().getTime()/1000);
                    resultObject.put("order_status", 100);
                    return resultObject;
                }else{
                    Integer disStatus = disburseRelation.getStatus();
                    if(disStatus==600){
                        //结清
                        resultObject.put("update_time",disburseRelation.getUpdateTime().getTime()/1000);
                        resultObject.put("order_status", 200);
                    }else if(disStatus==300){
                        //待放款/放款中300-》151
                        resultObject.put("update_time",disburseRelation.getUpdateTime().getTime()/1000);
                        resultObject.put("order_status",151);
                    }else if(disStatus==400){
                        //放款失败400-》169
                        resultObject.put("update_time",disburseRelation.getUpdateTime().getTime()/1000);
                        resultObject.put("order_status",169);
                    }else if(disStatus==500){
                        //放款成功/还款中500-》170
                        //判断是否有逾期的期数，本金逾期或者赊销逾期
                        LambdaQueryWrapper<RepaySchedule> wrapper = new LambdaQueryWrapper<>();
                        wrapper.eq(RepaySchedule::getDisburseId,disburseRelation.getDisburseId())
                               .eq(RepaySchedule::getTermStatus, "O")
                                .and(
                                        inner -> inner.eq(RepaySchedule::getSettleFlag, SettleFlagConstant.RUNNING)
                                                .or()
                                                .eq(RepaySchedule::getSettleFlag, SettleFlagConstant.REPAYING)
                                )
                                .orderByAsc(RepaySchedule::getRepayTerm);
                        //判断本金是否逾期
                        List<RepaySchedule> repayList = repayScheduleMapper.selectList(wrapper);
                        if(repayList!=null && repayList.size()>0){
                            resultObject.put("order_status", 180);
                            long upTime = getTime(repayList.get(0).getRepayInteDate() + " 00:00:00");
                            resultObject.put("update_time", upTime);
                            return resultObject;
                        }
                            //判断赊销是否逾期
                        LambdaQueryWrapper<SaleSchedule> saleWrapper = new LambdaQueryWrapper<SaleSchedule>()
                                .eq(SaleSchedule::getDisburseId, disburseRelation.getDisburseId())
                                .eq(SaleSchedule::getTermStatus, "O")
                                .and(
                                        inner -> inner.eq(SaleSchedule::getSettleFlag, SettleFlagConstant.RUNNING)
                                                .or()
                                                .eq(SaleSchedule::getSettleFlag, SettleFlagConstant.REPAYING)
                                )
                                .orderByAsc(SaleSchedule::getRepayTerm);
                        List<SaleSchedule> saleList = saleScheduleMapper.selectList(saleWrapper);
                        if (saleList != null && saleList.size() > 0) {
                            resultObject.put("order_status", 180);
                            long upTime = getTime(saleList.get(0).getRepayInteDate() + " 00:00:00");
                            resultObject.put("update_time", upTime);
                            return resultObject;
                        }
                            resultObject.put("order_status", 170);
                            resultObject.put("update_time",disburseRelation.getUpdateTime().getTime()/1000);
                    }
                    return resultObject;
                }

            }


        }
    }

    private static long getTime(String timeStr) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        Date date = sdf.parse(timeStr);
        return date.getTime() / 1000;
    }


    /**
     * 构建错误响应
     *
     * @param errorCode
     * @param errorMessage
     * @param exception
     * @return
     */
    @Override
    public JSONObject buildErrorResponse(String errorCode, String errorMessage, Exception exception) {
        // 如果融360对返回结构体有特殊要求，则在此处进行处理
        JSONObject result = new JSONObject();
        //融360的错误响应code是400
        result.put("code", 400);
        result.put("msg", errorMessage);
        result.put("data", new JSONObject());
        if(errorMessage.equals(ErrorCodeEnum.USEREXIST.getErrorMessage())){
            result.put("reason", errorMessage);
        }
        return result;
    }

    /**
     * 构建一个通用的成功响应 JSONObject。
     * <p>
     * 此方法负责将业务逻辑执行结果 (businessResult) 转换为符合特定渠道期望响应结构的 JSONObject。
     * 基础实现直接返回输入的业务结果，子类可以覆盖此方法以实现特定渠道的成功响应格式。
     * <p>
     * 这个方法的返回值随后将传递给 {@link #postProcess} 方法进行最终处理 (例如签名、加密)。
     *
     * @param channel        渠道标识
     * @param operation      操作类型
     * @param businessResult 核心业务逻辑的执行结果
     * @return 结构化的成功响应，准备被进一步处理 (签名、加密等)
     */
    @Override
    protected JSONObject buildSuccessResponse(String channel, ApiOperation operation, JSONObject businessResult) {
        // 如果融360对返回结构体有特殊要求，则在此处进行处理
        // 比如返回结构体中需要包含responseCode,responseMsg,data
        JSONObject result = new JSONObject();
        result.put("code", 200);
        result.put("msg", "success");
        result.put("data", businessResult);
        return result;
    }

    private RongOrderInfo checkOrderNo(String bizData) {
        if (StringUtils.isNotBlank(bizData)) {
            JSONObject data = JSONObject.parseObject(bizData);
            String order_no = data.getString("order_no");
            //查询是否有该订单号
            RongOrderInfo info = rongOrderInfoService.selectByOrderNo(order_no);
            if (info == null) {
                throw new UnsupportedOperationException(ErrorCodeEnum.NOORDERNO.getErrorMessage() + ":" + order_no);
            } else {
                return info;
            }
        } else {
            throw new UnsupportedOperationException(ErrorCodeEnum.NODATA.getErrorMessage());
        }
    }
}
