<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.app.dao.RepayScheduleMapper">
  <insert id="insertSelective" keyColumn="id" keyProperty="id"
    parameterType="com.rongchen.byh.app.entity.RepaySchedule" useGeneratedKeys="true">
    <!--@mbg.generated--> insert into
    `repay_schedule` <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="disburseId != null"> `disburse_id`, </if>
      <if test="userId != null"> `user_id`, </if>
      <if
        test="repayApplyNo != null"> `repay_apply_no`, </if>
      <if test="repayOwnbDate != null">
    `repay_ownb_date`, </if>
      <if test="repayOwneDate != null"> `repay_owne_date`, </if>
      <if
        test="repayIntbDate != null"> `repay_intb_date`, </if>
      <if test="repayInteDate != null">
    `repay_inte_date`, </if>
      <if test="totalAmt != null"> `total_amt`, </if>
      <if
        test="termRetPrin != null"> `term_ret_prin`, </if>
      <if test="termRetInt != null">
    `term_ret_int`, </if>
      <if test="termGuarantorFee != null"> `term_guarantor_fee`, </if>
      <if
        test="termRetFint != null"> `term_ret_fint`, </if>
      <if test="termOverdueGuarantorFee != null">
    `term_overdue_guarantor_fee`, </if>
      <if test="prinAmt != null"> `prin_amt`, </if>
      <if
        test="noRetAmt != null"> `no_ret_amt`, </if>
      <if test="intAmt != null"> `int_amt`, </if>
      <if
        test="noRetInt != null"> `no_ret_int`, </if>
      <if test="termFintFinish != null">
    `term_fint_finish`, </if>
      <if test="noRetFin != null"> `no_ret_fin`, </if>
      <if
        test="guarantorFee != null"> `guarantor_fee`, </if>
      <if test="noGuarantorFee != null">
    `no_guarantor_fee`, </if>
      <if test="termServiceFee != null"> `term_service_fee`, </if>
      <if
        test="serviceFee != null"> `service_fee`, </if>
      <if test="noServiceFee != null">
    `no_service_fee`, </if>
      <if test="overdueGuarantorFee != null"> `overdue_guarantor_fee`, </if>
      <if
        test="noOverdueGuarantorFee != null"> `no_overdue_guarantor_fee`, </if>
      <if
        test="termStatus != null"> `term_status`, </if>
      <if test="settleFlag != null"> `settle_flag`, </if>
      <if
        test="repayTerm != null"> `repay_term`, </if>
      <if test="repayMethod != null"> `repay_method`, </if>
      <if
        test="payTime != null"> `pay_time`, </if>
      <if test="datePay != null"> `date_pay`, </if>
      <if
        test="datePayTime != null"> `date_pay_time`, </if>
      <if test="autoRepay != null">
    `auto_repay`, </if>
      <if test="createTime != null"> `create_time`, </if>
      <if
        test="updateTime != null"> `update_time`, </if>
      <if test="planId != null"> `plan_id`, </if>
      <if
        test="billId != null"> `bill_id`, </if>
    </trim>
    <trim prefix="values (" suffix=")"
      suffixOverrides=",">
      <if test="disburseId != null"> #{disburseId,jdbcType=BIGINT}, </if>
      <if test="userId != null">
    #{userId,jdbcType=BIGINT}, </if>
      <if test="repayApplyNo != null">
    #{repayApplyNo,jdbcType=VARCHAR}, </if>
      <if test="repayOwnbDate != null">
    #{repayOwnbDate,jdbcType=VARCHAR}, </if>
      <if test="repayOwneDate != null">
    #{repayOwneDate,jdbcType=VARCHAR}, </if>
      <if test="repayIntbDate != null">
    #{repayIntbDate,jdbcType=VARCHAR}, </if>
      <if test="repayInteDate != null">
    #{repayInteDate,jdbcType=VARCHAR}, </if>
      <if test="totalAmt != null">
    #{totalAmt,jdbcType=DECIMAL}, </if>
      <if test="termRetPrin != null">
    #{termRetPrin,jdbcType=DECIMAL}, </if>
      <if test="termRetInt != null">
    #{termRetInt,jdbcType=DECIMAL}, </if>
      <if test="termGuarantorFee != null">
    #{termGuarantorFee,jdbcType=DECIMAL}, </if>
      <if test="termRetFint != null">
    #{termRetFint,jdbcType=DECIMAL}, </if>
      <if test="termOverdueGuarantorFee != null">
    #{termOverdueGuarantorFee,jdbcType=DECIMAL}, </if>
      <if test="prinAmt != null">
    #{prinAmt,jdbcType=DECIMAL}, </if>
      <if test="noRetAmt != null"> #{noRetAmt,jdbcType=DECIMAL}, </if>
      <if
        test="intAmt != null"> #{intAmt,jdbcType=DECIMAL}, </if>
      <if test="noRetInt != null">
    #{noRetInt,jdbcType=DECIMAL}, </if>
      <if test="termFintFinish != null">
    #{termFintFinish,jdbcType=DECIMAL}, </if>
      <if test="noRetFin != null">
    #{noRetFin,jdbcType=DECIMAL}, </if>
      <if test="guarantorFee != null">
    #{guarantorFee,jdbcType=DECIMAL}, </if>
      <if test="noGuarantorFee != null">
    #{noGuarantorFee,jdbcType=DECIMAL}, </if>
      <if test="termServiceFee != null">
    #{termServiceFee,jdbcType=DECIMAL}, </if>
      <if test="serviceFee != null">
    #{serviceFee,jdbcType=DECIMAL}, </if>
      <if test="noServiceFee != null">
    #{noServiceFee,jdbcType=DECIMAL}, </if>
      <if test="overdueGuarantorFee != null">
    #{overdueGuarantorFee,jdbcType=DECIMAL}, </if>
      <if test="noOverdueGuarantorFee != null">
    #{noOverdueGuarantorFee,jdbcType=DECIMAL}, </if>
      <if test="termStatus != null">
    #{termStatus,jdbcType=VARCHAR}, </if>
      <if test="settleFlag != null">
    #{settleFlag,jdbcType=VARCHAR}, </if>
      <if test="repayTerm != null">
    #{repayTerm,jdbcType=VARCHAR}, </if>
      <if test="repayMethod != null">
    #{repayMethod,jdbcType=VARCHAR}, </if>
      <if test="payTime != null"> #{payTime,jdbcType=VARCHAR}, </if>
      <if
        test="datePay != null"> #{datePay,jdbcType=VARCHAR}, </if>
      <if test="datePayTime != null">
    #{datePayTime,jdbcType=VARCHAR}, </if>
      <if test="autoRepay != null">
    #{autoRepay,jdbcType=INTEGER}, </if>
      <if test="createTime != null">
    #{createTime,jdbcType=TIMESTAMP}, </if>
      <if test="updateTime != null">
    #{updateTime,jdbcType=TIMESTAMP}, </if>
      <if test="planId != null"> #{planId,jdbcType=VARCHAR}, </if>
      <if
        test="billId != null"> #{billId,jdbcType=VARCHAR}, </if>
    </trim>
  </insert>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map"
    useGeneratedKeys="true">
    <!--@mbg.generated--> insert into `repay_schedule` (`disburse_id`, `user_id`,
    `repay_apply_no`, `repay_ownb_date`, `repay_owne_date`, `repay_intb_date`, `repay_inte_date`,
    `total_amt`, `term_ret_prin`, `term_ret_int`, `term_guarantor_fee`, `term_ret_fint`,
    `term_overdue_guarantor_fee`, `prin_amt`, `no_ret_amt`, `int_amt`, `no_ret_int`,
    `term_fint_finish`, `no_ret_fin`, `guarantor_fee`, `no_guarantor_fee`, `term_service_fee`,
    `service_fee`, `no_service_fee`, `overdue_guarantor_fee`, `no_overdue_guarantor_fee`,
    `term_status`, `settle_flag`, `repay_term`, `repay_method`, `pay_time`, `date_pay`,
    `date_pay_time`, `auto_repay`, `create_time`, `update_time`, `plan_id`, `bill_id`) values <foreach
      collection="list" item="item" separator=","> (#{item.disburseId,jdbcType=BIGINT},
    #{item.userId,jdbcType=BIGINT}, #{item.repayApplyNo,jdbcType=VARCHAR},
    #{item.repayOwnbDate,jdbcType=VARCHAR}, #{item.repayOwneDate,jdbcType=VARCHAR},
    #{item.repayIntbDate,jdbcType=VARCHAR}, #{item.repayInteDate,jdbcType=VARCHAR},
    #{item.totalAmt,jdbcType=DECIMAL}, #{item.termRetPrin,jdbcType=DECIMAL},
    #{item.termRetInt,jdbcType=DECIMAL}, #{item.termGuarantorFee,jdbcType=DECIMAL},
    #{item.termRetFint,jdbcType=DECIMAL}, #{item.termOverdueGuarantorFee,jdbcType=DECIMAL},
    #{item.prinAmt,jdbcType=DECIMAL}, #{item.noRetAmt,jdbcType=DECIMAL},
    #{item.intAmt,jdbcType=DECIMAL}, #{item.noRetInt,jdbcType=DECIMAL},
    #{item.termFintFinish,jdbcType=DECIMAL}, #{item.noRetFin,jdbcType=DECIMAL},
    #{item.guarantorFee,jdbcType=DECIMAL}, #{item.noGuarantorFee,jdbcType=DECIMAL},
    #{item.termServiceFee,jdbcType=DECIMAL}, #{item.serviceFee,jdbcType=DECIMAL},
    #{item.noServiceFee,jdbcType=DECIMAL}, #{item.overdueGuarantorFee,jdbcType=DECIMAL},
    #{item.noOverdueGuarantorFee,jdbcType=DECIMAL}, #{item.termStatus,jdbcType=VARCHAR},
    #{item.settleFlag,jdbcType=VARCHAR}, #{item.repayTerm,jdbcType=VARCHAR},
    #{item.repayMethod,jdbcType=VARCHAR}, #{item.payTime,jdbcType=VARCHAR},
    #{item.datePay,jdbcType=VARCHAR}, #{item.datePayTime,jdbcType=VARCHAR},
    #{item.autoRepay,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP},
    #{item.updateTime,jdbcType=TIMESTAMP}, #{item.planId,jdbcType=VARCHAR},
    #{item.billId,jdbcType=VARCHAR}) </foreach>
  </insert>
  <insert id="batchInsertOrUpdate" keyColumn="id" keyProperty="id" parameterType="map"
    useGeneratedKeys="true">
    <!--@mbg.generated--> insert into `repay_schedule` (`disburse_id`, `user_id`,
    `repay_apply_no`, `repay_ownb_date`, `repay_owne_date`, `repay_intb_date`, `repay_inte_date`,
    `total_amt`, `term_ret_prin`, `term_ret_int`, `term_guarantor_fee`, `term_ret_fint`,
    `term_overdue_guarantor_fee`, `prin_amt`, `no_ret_amt`, `int_amt`, `no_ret_int`,
    `term_fint_finish`, `no_ret_fin`, `guarantor_fee`, `no_guarantor_fee`, `term_service_fee`,
    `service_fee`, `no_service_fee`, `overdue_guarantor_fee`, `no_overdue_guarantor_fee`,
    `term_status`, `settle_flag`, `repay_term`, `repay_method`, `pay_time`, `date_pay`,
    `date_pay_time`, `auto_repay`, `create_time`, `update_time`, `plan_id`, `bill_id`) values <foreach
      collection="list" item="item" separator=","> (#{item.disburseId,jdbcType=BIGINT},
    #{item.userId,jdbcType=BIGINT}, #{item.repayApplyNo,jdbcType=VARCHAR},
    #{item.repayOwnbDate,jdbcType=VARCHAR}, #{item.repayOwneDate,jdbcType=VARCHAR},
    #{item.repayIntbDate,jdbcType=VARCHAR}, #{item.repayInteDate,jdbcType=VARCHAR},
    #{item.totalAmt,jdbcType=DECIMAL}, #{item.termRetPrin,jdbcType=DECIMAL},
    #{item.termRetInt,jdbcType=DECIMAL}, #{item.termGuarantorFee,jdbcType=DECIMAL},
    #{item.termRetFint,jdbcType=DECIMAL}, #{item.termOverdueGuarantorFee,jdbcType=DECIMAL},
    #{item.prinAmt,jdbcType=DECIMAL}, #{item.noRetAmt,jdbcType=DECIMAL},
    #{item.intAmt,jdbcType=DECIMAL}, #{item.noRetInt,jdbcType=DECIMAL},
    #{item.termFintFinish,jdbcType=DECIMAL}, #{item.noRetFin,jdbcType=DECIMAL},
    #{item.guarantorFee,jdbcType=DECIMAL}, #{item.noGuarantorFee,jdbcType=DECIMAL},
    #{item.termServiceFee,jdbcType=DECIMAL}, #{item.serviceFee,jdbcType=DECIMAL},
    #{item.noServiceFee,jdbcType=DECIMAL}, #{item.overdueGuarantorFee,jdbcType=DECIMAL},
    #{item.noOverdueGuarantorFee,jdbcType=DECIMAL}, #{item.termStatus,jdbcType=VARCHAR},
    #{item.settleFlag,jdbcType=VARCHAR}, #{item.repayTerm,jdbcType=VARCHAR},
    #{item.repayMethod,jdbcType=VARCHAR}, #{item.payTime,jdbcType=VARCHAR},
    #{item.datePay,jdbcType=VARCHAR}, #{item.datePayTime,jdbcType=VARCHAR},
    #{item.autoRepay,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP},
    #{item.updateTime,jdbcType=TIMESTAMP}, #{item.planId,jdbcType=VARCHAR},
    #{item.billId,jdbcType=VARCHAR}) </foreach> on duplicate key update
    disburse_id=values(disburse_id), user_id=values(user_id), repay_apply_no=values(repay_apply_no),
    repay_ownb_date=values(repay_ownb_date), repay_owne_date=values(repay_owne_date),
    repay_intb_date=values(repay_intb_date), repay_inte_date=values(repay_inte_date),
    total_amt=values(total_amt), term_ret_prin=values(term_ret_prin),
    term_ret_int=values(term_ret_int), term_guarantor_fee=values(term_guarantor_fee),
    term_ret_fint=values(term_ret_fint),
    term_overdue_guarantor_fee=values(term_overdue_guarantor_fee), prin_amt=values(prin_amt),
    no_ret_amt=values(no_ret_amt), int_amt=values(int_amt), no_ret_int=values(no_ret_int),
    term_fint_finish=values(term_fint_finish), no_ret_fin=values(no_ret_fin),
    guarantor_fee=values(guarantor_fee), no_guarantor_fee=values(no_guarantor_fee),
    term_service_fee=values(term_service_fee), service_fee=values(service_fee),
    no_service_fee=values(no_service_fee), overdue_guarantor_fee=values(overdue_guarantor_fee),
    no_overdue_guarantor_fee=values(no_overdue_guarantor_fee), term_status=values(term_status),
    settle_flag=values(settle_flag), repay_term=values(repay_term),
    repay_method=values(repay_method), pay_time=values(pay_time), date_pay=values(date_pay),
    date_pay_time=values(date_pay_time), auto_repay=values(auto_repay),
    create_time=values(create_time), update_time=values(update_time), plan_id=values(plan_id),
    bill_id=values(bill_id) </insert>
  <resultMap id="BaseResultMap" type="com.rongchen.byh.app.entity.RepaySchedule">
    <!--@mbg.generated-->
    <!--@Table
    `repay_schedule`-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="disburse_id" jdbcType="BIGINT" property="disburseId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="repay_apply_no" jdbcType="VARCHAR" property="repayApplyNo" />
    <result column="repay_ownb_date" jdbcType="VARCHAR" property="repayOwnbDate" />
    <result column="repay_owne_date" jdbcType="VARCHAR" property="repayOwneDate" />
    <result column="repay_intb_date" jdbcType="VARCHAR" property="repayIntbDate" />
    <result column="repay_inte_date" jdbcType="VARCHAR" property="repayInteDate" />
    <result column="total_amt" jdbcType="DECIMAL" property="totalAmt" />
    <result column="term_ret_prin" jdbcType="DECIMAL" property="termRetPrin" />
    <result column="term_ret_int" jdbcType="DECIMAL" property="termRetInt" />
    <result column="term_guarantor_fee" jdbcType="DECIMAL" property="termGuarantorFee" />
    <result column="term_ret_fint" jdbcType="DECIMAL" property="termRetFint" />
    <result column="term_overdue_guarantor_fee" jdbcType="DECIMAL"
      property="termOverdueGuarantorFee" />
    <result column="prin_amt" jdbcType="DECIMAL" property="prinAmt" />
    <result column="no_ret_amt" jdbcType="DECIMAL" property="noRetAmt" />
    <result column="int_amt" jdbcType="DECIMAL" property="intAmt" />
    <result column="no_ret_int" jdbcType="DECIMAL" property="noRetInt" />
    <result column="term_fint_finish" jdbcType="DECIMAL" property="termFintFinish" />
    <result column="no_ret_fin" jdbcType="DECIMAL" property="noRetFin" />
    <result column="guarantor_fee" jdbcType="DECIMAL" property="guarantorFee" />
    <result column="no_guarantor_fee" jdbcType="DECIMAL" property="noGuarantorFee" />
    <result column="term_service_fee" jdbcType="DECIMAL" property="termServiceFee" />
    <result column="service_fee" jdbcType="DECIMAL" property="serviceFee" />
    <result column="no_service_fee" jdbcType="DECIMAL" property="noServiceFee" />
    <result column="overdue_guarantor_fee" jdbcType="DECIMAL" property="overdueGuarantorFee" />
    <result column="no_overdue_guarantor_fee" jdbcType="DECIMAL" property="noOverdueGuarantorFee" />
    <result column="term_status" jdbcType="VARCHAR" property="termStatus" />
    <result column="settle_flag" jdbcType="VARCHAR" property="settleFlag" />
    <result column="repay_term" jdbcType="VARCHAR" property="repayTerm" />
    <result column="repay_method" jdbcType="VARCHAR" property="repayMethod" />
    <result column="pay_time" jdbcType="VARCHAR" property="payTime" />
    <result column="date_pay" jdbcType="VARCHAR" property="datePay" />
    <result column="date_pay_time" jdbcType="VARCHAR" property="datePayTime" />
    <result column="auto_repay" jdbcType="INTEGER" property="autoRepay" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="plan_id" jdbcType="VARCHAR" property="planId" />
    <result column="bill_id" jdbcType="VARCHAR" property="billId" />
  </resultMap>
  <select id="selectListByDisburseId" resultType="com.rongchen.byh.app.entity.RepaySchedule"> select <include
      refid="Base_Column_List" /> from repay_schedule where disburse_id = #{disburseId} order by id
    asc; </select>
  <select id="selectLastOne" resultType="com.rongchen.byh.app.entity.RepaySchedule"> select * FROM
    repay_schedule WHERE disburse_id = #{disburseId} and repay_intb_date &lt;= #{today} and
    settle_flag = "RUNNING" ORDER BY id asc limit 1 </select>
  <select id="selectListRepay" resultType="com.rongchen.byh.app.entity.RepaySchedule"> select * from
    repay_schedule where disburse_id in ( select disburse_id from repay_schedule where
    repay_owne_date &lt;= #{today} and settle_flag = 'RUNNING' GROUP BY disburse_id HAVING count(*) = 1 ) and repay_owne_date
    &lt;= #{today} and settle_flag = 'RUNNING' and auto_repay = 1; </select>
  <select id="selectListOverdue" resultType="com.rongchen.byh.app.entity.RepaySchedule"> select *
    from repay_schedule where repay_owne_date &lt; #{today} and settle_flag = 'RUNNING' AND
    term_status = "O" and auto_repay = 1 </select>
  <select id="selectByRepayApplyNo" resultType="com.rongchen.byh.app.entity.RepaySchedule"> select *
    from repay_schedule where repay_apply_no = #{repayApplyNo} </select>

  <select id="selectListOverdueUp" resultType="com.rongchen.byh.app.entity.DisburseData"> select *
    FROM disburse_data WHERE id in ( select disburse_id from repay_schedule where repay_owne_date
    &lt; #{today} and settle_flag = 'RUNNING' AND (term_status = "N" OR term_status = "O") AND
    auto_repay = 1 ) </select>
  <select id="selectByDisburseIdAndTerm" resultType="com.rongchen.byh.app.entity.RepaySchedule">
    select * FROM repay_schedule WHERE disburse_id = #{disburseId} and repay_term = #{term}
  </select>
  <select id="selectListRepayEq" resultType="com.rongchen.byh.app.entity.RepaySchedule"> select *
    from repay_schedule where disburse_id in ( select disburse_id from repay_schedule where
    repay_owne_date = #{today} GROUP BY disburse_id HAVING count(*) = 1 ) and repay_owne_date =
    #{today} and settle_flag = 'RUNNING' and auto_repay = 1; </select>
  <select id="seletLast" resultType="com.rongchen.byh.app.entity.RepaySchedule"> select * from
    repay_schedule <where>
      <if test="userId != null">and user_id = #{userId}</if>
            <if test="repayTerm != null">and
    repay_term = #{repayTerm}</if>
    </where> order by id desc limit 1 </select>
  <select id="repaying" resultType="com.rongchen.byh.app.entity.RepayPayVo"> select
    repay_schedule.id as repayScheduleId, repay_schedule.disburse_id as disburseId,
    repay_schedule.repay_apply_no as repayApplyNo, disburse_data.loan_no as loanNo,
    disburse_data.capital_id as capitalId, repay_schedule.user_id as userId from repay_schedule left
    join disburse_data on repay_schedule.disburse_id = disburse_data.id where
    repay_schedule.settle_flag = 'REPAYING' </select>

  <resultMap id="apiRepayPlanResult" type="com.rongchen.byh.app.v2.entity.ApiRepayPlan">
    <result column="period_no" property="period_no" />
    <result column="principal" property="principal" />
    <result column="interest" property="interest" />
    <result column="service_fee" property="service_fee" />
    <result column="overdue_fee" property="overdue_fee" />
    <result column="dueTimeStr" property="dueTimeStr" />
    <result column="amount" property="amount" />
    <result column="paid_amount" property="paid_amount" />
    <result column="billStatusStr" property="billStatusStr" />
    <result column="successTimeStr" property="successTimeStr" />
    <result column="canRepayTimeStr" property="canRepayTimeStr" />
  </resultMap>

  <select id="queryListByDisburseId" resultMap="apiRepayPlanResult">
    select
      repay_term as period_no,
      term_ret_prin as principal,
      term_ret_int as interest,
      (term_guarantor_fee + term_service_fee) as service_fee,
      term_ret_fint as overdue_fee,
      repay_inte_date as dueTimeStr,
      total_amt as amount,
      CASE
        WHEN settle_flag = 'CLOSE' THEN total_amt
        ELSE 0
        END AS paid_amount,
      CASE
        WHEN term_status = 'O' AND (settle_flag = 'RUNNING' OR settle_flag = 'REPAYING') THEN '3'
        WHEN term_status = 'O' AND settle_flag = 'CLOSE' THEN '2'
        WHEN term_status = 'N' AND (settle_flag = 'RUNNING' OR settle_flag = 'REPAYING') THEN '1'
        WHEN term_status = 'N' AND settle_flag = 'CLOSE' THEN '2'
        ELSE '1'  -- 默认值（可选）
        END AS billStatusStr,
      date_pay_time as successTimeStr,
      repay_intb_date as canRepayTimeStr
    from repay_schedule
    where disburse_id = #{disburseId}
  </select>
  <sql id="Base_Column_List">
    <!--@mbg.generated--> `id`, `disburse_id`, `user_id`, `repay_apply_no`, `repay_ownb_date`,
    `repay_owne_date`, `repay_intb_date`, `repay_inte_date`, `total_amt`, `term_ret_prin`,
    `term_ret_int`, `term_guarantor_fee`, `term_ret_fint`, `term_overdue_guarantor_fee`, `prin_amt`,
    `no_ret_amt`, `int_amt`, `no_ret_int`, `term_fint_finish`, `no_ret_fin`, `guarantor_fee`,
    `no_guarantor_fee`, `term_service_fee`, `service_fee`, `no_service_fee`,
    `overdue_guarantor_fee`, `no_overdue_guarantor_fee`, `term_status`, `settle_flag`, `repay_term`,
    `repay_method`, `pay_time`, `date_pay`, `date_pay_time`, `auto_repay`, `create_time`,
    `update_time`, `plan_id`, `bill_id` </sql>
  <update id="updateByPrimaryKeySelective" parameterType="com.rongchen.byh.app.entity.RepaySchedule">
    <!--@mbg.generated-->
    update `repay_schedule` <set>
      <if test="disburseId != null"> `disburse_id` = #{disburseId,jdbcType=BIGINT}, </if>
      <if
        test="userId != null"> `user_id` = #{userId,jdbcType=BIGINT}, </if>
      <if
        test="repayApplyNo != null"> `repay_apply_no` = #{repayApplyNo,jdbcType=VARCHAR}, </if>
      <if
        test="repayOwnbDate != null"> `repay_ownb_date` = #{repayOwnbDate,jdbcType=VARCHAR}, </if>
      <if
        test="repayOwneDate != null"> `repay_owne_date` = #{repayOwneDate,jdbcType=VARCHAR}, </if>
      <if
        test="repayIntbDate != null"> `repay_intb_date` = #{repayIntbDate,jdbcType=VARCHAR}, </if>
      <if
        test="repayInteDate != null"> `repay_inte_date` = #{repayInteDate,jdbcType=VARCHAR}, </if>
      <if
        test="totalAmt != null"> `total_amt` = #{totalAmt,jdbcType=DECIMAL}, </if>
      <if
        test="termRetPrin != null"> `term_ret_prin` = #{termRetPrin,jdbcType=DECIMAL}, </if>
      <if
        test="termRetInt != null"> `term_ret_int` = #{termRetInt,jdbcType=DECIMAL}, </if>
      <if
        test="termGuarantorFee != null"> `term_guarantor_fee` =
    #{termGuarantorFee,jdbcType=DECIMAL}, </if>
      <if test="termRetFint != null"> `term_ret_fint` =
    #{termRetFint,jdbcType=DECIMAL}, </if>
      <if test="termOverdueGuarantorFee != null">
    `term_overdue_guarantor_fee` = #{termOverdueGuarantorFee,jdbcType=DECIMAL}, </if>
      <if
        test="prinAmt != null"> `prin_amt` = #{prinAmt,jdbcType=DECIMAL}, </if>
      <if
        test="noRetAmt != null"> `no_ret_amt` = #{noRetAmt,jdbcType=DECIMAL}, </if>
      <if
        test="intAmt != null"> `int_amt` = #{intAmt,jdbcType=DECIMAL}, </if>
      <if
        test="noRetInt != null"> `no_ret_int` = #{noRetInt,jdbcType=DECIMAL}, </if>
      <if
        test="termFintFinish != null"> `term_fint_finish` = #{termFintFinish,jdbcType=DECIMAL}, </if>
      <if
        test="noRetFin != null"> `no_ret_fin` = #{noRetFin,jdbcType=DECIMAL}, </if>
      <if
        test="guarantorFee != null"> `guarantor_fee` = #{guarantorFee,jdbcType=DECIMAL}, </if>
      <if
        test="noGuarantorFee != null"> `no_guarantor_fee` = #{noGuarantorFee,jdbcType=DECIMAL}, </if>
      <if
        test="termServiceFee != null"> `term_service_fee` = #{termServiceFee,jdbcType=DECIMAL}, </if>
      <if
        test="serviceFee != null"> `service_fee` = #{serviceFee,jdbcType=DECIMAL}, </if>
      <if
        test="noServiceFee != null"> `no_service_fee` = #{noServiceFee,jdbcType=DECIMAL}, </if>
      <if
        test="overdueGuarantorFee != null"> `overdue_guarantor_fee` =
    #{overdueGuarantorFee,jdbcType=DECIMAL}, </if>
      <if test="noOverdueGuarantorFee != null">
    `no_overdue_guarantor_fee` = #{noOverdueGuarantorFee,jdbcType=DECIMAL}, </if>
      <if
        test="termStatus != null"> `term_status` = #{termStatus,jdbcType=VARCHAR}, </if>
      <if
        test="settleFlag != null"> `settle_flag` = #{settleFlag,jdbcType=VARCHAR}, </if>
      <if
        test="repayTerm != null"> `repay_term` = #{repayTerm,jdbcType=VARCHAR}, </if>
      <if
        test="repayMethod != null"> `repay_method` = #{repayMethod,jdbcType=VARCHAR}, </if>
      <if
        test="payTime != null"> `pay_time` = #{payTime,jdbcType=VARCHAR}, </if>
      <if
        test="datePay != null"> `date_pay` = #{datePay,jdbcType=VARCHAR}, </if>
      <if
        test="datePayTime != null"> `date_pay_time` = #{datePayTime,jdbcType=VARCHAR}, </if>
      <if
        test="autoRepay != null"> `auto_repay` = #{autoRepay,jdbcType=INTEGER}, </if>
      <if
        test="createTime != null"> `create_time` = #{createTime,jdbcType=TIMESTAMP}, </if>
      <if
        test="updateTime != null"> `update_time` = #{updateTime,jdbcType=TIMESTAMP}, </if>
      <if
        test="planId != null"> `plan_id` = #{planId,jdbcType=VARCHAR}, </if>
      <if
        test="billId != null"> `bill_id` = #{billId,jdbcType=VARCHAR}, </if>
    </set> where `id` =
    #{id,jdbcType=BIGINT} </update>
  <update id="updateByPrimaryKey" parameterType="com.rongchen.byh.app.entity.RepaySchedule">
    <!--@mbg.generated--> update
    `repay_schedule` set `disburse_id` = #{disburseId,jdbcType=BIGINT}, `user_id` =
    #{userId,jdbcType=BIGINT}, `repay_apply_no` = #{repayApplyNo,jdbcType=VARCHAR},
    `repay_ownb_date` = #{repayOwnbDate,jdbcType=VARCHAR}, `repay_owne_date` =
    #{repayOwneDate,jdbcType=VARCHAR}, `repay_intb_date` = #{repayIntbDate,jdbcType=VARCHAR},
    `repay_inte_date` = #{repayInteDate,jdbcType=VARCHAR}, `total_amt` =
    #{totalAmt,jdbcType=DECIMAL}, `term_ret_prin` = #{termRetPrin,jdbcType=DECIMAL}, `term_ret_int`
    = #{termRetInt,jdbcType=DECIMAL}, `term_guarantor_fee` = #{termGuarantorFee,jdbcType=DECIMAL},
    `term_ret_fint` = #{termRetFint,jdbcType=DECIMAL}, `term_overdue_guarantor_fee` =
    #{termOverdueGuarantorFee,jdbcType=DECIMAL}, `prin_amt` = #{prinAmt,jdbcType=DECIMAL},
    `no_ret_amt` = #{noRetAmt,jdbcType=DECIMAL}, `int_amt` = #{intAmt,jdbcType=DECIMAL},
    `no_ret_int` = #{noRetInt,jdbcType=DECIMAL}, `term_fint_finish` =
    #{termFintFinish,jdbcType=DECIMAL}, `no_ret_fin` = #{noRetFin,jdbcType=DECIMAL}, `guarantor_fee`
    = #{guarantorFee,jdbcType=DECIMAL}, `no_guarantor_fee` = #{noGuarantorFee,jdbcType=DECIMAL},
    `term_service_fee` = #{termServiceFee,jdbcType=DECIMAL}, `service_fee` =
    #{serviceFee,jdbcType=DECIMAL}, `no_service_fee` = #{noServiceFee,jdbcType=DECIMAL},
    `overdue_guarantor_fee` = #{overdueGuarantorFee,jdbcType=DECIMAL}, `no_overdue_guarantor_fee` =
    #{noOverdueGuarantorFee,jdbcType=DECIMAL}, `term_status` = #{termStatus,jdbcType=VARCHAR},
    `settle_flag` = #{settleFlag,jdbcType=VARCHAR}, `repay_term` = #{repayTerm,jdbcType=VARCHAR},
    `repay_method` = #{repayMethod,jdbcType=VARCHAR}, `pay_time` = #{payTime,jdbcType=VARCHAR},
    `date_pay` = #{datePay,jdbcType=VARCHAR}, `date_pay_time` = #{datePayTime,jdbcType=VARCHAR},
    `auto_repay` = #{autoRepay,jdbcType=INTEGER}, `create_time` = #{createTime,jdbcType=TIMESTAMP},
    `update_time` = #{updateTime,jdbcType=TIMESTAMP}, `plan_id` = #{planId,jdbcType=VARCHAR},
    `bill_id` = #{billId,jdbcType=VARCHAR} where `id` = #{id,jdbcType=BIGINT} </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated--> update `repay_schedule` <foreach
      close=")" collection="list" item="item" open="(" separator=", "> #{item.id,jdbcType=BIGINT} </foreach> where `id` in <trim
      prefix="set" suffixOverrides=",">
      <trim prefix="`disburse_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.disburseId,jdbcType=BIGINT} </foreach>
      </trim>
      <trim prefix="`user_id` = case"
        suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.userId,jdbcType=BIGINT} </foreach>
      </trim>
      <trim
        prefix="`repay_apply_no` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.repayApplyNo,jdbcType=VARCHAR} </foreach>
      </trim>
      <trim
        prefix="`repay_ownb_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.repayOwnbDate,jdbcType=VARCHAR} </foreach>
      </trim>
      <trim
        prefix="`repay_owne_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.repayOwneDate,jdbcType=VARCHAR} </foreach>
      </trim>
      <trim
        prefix="`repay_intb_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.repayIntbDate,jdbcType=VARCHAR} </foreach>
      </trim>
      <trim
        prefix="`repay_inte_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.repayInteDate,jdbcType=VARCHAR} </foreach>
      </trim>
      <trim
        prefix="`total_amt` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.totalAmt,jdbcType=DECIMAL} </foreach>
      </trim>
      <trim
        prefix="`term_ret_prin` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.termRetPrin,jdbcType=DECIMAL} </foreach>
      </trim>
      <trim
        prefix="`term_ret_int` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.termRetInt,jdbcType=DECIMAL} </foreach>
      </trim>
      <trim
        prefix="`term_guarantor_fee` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.termGuarantorFee,jdbcType=DECIMAL} </foreach>
      </trim>
      <trim
        prefix="`term_ret_fint` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.termRetFint,jdbcType=DECIMAL} </foreach>
      </trim>
      <trim
        prefix="`term_overdue_guarantor_fee` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.termOverdueGuarantorFee,jdbcType=DECIMAL} </foreach>
      </trim>
      <trim
        prefix="`prin_amt` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.prinAmt,jdbcType=DECIMAL} </foreach>
      </trim>
      <trim prefix="`no_ret_amt` = case"
        suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.noRetAmt,jdbcType=DECIMAL} </foreach>
      </trim>
      <trim prefix="`int_amt` = case"
        suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.intAmt,jdbcType=DECIMAL} </foreach>
      </trim>
      <trim prefix="`no_ret_int` = case"
        suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.noRetInt,jdbcType=DECIMAL} </foreach>
      </trim>
      <trim
        prefix="`term_fint_finish` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.termFintFinish,jdbcType=DECIMAL} </foreach>
      </trim>
      <trim
        prefix="`no_ret_fin` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.noRetFin,jdbcType=DECIMAL} </foreach>
      </trim>
      <trim
        prefix="`guarantor_fee` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.guarantorFee,jdbcType=DECIMAL} </foreach>
      </trim>
      <trim
        prefix="`no_guarantor_fee` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.noGuarantorFee,jdbcType=DECIMAL} </foreach>
      </trim>
      <trim
        prefix="`term_service_fee` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.termServiceFee,jdbcType=DECIMAL} </foreach>
      </trim>
      <trim
        prefix="`service_fee` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.serviceFee,jdbcType=DECIMAL} </foreach>
      </trim>
      <trim
        prefix="`no_service_fee` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.noServiceFee,jdbcType=DECIMAL} </foreach>
      </trim>
      <trim
        prefix="`overdue_guarantor_fee` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.overdueGuarantorFee,jdbcType=DECIMAL} </foreach>
      </trim>
      <trim
        prefix="`no_overdue_guarantor_fee` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.noOverdueGuarantorFee,jdbcType=DECIMAL} </foreach>
      </trim>
      <trim
        prefix="`term_status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.termStatus,jdbcType=VARCHAR} </foreach>
      </trim>
      <trim
        prefix="`settle_flag` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.settleFlag,jdbcType=VARCHAR} </foreach>
      </trim>
      <trim
        prefix="`repay_term` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.repayTerm,jdbcType=VARCHAR} </foreach>
      </trim>
      <trim
        prefix="`repay_method` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.repayMethod,jdbcType=VARCHAR} </foreach>
      </trim>
      <trim
        prefix="`pay_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.payTime,jdbcType=VARCHAR} </foreach>
      </trim>
      <trim prefix="`date_pay` = case"
        suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.datePay,jdbcType=VARCHAR} </foreach>
      </trim>
      <trim
        prefix="`date_pay_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.datePayTime,jdbcType=VARCHAR} </foreach>
      </trim>
      <trim
        prefix="`auto_repay` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.autoRepay,jdbcType=INTEGER} </foreach>
      </trim>
      <trim
        prefix="`create_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.createTime,jdbcType=TIMESTAMP} </foreach>
      </trim>
      <trim
        prefix="`update_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.updateTime,jdbcType=TIMESTAMP} </foreach>
      </trim>
      <trim
        prefix="`plan_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.planId,jdbcType=VARCHAR} </foreach>
      </trim>
      <trim prefix="`bill_id` = case"
        suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.billId,jdbcType=VARCHAR} </foreach>
      </trim>
    </trim>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated--> update
    `repay_schedule` <foreach
      close=")" collection="list" item="item" open="(" separator=", "> #{item.id,jdbcType=BIGINT} </foreach> where `id` in <trim prefix="set" suffixOverrides=",">
      <trim prefix="`disburse_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.disburseId != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.disburseId,jdbcType=BIGINT} </if>
        </foreach>
      </trim>
      <trim
        prefix="`user_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.userId != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.userId,jdbcType=BIGINT} </if>
        </foreach>
      </trim>
      <trim
        prefix="`repay_apply_no` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.repayApplyNo != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.repayApplyNo,jdbcType=VARCHAR} </if>
        </foreach>
      </trim>
      <trim
        prefix="`repay_ownb_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.repayOwnbDate != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.repayOwnbDate,jdbcType=VARCHAR} </if>
        </foreach>
      </trim>
      <trim
        prefix="`repay_owne_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.repayOwneDate != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.repayOwneDate,jdbcType=VARCHAR} </if>
        </foreach>
      </trim>
      <trim
        prefix="`repay_intb_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.repayIntbDate != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.repayIntbDate,jdbcType=VARCHAR} </if>
        </foreach>
      </trim>
      <trim
        prefix="`repay_inte_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.repayInteDate != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.repayInteDate,jdbcType=VARCHAR} </if>
        </foreach>
      </trim>
      <trim
        prefix="`total_amt` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.totalAmt != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.totalAmt,jdbcType=DECIMAL} </if>
        </foreach>
      </trim>
      <trim
        prefix="`term_ret_prin` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.termRetPrin != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.termRetPrin,jdbcType=DECIMAL} </if>
        </foreach>
      </trim>
      <trim
        prefix="`term_ret_int` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.termRetInt != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.termRetInt,jdbcType=DECIMAL} </if>
        </foreach>
      </trim>
      <trim
        prefix="`term_guarantor_fee` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.termGuarantorFee != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.termGuarantorFee,jdbcType=DECIMAL} </if>
        </foreach>
      </trim>
      <trim
        prefix="`term_ret_fint` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.termRetFint != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.termRetFint,jdbcType=DECIMAL} </if>
        </foreach>
      </trim>
      <trim
        prefix="`term_overdue_guarantor_fee` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.termOverdueGuarantorFee != null"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.termOverdueGuarantorFee,jdbcType=DECIMAL} </if>
        </foreach>
      </trim>
      <trim
        prefix="`prin_amt` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.prinAmt != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.prinAmt,jdbcType=DECIMAL} </if>
        </foreach>
      </trim>
      <trim
        prefix="`no_ret_amt` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.noRetAmt != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.noRetAmt,jdbcType=DECIMAL} </if>
        </foreach>
      </trim>
      <trim
        prefix="`int_amt` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.intAmt != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.intAmt,jdbcType=DECIMAL} </if>
        </foreach>
      </trim>
      <trim
        prefix="`no_ret_int` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.noRetInt != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.noRetInt,jdbcType=DECIMAL} </if>
        </foreach>
      </trim>
      <trim
        prefix="`term_fint_finish` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.termFintFinish != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.termFintFinish,jdbcType=DECIMAL} </if>
        </foreach>
      </trim>
      <trim
        prefix="`no_ret_fin` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.noRetFin != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.noRetFin,jdbcType=DECIMAL} </if>
        </foreach>
      </trim>
      <trim
        prefix="`guarantor_fee` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.guarantorFee != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.guarantorFee,jdbcType=DECIMAL} </if>
        </foreach>
      </trim>
      <trim
        prefix="`no_guarantor_fee` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.noGuarantorFee != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.noGuarantorFee,jdbcType=DECIMAL} </if>
        </foreach>
      </trim>
      <trim
        prefix="`term_service_fee` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.termServiceFee != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.termServiceFee,jdbcType=DECIMAL} </if>
        </foreach>
      </trim>
      <trim
        prefix="`service_fee` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.serviceFee != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.serviceFee,jdbcType=DECIMAL} </if>
        </foreach>
      </trim>
      <trim
        prefix="`no_service_fee` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.noServiceFee != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.noServiceFee,jdbcType=DECIMAL} </if>
        </foreach>
      </trim>
      <trim
        prefix="`overdue_guarantor_fee` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.overdueGuarantorFee != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.overdueGuarantorFee,jdbcType=DECIMAL} </if>
        </foreach>
      </trim>
      <trim
        prefix="`no_overdue_guarantor_fee` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.noOverdueGuarantorFee != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.noOverdueGuarantorFee,jdbcType=DECIMAL} </if>
        </foreach>
      </trim>
      <trim
        prefix="`term_status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.termStatus != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.termStatus,jdbcType=VARCHAR} </if>
        </foreach>
      </trim>
      <trim
        prefix="`settle_flag` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.settleFlag != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.settleFlag,jdbcType=VARCHAR} </if>
        </foreach>
      </trim>
      <trim
        prefix="`repay_term` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.repayTerm != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.repayTerm,jdbcType=VARCHAR} </if>
        </foreach>
      </trim>
      <trim
        prefix="`repay_method` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.repayMethod != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.repayMethod,jdbcType=VARCHAR} </if>
        </foreach>
      </trim>
      <trim
        prefix="`pay_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.payTime != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.payTime,jdbcType=VARCHAR} </if>
        </foreach>
      </trim>
      <trim
        prefix="`date_pay` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.datePay != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.datePay,jdbcType=VARCHAR} </if>
        </foreach>
      </trim>
      <trim
        prefix="`date_pay_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.datePayTime != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.datePayTime,jdbcType=VARCHAR} </if>
        </foreach>
      </trim>
      <trim
        prefix="`auto_repay` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.autoRepay != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.autoRepay,jdbcType=INTEGER} </if>
        </foreach>
      </trim>
      <trim
        prefix="`create_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.createTime,jdbcType=TIMESTAMP} </if>
        </foreach>
      </trim>
      <trim
        prefix="`update_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.updateTime,jdbcType=TIMESTAMP} </if>
        </foreach>
      </trim>
      <trim
        prefix="`plan_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.planId != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.planId,jdbcType=VARCHAR} </if>
        </foreach>
      </trim>
      <trim
        prefix="`bill_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.billId != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.billId,jdbcType=VARCHAR} </if>
        </foreach>
      </trim>
    </trim>
  </update>
</mapper>