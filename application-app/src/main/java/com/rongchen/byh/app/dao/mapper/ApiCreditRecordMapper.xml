<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.app.dao.ApiCreditRecordMapper">

    <resultMap id="BaseResultMap" type="com.rongchen.byh.app.entity.ApiCreditRecord">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="creditNo" column="credit_no" jdbcType="VARCHAR"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="outUserId" column="out_user_id" jdbcType="VARCHAR"/>
            <result property="channel" column="channel" jdbcType="VARCHAR"/>
            <result property="creditStatus" column="credit_status" jdbcType="INTEGER"/>
            <result property="failReason" column="fail_reason" jdbcType="VARCHAR"/>
            <result property="creditMoney" column="credit_money" jdbcType="DECIMAL"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,credit_no,user_id,
        out_user_id,channel,credit_status,fail_reason,
        credit_money,create_time,update_time
    </sql>
    <update id="updateByCreditNo">
        update api_credit_record
        <set>
            <if test="creditStatus != null">
                credit_status = #{creditStatus},
            </if>
            <if test="failReason != null">
                fail_reason = #{failReason},
            </if>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="outUserId != null">
                out_user_id = #{outUserId},
            </if>
            <if test="creditMoney != null">
                credit_money = #{creditMoney},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where credit_no = #{creditNo}
    </update>
    <update id="batchFail">
        update api_credit_record
        set credit_status = 3,
        fail_reason = '授信失败'
        where id in
        <foreach item="item" collection="list" separator="," open="(" close=")">
            #{item.id}
        </foreach>
        and credit_status = 1
    </update>
    <select id="selectByCreditNo" resultType="com.rongchen.byh.app.entity.ApiCreditRecord">
        select *
        from api_credit_record
        where credit_no = #{creditNo}
    </select>
    <select id="selectInProcessList" resultType="com.rongchen.byh.app.entity.ApiCreditRecord">
        select *
        from api_credit_record
        where credit_status = 1
          and create_time &lt;= DATE_SUB(NOW(), INTERVAL ${hour} HOUR)
    </select>
</mapper>
