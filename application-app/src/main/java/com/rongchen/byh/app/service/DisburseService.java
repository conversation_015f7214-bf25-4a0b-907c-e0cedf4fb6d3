package com.rongchen.byh.app.service;

import com.rongchen.byh.app.dto.app.BackVo;
import com.rongchen.byh.app.dto.app.LoanApplyDto;
import com.rongchen.byh.app.dto.app.TrialPaymentDto;
import com.rongchen.byh.app.entity.DisburseData;
import com.rongchen.byh.app.entity.UserCreditData;
import com.rongchen.byh.app.vo.app.DisburseAmountVo;
import com.rongchen.byh.app.vo.app.DisburseStatusVo;
import com.rongchen.byh.app.vo.app.WebSaleUrlVo;
import com.rongchen.byh.common.api.zifang.dto.BindBankSmsDto;
import com.rongchen.byh.common.api.zifang.dto.QueryBindBankResultDto;
import com.rongchen.byh.common.api.zifang.dto.VerifyBindBankSmsDto;
import com.rongchen.byh.common.api.zifang.vo.*;
import com.rongchen.byh.common.core.object.ResponseResult;

import java.util.List;

/**
 * @ClassName DisburseService
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/12 16:09
 * @Version 1.0
 **/
public interface DisburseService {

    ResponseResult<List<BackVo>> backList();

    ResponseResult<RepayPlanCalcVo> trialPayment(TrialPaymentDto dto);

    ResponseResult<LoanElementVo> bindBackPay();


    ResponseResult<BindBankSmsVo> bindSendCode(BindBankSmsDto dto);

    ResponseResult<VerifyBindBankSmsVo> verifyBindSend(VerifyBindBankSmsDto dto);

    ResponseResult<QueryBindBankResultVo> bindBankResult(QueryBindBankResultDto dto);

    ResponseResult<Void> loanApply(LoanApplyDto dto);

    ResponseResult<DisburseStatusVo> queryCreditStatus();

    ResponseResult<DisburseAmountVo> disburseAmount();

    ResponseResult<WebSaleUrlVo> webSaleUrl();

    ResponseResult<LoanElementVo> newBindBackPay();

    ResponseResult<BindBankSmsVo> newBindSendCode(BindBankSmsDto dto);

    ResponseResult<VerifyBindBankSmsVo> newVerifyBindSend(VerifyBindBankSmsDto dto);

    DisburseData selectOneDisburse(DisburseData disburseData);

    ResponseResult<Void> checkOverDisburse(Long disburseId);
}
