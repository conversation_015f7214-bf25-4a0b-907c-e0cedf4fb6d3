package com.rongchen.byh.app.dto.api;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @ClassName FenZhuanLoanNoticeDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/10 17:23
 * @Version 1.0
 **/
@Data
public class FenZhuanLoanNoticeDto {

    /**
     * 进件单号
     */
    private String orderNo;

    /**
     * 借款请求单号
     */
    private String loanRequestNo;

    /**
     * 借款单号
     */
    private String loanOrderNo;

    /**
     * 订单状态
     */
    private String loanStatus;

    /**
     * 订单更新时间，格式：yyyy-MM-dd HH:mm:ss
     */
    private String updateTime;

    /**
     * 拒绝原因，不通过时必填
     */
    private String rejectReasons;

    /**
     * 放款时间，格式：yyyy-MM-dd HH:mm:ss，放款成功必填
     */
    private String loanDate;

    /**
     * 放款金额，单位：元，放款成功必填
     */
    private BigDecimal loanAmount;

    /**
     * 合同金额，单位：元，放款成功必填
     */
    private BigDecimal contAmount;

    /**
     * 年综合费率
     */
    private BigDecimal yearRate;

    /**
     * 重路由标识，订单状态回退，非空即为重路由
     */
    private Integer terminationType;

    /**
     * 复贷标识：0 非复贷 1 复贷，放款后有值
     */
    private Integer repeatFlag;
}
