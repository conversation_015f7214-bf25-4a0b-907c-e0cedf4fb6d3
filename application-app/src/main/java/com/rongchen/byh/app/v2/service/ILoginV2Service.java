package com.rongchen.byh.app.v2.service;

import com.rongchen.byh.app.dto.OfflineRegOrLoginDto;
import com.rongchen.byh.app.dto.RegOrLoginDto;
import com.rongchen.byh.app.dto.YzmCodeDto;
import com.rongchen.byh.app.dto.app.AppLoginDto;
import com.rongchen.byh.app.v2.dto.LoginH5Dto;
import com.rongchen.byh.app.vo.RegOrLoginVo;
import com.rongchen.byh.app.vo.app.RegisterUserVo;
import com.rongchen.byh.common.core.object.ResponseResult;

public interface ILoginV2Service {

    /**
     * 发送验证码
     * @param yzmCodeDto
     * @return
     */
    ResponseResult<Void> sendCode(YzmCodeDto yzmCodeDto);


    ResponseResult<RegOrLoginVo> loginByH5(LoginH5Dto loginH5Dto);

    ResponseResult<Void> checkInviteCode(LoginH5Dto dto);

    ResponseResult<RegisterUserVo> loginByApp(AppLoginDto appLoginDto);
}
