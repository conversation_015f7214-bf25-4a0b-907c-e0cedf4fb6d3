package com.rongchen.byh.app.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rongchen.byh.app.dao.DisburseDataMapper;
import com.rongchen.byh.app.entity.DisburseData;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * 项目名称：byh_java
 * 文件名称: DisburseDataService
 * 创建时间: 2025-04-05 14:14
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.app.service
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
@Service
public class DisburseDataService extends ServiceImpl<DisburseDataMapper, DisburseData> {


    public int updateByPrimaryKey(DisburseData record) {
        return baseMapper.updateByPrimaryKey(record);
    }

    public int insertSelective(DisburseData record) {
        return baseMapper.insertSelective(record);
    }

    public int updateByPrimaryKeySelective(DisburseData record) {
        return baseMapper.updateByPrimaryKeySelective(record);
    }

    public int updateBatch(List<DisburseData> list) {
        return baseMapper.updateBatch(list);
    }

    public int updateBatchSelective(List<DisburseData> list) {
        return baseMapper.updateBatchSelective(list);
    }

    public int batchInsert(List<DisburseData> list) {
        return baseMapper.batchInsert(list);
    }

    public int batchInsertSelectiveUseDefaultForNull(List<DisburseData> list) {
        return baseMapper.batchInsertSelectiveUseDefaultForNull(list);
    }

    public int batchInsertOrUpdate(List<DisburseData> list) {
        return baseMapper.batchInsertOrUpdate(list);
    }
}

