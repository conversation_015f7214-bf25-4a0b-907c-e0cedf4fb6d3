package com.rongchen.byh.app.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**

 * 项目名称：byh_java
 * 文件名称: BaofuPayRecord
 * 创建时间: 2025-03-13 18:43
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.app.entity
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */

/**
 * 宝付支付记录表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "`baofu_pay_record`")
public class BaofuPayRecord implements Serializable {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField(value = "`user_id`")
    private String userId;

    /**
     * 交易订单号
     */
    @TableField(value = "`trans_id`")
    private String transId;

    /**
     * 原始交易订单号
     */
    @TableField(value = "`original_trans_id`")
    private String originalTransId;

    /**
     * 宝付订单号
     */
    @TableField(value = "`ou_pay_order_on`")
    private String ouPayOrderOn;

    /**
     * 流水号
     */
    @TableField(value = "`msg_id`")
    private String msgId;

    /**
     * 签约协议号
     */
    @TableField(value = "`protocol_no`")
    private String protocolNo;

    /**
     * 交易金额(分)
     */
    @TableField(value = "`txn_amt`")
    private String txnAmt;

    /**
     * 重试次数
     */
    @TableField(value = "`retry_count`")
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    @TableField(value = "`max_retry_count`")
    private Integer maxRetryCount;

    /**
     * 最后重试时间
     */
    @TableField(value = "`last_retry_time`")
    private Date lastRetryTime;

    /**
     * 首次失败时间
     */
    @TableField(value = "`first_error_time`")
    private Date firstErrorTime;

    /**
     * 支付状态: INIT/SUCCESS/FAIL
     */
    @TableField(value = "`pay_status`")
    private String payStatus;

    /**
     * 响应码
     */
    @TableField(value = "`msg_code`")
    private String msgCode;

    /**
     * 错误信息
     */
    @TableField(value = "`error_message`")
    private String errorMessage;

    /**
     * 是否已告警通知: 0否 1是
     */
    @TableField(value = "`notify_status`")
    private Boolean notifyStatus;

    /**
     * 支付成功时间
     */
    @TableField(value = "`succeed_time`")
    private Date succeedTime;

    /**
     * 成功金额(单位:分)
     */
    @TableField(value = "`succeed_amt`")
    private String succeedAmt;

    /**
     * 是否已处理权益购买: 0否 1是
     */
    @TableField(value = "`rights_purchased`")
    private Boolean rightsPurchased;

    /**
     * 权益购买状态: SUCCESS/FAIL/FAIL_MAX_RETRY
     */
    @TableField(value = "`rights_status`")
    private String rightsStatus;

    /**
     * 权益订单号
     */
    @TableField(value = "`rights_order_no`")
    private String rightsOrderNo;

    /**
     * 权益错误信息
     */
    @TableField(value = "`rights_message`")
    private String rightsMessage;

    /**
     * 权益失败次数
     */
    @TableField(value = "`rights_fail_count`")
    private Integer rightsFailCount;

    /**
     * 创建时间
     */
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}