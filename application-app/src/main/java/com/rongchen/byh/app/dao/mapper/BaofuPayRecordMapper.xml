<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.app.dao.BaofuPayRecordMapper">
  <resultMap id="BaseResultMap" type="com.rongchen.byh.app.entity.BaofuPayRecord">
    <!--@mbg.generated-->
    <!--@Table `baofu_pay_record`-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="trans_id" jdbcType="VARCHAR" property="transId" />
    <result column="original_trans_id" jdbcType="VARCHAR" property="originalTransId" />
    <result column="ou_pay_order_on" jdbcType="VARCHAR" property="ouPayOrderOn" />
    <result column="msg_id" jdbcType="VARCHAR" property="msgId" />
    <result column="protocol_no" jdbcType="VARCHAR" property="protocolNo" />
    <result column="txn_amt" jdbcType="VARCHAR" property="txnAmt" />
    <result column="retry_count" jdbcType="INTEGER" property="retryCount" />
    <result column="max_retry_count" jdbcType="INTEGER" property="maxRetryCount" />
    <result column="last_retry_time" jdbcType="TIMESTAMP" property="lastRetryTime" />
    <result column="first_error_time" jdbcType="TIMESTAMP" property="firstErrorTime" />
    <result column="pay_status" jdbcType="VARCHAR" property="payStatus" />
    <result column="msg_code" jdbcType="VARCHAR" property="msgCode" />
    <result column="error_message" jdbcType="VARCHAR" property="errorMessage" />
    <result column="notify_status" jdbcType="BOOLEAN" property="notifyStatus" />
    <result column="succeed_time" jdbcType="TIMESTAMP" property="succeedTime" />
    <result column="succeed_amt" jdbcType="VARCHAR" property="succeedAmt" />
    <result column="rights_purchased" jdbcType="BOOLEAN" property="rightsPurchased" />
    <result column="rights_status" jdbcType="VARCHAR" property="rightsStatus" />
    <result column="rights_order_no" jdbcType="VARCHAR" property="rightsOrderNo" />
    <result column="rights_message" jdbcType="VARCHAR" property="rightsMessage" />
    <result column="rights_fail_count" jdbcType="INTEGER" property="rightsFailCount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `id`, `user_id`, `trans_id`, `original_trans_id`, `ou_pay_order_on`, `msg_id`, `protocol_no`, 
    `txn_amt`, `retry_count`, `max_retry_count`, `last_retry_time`, `first_error_time`, 
    `pay_status`, `msg_code`, `error_message`, `notify_status`, `succeed_time`, `succeed_amt`, 
    `rights_purchased`, `rights_status`, `rights_order_no`, `rights_message`, `rights_fail_count`, 
    `create_time`, `update_time`
  </sql>
  <update id="updateByPrimaryKey" parameterType="com.rongchen.byh.app.entity.BaofuPayRecord">
    <!--@mbg.generated-->
    update `baofu_pay_record`
    set `user_id` = #{userId,jdbcType=VARCHAR},
      `trans_id` = #{transId,jdbcType=VARCHAR},
      `original_trans_id` = #{originalTransId,jdbcType=VARCHAR},
      `ou_pay_order_on` = #{ouPayOrderOn,jdbcType=VARCHAR},
      `msg_id` = #{msgId,jdbcType=VARCHAR},
      `protocol_no` = #{protocolNo,jdbcType=VARCHAR},
      `txn_amt` = #{txnAmt,jdbcType=VARCHAR},
      `retry_count` = #{retryCount,jdbcType=INTEGER},
      `max_retry_count` = #{maxRetryCount,jdbcType=INTEGER},
      `last_retry_time` = #{lastRetryTime,jdbcType=TIMESTAMP},
      `first_error_time` = #{firstErrorTime,jdbcType=TIMESTAMP},
      `pay_status` = #{payStatus,jdbcType=VARCHAR},
      `msg_code` = #{msgCode,jdbcType=VARCHAR},
      `error_message` = #{errorMessage,jdbcType=VARCHAR},
      `notify_status` = #{notifyStatus,jdbcType=BOOLEAN},
      `succeed_time` = #{succeedTime,jdbcType=TIMESTAMP},
      `succeed_amt` = #{succeedAmt,jdbcType=VARCHAR},
      `rights_purchased` = #{rightsPurchased,jdbcType=BOOLEAN},
      `rights_status` = #{rightsStatus,jdbcType=VARCHAR},
      `rights_order_no` = #{rightsOrderNo,jdbcType=VARCHAR},
      `rights_message` = #{rightsMessage,jdbcType=VARCHAR},
      `rights_fail_count` = #{rightsFailCount,jdbcType=INTEGER},
      `create_time` = #{createTime,jdbcType=TIMESTAMP},
      `update_time` = #{updateTime,jdbcType=TIMESTAMP}
    where `id` = #{id,jdbcType=BIGINT}
  </update>
</mapper>