package com.rongchen.byh.app.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rongchen.byh.app.config.ZifangFactory;
import com.rongchen.byh.app.dao.CapitalDataMapper;
import com.rongchen.byh.app.dao.DisburseDataMapper;
import com.rongchen.byh.app.entity.CapitalData;
import com.rongchen.byh.app.entity.DisburseData;
import com.rongchen.byh.app.entity.RepaySchedule;
import com.rongchen.byh.common.api.zifang.dto.RepaymentPlanQueryDto;
import com.rongchen.byh.common.api.zifang.service.RepaymentApi;
import com.rongchen.byh.common.api.zifang.vo.RepaymentPlanQueryPkgVo;
import com.rongchen.byh.common.api.zifang.vo.RepaymentPlanQueryVo;
import com.rongchen.byh.common.core.constant.SettleFlagConstant;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.MDCUtil;
import com.rongchen.byh.common.redis.util.CommonRedisUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

@Service
@Slf4j
public class RepayPlanUpdateService {

    @Resource
    private DisburseDataMapper disburseDataMapper;
    @Resource
    private CapitalDataMapper capitalDataMapper;
    @Resource
    private ZifangFactory zifangFactory;
    @Resource
    private RepayScheduleService repayScheduleService;
    @Resource
    private DisburseRecordService disburseRecordService;
    @Resource
    private PlatformTransactionManager transactionManager;
    @Resource
    private CommonRedisUtil commonRedisUtil;

    public void triggerRepayPlanUpdate(Long userId) {
        String traceId = MDCUtil.setTraceId();
        log.info("【还款计划更新】任务开始，userId: {}, traceId: {}", userId, traceId);

        String lockKey = Constants.LOCK_KEY_PREFIX + userId;
        RLock lock = commonRedisUtil.getLock(lockKey);
        boolean lockAcquired = false;
        Long disburseId = null; // Initialize disburseId for logging in finally block

        try {
            // 尝试获取分布式锁
            lockAcquired = lock.tryLock(Constants.LOCK_WAIT_TIME, Constants.LOCK_LEASE_TIME, TimeUnit.SECONDS);
            if (!lockAcquired) {
                log.warn("【还款计划更新】获取分布式锁失败，可能已有任务在处理，userId: {}, lockKey: {}", userId, lockKey);
                return;
            }
            log.info("【还款计划更新】成功获取分布式锁，userId: {}, lockKey: {}", userId, lockKey);

            // 1. 查询用户最新的"还款中"的支用记录
            DisburseData disburseData = findLatestRepayingDisburse(userId);
            if (disburseData == null) {
                // 日志已在 findLatestRepayingDisburse 中记录
                return;
            }
            disburseId = disburseData.getId(); // Assign disburseId after successful retrieval

            // 2. 检查状态 (此检查已移入 findLatestRepayingDisburse，但保留日志确认)
            log.info("【还款计划更新】找到需更新的借款记录，disburseId: {}, userId: {}", disburseId, userId);

            // 3. 获取资方信息
            CapitalData capitalData = fetchCapitalData(disburseData.getCapitalId(), disburseId, userId);
            if (capitalData == null) {
                disburseRecordService.saveRecord(Constants.ERROR_TYPE_GENERAL,
                        "借款(disburseData)id:" + disburseId, traceId,
                        "更新失败:无法找到资方信息");
                return;
            }
            log.info("【还款计划更新】获取到资方信息，capitalId: {}, beanName: {}, userId: {}", capitalData.getId(),
                    capitalData.getBeanName(), userId);

            // 4. 获取资方API
            RepaymentApi api = getRepaymentApiProvider(capitalData.getBeanName(), disburseId, userId);
            if (api == null) {
                disburseRecordService.saveRecord(Constants.ERROR_TYPE_GENERAL,
                        "借款(disburseData)id:" + disburseId, traceId,
                        "更新失败:无法获取资方API实例");
                return;
            }
            log.info("【还款计划更新】成功获取资方API实例，准备查询还款计划，userId: {}", userId);

            // 5. 查询资方还款计划 (带重试)
            ResponseResult<RepaymentPlanQueryVo> result = fetchRemoteRepaymentPlan(api, disburseData, traceId);

            // 6. 处理资方响应
            if (result == null || !result.isSuccess()) {
                log.error("【还款计划更新】查询资方还款计划接口调用失败(重试后)，disburseId: {}, userId: {}", disburseId,
                        userId);
                disburseRecordService.saveRecord(Constants.ERROR_TYPE_GENERAL,
                        "借款(disburseData)id:" + disburseId, traceId,
                        "查询资方还款计划接口失败(重试后)");
                return;
            }

            RepaymentPlanQueryVo data = result.getData();
            if (data == null || !Constants.SUCCESS_CODE.equals(data.getResponseCode())) {
                String errorMsg = (data != null) ? data.getResponseMsg() : "返回数据为空";
                String responseCode = (data != null) ? data.getResponseCode() : "N/A";
                log.error("【还款计划更新】查询资方还款计划返回错误，disburseId: {}, code: {}, msg: {}, userId: {}",
                        disburseId, responseCode, errorMsg, userId);
                disburseRecordService.saveRecord(Constants.ERROR_TYPE_GENERAL,
                        "借款(disburseData)id:" + disburseId, traceId,
                        "查询资方还款计划返回错误: " + errorMsg);
                return;
            }
            log.info("【还款计划更新】查询资方还款计划成功，disburseId: {}, userId: {}", disburseId, userId);

            // 7. 查询本地还款计划
            Map<String, RepaySchedule> localMap = getLocalRepaySchedulesMap(userId, disburseId);
            if (localMap == null || localMap.isEmpty()) {
                // 日志已在 getLocalRepaySchedulesMap 中记录
                return; // 没有本地还款计划，跳过更新
            }

            // 8. 对比并准备更新数据
            List<RepaymentPlanQueryPkgVo> remotePlanList = data.getPkgList();
            if (remotePlanList == null || remotePlanList.isEmpty()) {
                log.warn("【还款计划更新】资方返回的还款计划列表为空，disburseId: {}，无法进行更新，userId: {}",
                        disburseId, userId);
                return;
            }

            UpdatePreparation preparation = prepareSchedulesForUpdate(remotePlanList, localMap, disburseId,
                    userId);
            List<RepaySchedule> saveList = preparation.schedulesToUpdate();
            BigDecimal totalRet = preparation.totalInterest();

            log.info("【还款计划更新】准备更新 {} 条状态为 RUNNING 的还款计划记录，计算总利息: {}，disburseId: {}, userId: {}",
                    saveList.size(), totalRet, disburseId, userId);

            // 9. 执行更新 (事务包裹)
            if (!saveList.isEmpty()) {
                performDatabaseUpdatesInTransaction(saveList, totalRet, disburseId, userId, traceId);
            } else {
                log.info("【还款计划更新】没有需要更新的（状态为RUNNING的）还款计划记录，disburseId: {}, userId: {}",
                        disburseId, userId);
            }

            log.info("【还款计划更新】任务成功完成，userId: {}, disburseId: {}", userId, disburseId);

        } catch (InterruptedException e) {
            // 处理 tryLock 中断异常
            Thread.currentThread().interrupt();
            log.error("【还款计划更新】获取锁时线程被中断，userId: {}, traceId: {}, error: ", userId, traceId, e);
        } catch (Exception e) {
            log.error("【还款计划更新】任务执行异常，userId: {}, traceId: {}, error: ", userId, traceId, e);
            // 使用局部变量 disburseId，如果它在异常发生前被赋值了
            String errorSubject = (disburseId != null) ? "借款(disburseData)id:" + disburseId
                    : "用户id:" + userId;
            disburseRecordService.saveRecord(Constants.ERROR_TYPE_GENERAL, errorSubject, traceId,
                    "更新异常:" + e.getMessage());
        } finally {
            // 确保释放锁
            if (lockAcquired && lock.isHeldByCurrentThread()) {
                lock.unlock();
                log.info("【还款计划更新】分布式锁已释放，userId: {}, lockKey: {}", userId, lockKey);
            }
            MDCUtil.clear();
            log.debug("【还款计划更新】MDC cleared for traceId: {}, userId: {}", traceId, userId);
        }
    }

    /**
     * 查询用户最新的"还款中"的支用记录
     *
     * @param userId 用户ID
     * @return DisburseData 或 null
     */
    private DisburseData findLatestRepayingDisburse(Long userId) {
        log.info("【还款计划更新】开始查询用户最新还款中借款记录，userId: {}", userId);
        QueryWrapper<DisburseData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                .eq("credit_status", Constants.CREDIT_STATUS_REPAYING) // 使用常量
                .orderByDesc("id")
                .last("LIMIT 1");

        DisburseData disburseData = disburseDataMapper.selectOne(queryWrapper);

        if (disburseData == null) {
            log.info("【还款计划更新】用户无还款中({})借款记录，无需更新，userId: {}",
                    Constants.CREDIT_STATUS_REPAYING, userId);
            return null;
        }
        // 此处无需再检查状态，因为查询条件已包含
        log.info("【还款计划更新】查询到用户还款中借款记录，disburseId: {}, userId: {}", disburseData.getId(),
                userId);
        return disburseData;
    }

    /**
     * 获取资方信息
     *
     * @param capitalId  资方ID
     * @param disburseId 借款ID (用于日志)
     * @param userId     用户ID (用于日志)
     * @return CapitalData 或 null
     */
    private CapitalData fetchCapitalData(Long capitalId, Long disburseId, Long userId) {
        log.info("【还款计划更新】开始获取资方信息，capitalId: {}, disburseId: {}, userId: {}", capitalId,
                disburseId, userId);
        CapitalData capitalData = capitalDataMapper.selectById(capitalId);
        if (capitalData == null) {
            log.error("【还款计划更新】无法找到资方信息，capitalId: {}, disburseId: {}, userId: {}", capitalId,
                    disburseId, userId);
            return null;
        }
        return capitalData;
    }

    /**
     * 获取资方API实例
     *
     * @param beanName   资方Bean名称
     * @param disburseId 借款ID (用于日志)
     * @param userId     用户ID (用于日志)
     * @return RepaymentApi 或 null
     */
    private RepaymentApi getRepaymentApiProvider(String beanName, Long disburseId, Long userId) {
        log.info("【还款计划更新】开始获取资方API实例，beanName: {}, disburseId: {}, userId: {}", beanName,
                disburseId, userId);
        RepaymentApi api = zifangFactory.getApi(beanName, RepaymentApi.class);
        if (api == null) {
            log.error("【还款计划更新】无法获取资方API实例，beanName: {}, disburseId: {}, userId: {}", beanName,
                    disburseId, userId);
            return null;
        }
        return api;
    }

    /**
     * 调用资方API查询还款计划 (带重试)
     *
     * @param api          资方API实例
     * @param disburseData 借款信息
     * @param traceId      追踪ID
     * @return ResponseResult 或 null (如果所有重试失败 或 发生异常)
     */
    private ResponseResult<RepaymentPlanQueryVo> fetchRemoteRepaymentPlan(RepaymentApi api,
            DisburseData disburseData, String traceId) {
        RepaymentPlanQueryDto queryDto = new RepaymentPlanQueryDto();
        queryDto.setUserId(disburseData.getUserId() + "");
        queryDto.setLoanNo(disburseData.getLoanNo());
        Long disburseId = disburseData.getId();
        Long userId = disburseData.getUserId();

        ResponseResult<RepaymentPlanQueryVo> lastResult = null; // Store the last result here
        for (int attempt = 1; attempt <= Constants.MAX_RETRY_TIMES; attempt++) {
            log.info("【还款计划更新】第 {}/{} 次尝试调用资方还款计划查询接口，userId: {}, loanNo: {}", attempt,
                    Constants.MAX_RETRY_TIMES, queryDto.getUserId(), queryDto.getLoanNo());
            try {
                lastResult = api.getRepaymentPlanQuery(queryDto); // Assign to lastResult
                // 检查是否成功，如果成功则跳出重试循环
                if (lastResult != null && lastResult.isSuccess() && lastResult.getData() != null
                        && Constants.SUCCESS_CODE.equals(lastResult.getData().getResponseCode())) {
                    log.info("【还款计划更新】第 {} 次尝试成功获取资方还款计划，disburseId: {}, userId: {}", attempt,
                            disburseId, userId);
                    return lastResult; // Return successful result immediately
                } else {
                    String errorMsg = "接口调用失败";
                    if (lastResult != null && lastResult.getData() != null) {
                        errorMsg = "返回错误码: " + lastResult.getData().getResponseCode() + ", 消息: "
                                + lastResult.getData().getResponseMsg();
                    } else if (lastResult != null) {
                        errorMsg = "接口调用成功但无有效数据或响应码错误";
                    } else {
                        errorMsg = "接口调用返回null";
                    }
                    log.warn("【还款计划更新】第 {} 次尝试查询资方还款计划失败，disburseId: {}, userId: {}, 原因: {}",
                            attempt, disburseId, userId, errorMsg);
                }
            } catch (Exception e) {
                log.error("【还款计划更新】第 {} 次尝试调用资方接口时发生异常，disburseId: {}, userId: {}, error: ",
                        attempt, disburseId, userId, e);
                //不要在这里创建 ResponseResult，只记录异常。
                // lastResult 将保存上一次尝试的结果（如果第一次尝试，则为 null。）
                // 后续重试时会使用 lastResult 作为上一次尝试的结果（如果第一次尝试失败，则会重试第一次尝试，并记录第一次尝试的结果。）
                // 因此，这里只记录异常，不创建 ResponseResult。

            }

            // 如果不是最后一次尝试，则等待后重试
            if (attempt < Constants.MAX_RETRY_TIMES) {
                try {
                    long delay = Constants.INITIAL_RETRY_DELAY_MS * (long) Math.pow(2, attempt - 1); // 指数退避
                    delay = Math.min(delay, 5000L); // 限制最大延迟，防止过长等待
                    log.info("【还款计划更新】准备在 {} ms后进行第 {} 次重试，disburseId: {}, userId: {}", delay,
                            attempt + 1, disburseId, userId);
                    Thread.sleep(delay);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    log.error("【还款计划更新】重试等待时线程被中断，disburseId: {}, userId: {}, error: ", disburseId,
                            userId, ie);
                    return lastResult; // 返回当前结果
                }
            }
        }
        log.error("【还款计划更新】查询资方还款计划在 {} 次尝试后最终失败或发生异常，disburseId: {}, userId: {}",
                Constants.MAX_RETRY_TIMES, disburseId, userId);
        return lastResult; // 返回最后一次尝试的结果 (可能是成功、失败或null)
    }

    /**
     * 查询并构建本地还款计划Map
     *
     * @param userId     用户ID
     * @param disburseId 借款ID
     * @return Map<String, RepaySchedule> 或 null (如果无记录)
     */
    private Map<String, RepaySchedule> getLocalRepaySchedulesMap(Long userId, Long disburseId) {
        log.info("【还款计划更新】开始查询本地还款计划记录，userId: {}, disburseId: {}", userId, disburseId);
        List<RepaySchedule> localList = repayScheduleService.list(
                new LambdaQueryWrapper<RepaySchedule>()
                        .eq(RepaySchedule::getUserId, userId)
                        .eq(RepaySchedule::getDisburseId, disburseId));

        if (localList == null || localList.isEmpty()) {
            log.warn("【还款计划更新】未找到本地还款计划记录，disburseId: {}，无法进行更新，userId: {}", disburseId,
                    userId);
            return Collections.emptyMap(); // 返回空Map，避免空指针
        }

        Map<String, RepaySchedule> localMap = localList.stream()
                .collect(Collectors.toMap(RepaySchedule::getRepayTerm, Function.identity())); // 使用方法引用简化
        log.info("【还款计划更新】查询到本地还款计划 {} 条，disburseId: {}, userId: {}", localList.size(),
                disburseId, userId);
        return localMap;
    }

    /**
     * 对比计划并准备待更新的 RepaySchedule 列表和总利息
     *
     * @param remotePlanList 资方还款计划列表
     * @param localMap       本地还款计划Map (key: repayTerm)
     * @param disburseId     借款ID
     * @param userId         用户ID
     * @return UpdatePreparation 包含待更新列表和总利息
     */
    private UpdatePreparation prepareSchedulesForUpdate(List<RepaymentPlanQueryPkgVo> remotePlanList,
            Map<String, RepaySchedule> localMap, Long disburseId, Long userId) {

        List<RepaySchedule> saveList = new ArrayList<>(remotePlanList.size());
        BigDecimal totalRet = BigDecimal.ZERO; // 总利息

        for (RepaymentPlanQueryPkgVo li : remotePlanList) {
            RepaySchedule schedule = localMap.get(li.getRepayTerm());
            if (schedule == null) {
                log.warn("【还款计划更新】未找到本地对应的期数: {}，disburseId: {}，跳过，userId: {}",
                        li.getRepayTerm(), disburseId, userId);
                continue;
            }

            // 本地账单期数状态不为进行中，跳过更新
            if (!SettleFlagConstant.RUNNING.equals(schedule.getSettleFlag())) {
                log.info("【还款计划更新】本地账单期数 {} 状态不为RUNNING({})，跳过更新，disburseId: {}, userId: {}",
                        schedule.getRepayTerm(), schedule.getSettleFlag(), disburseId, userId);
                continue;
            }

            log.debug("【还款计划更新】准备更新期数: {}，disburseId: {}, userId: {}", li.getRepayTerm(), disburseId,
                    userId);
            RepaySchedule entity = new RepaySchedule();
            entity.setId(schedule.getId()); // 重要：设置ID进行更新！
            entity.setDisburseId(disburseId);
            entity.setUserId(userId);
            buildEntity(li, entity); // 从li填充实体

            // 计算总利息用于DisburseData更新（根据实际需要调整）
            BigDecimal termRetInt = entity.getTermRetInt() != null ? entity.getTermRetInt()
                    : BigDecimal.ZERO;
            BigDecimal termGuarantorFee = entity.getTermGuarantorFee() != null
                    ? entity.getTermGuarantorFee()
                    : BigDecimal.ZERO;
            BigDecimal termOverdueGuarantorFee = entity.getTermOverdueGuarantorFee() != null
                    ? entity.getTermOverdueGuarantorFee()
                    : BigDecimal.ZERO;

            totalRet = totalRet.add(termRetInt).add(termGuarantorFee).add(termOverdueGuarantorFee);
            saveList.add(entity);
        }
        return new UpdatePreparation(saveList, totalRet);
    }

    /**
     * 在事务中执行数据库更新
     *
     * @param schedulesToUpdate 待更新的还款计划列表
     * @param totalInterest     计算出的总利息
     * @param disburseId        借款ID
     * @param userId            用户ID
     * @param traceId           追踪ID
     */
    private void performDatabaseUpdatesInTransaction(List<RepaySchedule> schedulesToUpdate,
            BigDecimal totalInterest, Long disburseId, Long userId, String traceId) {

        log.info("【还款计划更新】开始执行数据库更新事务，disburseId: {}, userId: {}, 更新条数: {}", disburseId, userId,
                schedulesToUpdate.size());
        TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
        // 可以显式设置隔离级别和传播行为（如果需要覆盖默认值）
        // transactionTemplate.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);
        // transactionTemplate.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);

        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(TransactionStatus status) {
                try {
                    // 1. 更新支用数据总利息
                    DisburseData disburseDataUp = new DisburseData();
                    disburseDataUp.setId(disburseId);
                    disburseDataUp.setGrossInterest(totalInterest);
                    int disburseUpdateCount = disburseDataMapper.updateById(disburseDataUp);
                    log.info(
                            "【还款计划更新】[事务内] 更新DisburseData总利息完成，影响行数: {}, disburseId: {}, userId: {}, 更新总利息: {}",
                            disburseUpdateCount, disburseId, userId, totalInterest);

                    // 2. 批量更新还款计划
                    boolean scheduleUpdateSuccess = repayScheduleService.updateBatchById(schedulesToUpdate);
                    log.info(
                            "【还款计划更新】[事务内] 批量更新RepaySchedule完成，结果: {}，更新条数: {}, disburseId: {}, userId: {}",
                            scheduleUpdateSuccess, schedulesToUpdate.size(), disburseId, userId);

                    // 3. 检查批量更新是否成功
                    if (!scheduleUpdateSuccess) {
                        String errorMsg = "更新失败:批量更新还款计划失败，事务将回滚";
                        log.error("【还款计划更新】[事务内] 批量更新还款计划失败，标记事务为回滚。disburseId: {}, userId: {}",
                                disburseId, userId);
                        disburseRecordService.saveRecord(Constants.ERROR_TYPE_GENERAL,
                                "借款(disburseData)id:" + disburseId,
                                traceId, errorMsg);
                        status.setRollbackOnly();
                        log.info("【还款计划更新】[事务内] 事务已标记为回滚，disburseId: {}, userId: {}", disburseId,
                                userId);
                    } else {
                        log.info(
                                "【还款计划更新】[事务内] 数据库更新操作成功完成，准备提交事务。disburseId: {}, userId: {}",
                                disburseId, userId);
                        // 如果没有 setRollbackOnly，事务会自动提交
                    }
                } catch (Exception e) {
                    String errorMsg = "更新异常，事务将回滚: " + e.getMessage();
                    log.error(
                            "【还款计划更新】[事务内] 事务执行过程中发生异常，标记事务为回滚。disburseId: {}, userId: {}, error: ",
                            disburseId, userId, e);
                    disburseRecordService.saveRecord(Constants.ERROR_TYPE_GENERAL,
                            "借款(disburseData)id:" + disburseId,
                            traceId, errorMsg);
                    status.setRollbackOnly();
                    log.info("【还款计划更新】[事务内] 事务已因异常标记为回滚，disburseId: {}, userId: {}",
                            disburseId, userId);
                    // 注意：这里不需要重新抛出异常，setRollbackOnly 足够处理
                }
            }
        });
        log.info("【还款计划更新】数据库更新事务执行完毕，disburseId: {}, userId: {}", disburseId, userId);
    }

    /**
     * 将字符串转换为BigDecimal，如果字符串为空或无效，则返回BigDecimal.ZERO
     *
     * @param amountStr 金额的字符串表示形式
     * @return BigDecimal值或BigDecimal.Zero
     */
    private BigDecimal safeDecimalFromString(String amountStr) {
        if (StrUtil.isEmpty(amountStr)) {
            return BigDecimal.ZERO;
        }
        try {
            return new BigDecimal(amountStr);
        } catch (NumberFormatException e) {
            log.warn("【工具方法】无效的数字格式字符串: '{}', 返回 BigDecimal.ZERO.", amountStr);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 使用资方还款计划数据填充本地 RepaySchedule 实体 (用于更新)
     *
     * @param li     资方还款计划单期数据
     * @param entity 待填充的本地 RepaySchedule 实体
     */
    private void buildEntity(RepaymentPlanQueryPkgVo li, RepaySchedule entity) {
        // 注意：这里只填充需要根据资方更新的字段
        // ID, UserId, DisburseId 已在调用方设置
        entity.setRepayOwnbDate(li.getRepayOwnbDate());
        entity.setRepayOwneDate(li.getRepayOwneDate());
        entity.setRepayIntbDate(li.getRepayIntbDate());
        entity.setRepayInteDate(li.getRepayInteDate());
        entity.setTotalAmt(safeDecimalFromString(li.getTotalAmt()));
        entity.setTermRetPrin(safeDecimalFromString(li.getTermRetPrin()));
        entity.setTermRetInt(safeDecimalFromString(li.getTermRetInt()));
        entity.setTermGuarantorFee(safeDecimalFromString(li.getTermGuarantorFee()));
        entity.setTermRetFint(safeDecimalFromString(li.getTermRetFint()));
        entity.setTermOverdueGuarantorFee(safeDecimalFromString(li.getTermOverdueGuarantorFee()));
        entity.setPrinAmt(safeDecimalFromString(li.getPrinAmt()));
        entity.setNoRetAmt(safeDecimalFromString(li.getNoRetAmt()));
        entity.setIntAmt(safeDecimalFromString(li.getIntAmt()));
        entity.setNoRetInt(safeDecimalFromString(li.getNoRetInt()));
        entity.setTermFintFinish(safeDecimalFromString(li.getTermFintFinish()));
        entity.setNoRetFin(safeDecimalFromString(li.getNoRetFin()));
        entity.setGuarantorFee(safeDecimalFromString(li.getGuarantorFee()));
        entity.setNoGuarantorFee(safeDecimalFromString(li.getNoGuarantorFee()));
        entity.setTermServiceFee(safeDecimalFromString(li.getTermServiceFee()));
        entity.setServiceFee(safeDecimalFromString(li.getServiceFee()));
        entity.setNoServiceFee(safeDecimalFromString(li.getNoServiceFee()));
        entity.setOverdueGuarantorFee(safeDecimalFromString(li.getOverdueGuarantorFee()));
        entity.setNoOverdueGuarantorFee(safeDecimalFromString(li.getNoOverdueGuarantorFee()));
        entity.setTermStatus(li.getTermStatus());
        entity.setSettleFlag(li.getSettleFlag()); // 注意：资方接口的 settleFlag 应该会影响本地状态，这里直接更新
        entity.setRepayTerm(li.getRepayTerm());
        // create_time, update_time 等字段通常由数据库或MyBatis Plus自动填充，此处不设置
    }

    /**
     * 常量定义
     */
    private static final class Constants {
        // 响应码
        static final String SUCCESS_CODE = "0000";
        // 借款状态: 还款中
        static final int CREDIT_STATUS_REPAYING = 500;

        // 分布式锁相关
        static final String LOCK_KEY_PREFIX = "repay_plan_update:";
        static final long LOCK_WAIT_TIME = 3; // 锁等待时间(秒)
        static final long LOCK_LEASE_TIME = 60; // 锁持有时间(秒)

        // 重试相关
        static final int MAX_RETRY_TIMES = 3; // 最大重试次数
        static final long INITIAL_RETRY_DELAY_MS = 500; // 初始重试延迟(毫秒)
        // static final long MAX_RETRY_DELAY_MS = 5000; // 最大重试延迟(毫秒) - 可选用于指数退避

        // 错误类型
        static final int ERROR_TYPE_GENERAL = 3; // 一般错误类型
    }

    // 用于封装准备更新的数据
    private static class UpdatePreparation {
        final List<RepaySchedule> schedulesToUpdate;
        final BigDecimal totalInterest;

        UpdatePreparation(List<RepaySchedule> schedulesToUpdate, BigDecimal totalInterest) {
            this.schedulesToUpdate = schedulesToUpdate;
            this.totalInterest = totalInterest;
        }

        List<RepaySchedule> schedulesToUpdate() {
            return schedulesToUpdate;
        }

        BigDecimal totalInterest() {
            return totalInterest;
        }
    }
}