package com.rongchen.byh.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 赊销账单表
 * @TableName sale_schedule
 */
@TableName(value ="sale_schedule")
@Data
public class SaleSchedule implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 支用表id
     */
    private Long disburseId;

    /**
     * 赊销订单表id
     */
    private Long saleOrderId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 还款申请流水
     */
    private String repayApplyNo;

    /**
     * 还款期数
     */
    private String repayTerm;

    /**
     * 还款起日 自动扣款开始日期
     */
    private String repayOwnbDate;

    /**
     * 还款止日 自动扣款结束日期
     */
    private String repayOwneDate;

    /**
     * 本期起日 结息周期开始日期
     */
    private String repayIntbDate;

    /**
     * 本期止日 结息周期截止日期
     */
    private String repayInteDate;

    /**
     * 本期应还总金额
     */
    private BigDecimal totalAmt;

    /**
     * 本期应还本金
     */
    private BigDecimal termRetPrin;

    /**
     * 本期应还利息
     */
    private BigDecimal termRetInt;

    /**
     * 本期应还罚息
     */
    private BigDecimal termRetFint;

    /**
     * 状态 N - 正常，G - 宽限期，O - 逾期
     */
    private String termStatus;

    /**
     * 结清状态 RUNNING - 未结 ，CLOSE - 已结
     */
    private String settleFlag;

    /**
     * 还款发起时间
     */
    private String payTime;

    /**
     * 实际还款日
     */
    private String datePay;

    /**
     * 时间还款日期
     */
    private String datePayTime;

    /**
     * 自动扣款标识 1-自动扣款 0-不扣
     */
    private Integer autoRepay;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 减免本金
     */
    private BigDecimal deratePrin;

    /**
     * VIP减免本金
     */
    private BigDecimal vipDeratePrin;



    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}