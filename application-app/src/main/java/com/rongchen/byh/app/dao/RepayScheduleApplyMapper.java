package com.rongchen.byh.app.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rongchen.byh.app.dto.RepayApplyWithCapitalDto;
import com.rongchen.byh.app.entity.RepayScheduleApply;
import java.time.LocalDateTime;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 项目名称：byh_java
 * 文件名称: RepayScheduleApplyMapper
 * 创建时间: 2025-04-05 18:22
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.app.dao
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
public interface RepayScheduleApplyMapper extends BaseMapper<RepayScheduleApply> {

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(RepayScheduleApply record);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(RepayScheduleApply record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(RepayScheduleApply record);

    int updateBatch(@Param("list") List<RepayScheduleApply> list);

    int updateBatchSelective(@Param("list") List<RepayScheduleApply> list);

    int batchInsert(@Param("list") List<RepayScheduleApply> list);

    int batchInsertOrUpdate(@Param("list") List<RepayScheduleApply> list);

    RepayScheduleApply selectByRepayScheduleIdAndUserId(@Param("repayScheduleId") Long repayScheduleId,
        @Param("userId") Long userId);

    RepayScheduleApply selectByRepayApplyNo(String repayApplyNo);

    /**
     * 查询待处理的还款申请记录
     * @param cutoffTime 截止时间
     * @return 还款申请记录列表
     */

    List<RepayApplyWithCapitalDto> selectPendingAppliesWithCapital(@Param("cutoffTime") LocalDateTime cutoffTime);
    
}