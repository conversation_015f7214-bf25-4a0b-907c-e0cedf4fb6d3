package com.rongchen.byh.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 员工资方授信状态表
 * @TableName user_capital_record
 */
@TableName(value ="user_capital_record")
@Data
public class UserCapitalRecord implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 资方id
     */
    private Long capitalId;


    /**
     * 唯一流水号
     */
    private String serialNo;

    /**
     * 授信状态 0-待授信 1-授信通过 2-授信失败
     */
    private Integer creditStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date upcateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}