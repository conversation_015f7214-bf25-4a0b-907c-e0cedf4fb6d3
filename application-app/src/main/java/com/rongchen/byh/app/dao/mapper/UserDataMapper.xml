<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.app.dao.UserDataMapper">
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.rongchen.byh.app.entity.UserData" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `qiyecao12`.`user_data`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mobile != null">
        `mobile`,
      </if>
      <if test="statusFlag != null">
        `status_flag`,
      </if>
      <if test="auditFlag != null">
        `audit_flag`,
      </if>
      <if test="auditStatus != null">
        `audit_status`,
      </if>
      <if test="mobileMd != null">
        `mobile_md`,
      </if>
      <if test="nameMd5 != null">
        `name_md5`,
      </if>
      <if test="idCardMd5 != null">
        `id_card_md5`,
      </if>
      <if test="channelId != null">
        `channel_id`,
      </if>
      <if test="sourceMode != null">
        `source_mode`,
      </if>
      <if test="ip != null">
        `ip`,
      </if>
      <if test="createTime != null">
        `create_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="statusFlag != null">
        #{statusFlag,jdbcType=INTEGER},
      </if>
      <if test="auditFlag != null">
        #{auditFlag,jdbcType=INTEGER},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="mobileMd != null">
        #{mobileMd,jdbcType=VARCHAR},
      </if>
      <if test="nameMd5 != null">
        #{nameMd5,jdbcType=VARCHAR},
      </if>
      <if test="idCardMd5 != null">
        #{idCardMd5,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=BIGINT},
      </if>
      <if test="sourceMode != null">
        #{sourceMode,jdbcType=INTEGER},
      </if>
      <if test="ip != null">
        #{ip,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `qiyecao12`.`user_data`
    (`mobile`, `status_flag`, `audit_flag`, `audit_status`, `mobile_md`, `name_md5`,
      `id_card_md5`, `channel_id`, `source_mode`, `ip`, `create_time`)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.mobile,jdbcType=VARCHAR}, #{item.statusFlag,jdbcType=INTEGER}, #{item.auditFlag,jdbcType=INTEGER},
        #{item.auditStatus,jdbcType=INTEGER}, #{item.mobileMd,jdbcType=VARCHAR}, #{item.nameMd5,jdbcType=VARCHAR},
        #{item.idCardMd5,jdbcType=VARCHAR}, #{item.channelId,jdbcType=BIGINT}, #{item.sourceMode,jdbcType=INTEGER},
        #{item.ip,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertOrUpdate" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `qiyecao12`.`user_data`
    (`mobile`, `status_flag`, `audit_flag`, `audit_status`, `mobile_md`, `name_md5`,
      `id_card_md5`, `channel_id`, `source_mode`, `ip`, `create_time`)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.mobile,jdbcType=VARCHAR}, #{item.statusFlag,jdbcType=INTEGER}, #{item.auditFlag,jdbcType=INTEGER},
        #{item.auditStatus,jdbcType=INTEGER}, #{item.mobileMd,jdbcType=VARCHAR}, #{item.nameMd5,jdbcType=VARCHAR},
        #{item.idCardMd5,jdbcType=VARCHAR}, #{item.channelId,jdbcType=BIGINT}, #{item.sourceMode,jdbcType=INTEGER},
        #{item.ip,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP})
    </foreach>
    on duplicate key update
    mobile=values(mobile),
    status_flag=values(status_flag),
    audit_flag=values(audit_flag),
    audit_status=values(audit_status),
    mobile_md=values(mobile_md),
    name_md5=values(name_md5),
    id_card_md5=values(id_card_md5),
    channel_id=values(channel_id),
    source_mode=values(source_mode),
    ip=values(ip),
    create_time=values(create_time)
  </insert>
  <resultMap id="BaseResultMap" type="com.rongchen.byh.app.entity.UserData">
    <!--@mbg.generated-->
    <!--@Table `qiyecao12`.`user_data`-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="status_flag" jdbcType="INTEGER" property="statusFlag" />
    <result column="audit_flag" jdbcType="INTEGER" property="auditFlag" />
    <result column="audit_status" jdbcType="INTEGER" property="auditStatus" />
    <result column="mobile_md" jdbcType="VARCHAR" property="mobileMd" />
    <result column="name_md5" jdbcType="VARCHAR" property="nameMd5" />
    <result column="id_card_md5" jdbcType="VARCHAR" property="idCardMd5" />
    <result column="channel_id" jdbcType="BIGINT" property="channelId" />
    <result column="source_mode" jdbcType="INTEGER" property="sourceMode" />
    <result column="ip" jdbcType="VARCHAR" property="ip" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
<!--  <resultMap id="BaseResultMap" type="com.rongchen.byh.app.entity.UserData">-->
<!--        <id column="id" jdbcType="BIGINT" property="id" />-->
<!--        <result column="mobile" jdbcType="VARCHAR" property="mobile" />-->
<!--        <result column="status_flag" jdbcType="INTEGER" property="statusFlag" />-->
<!--        <result column="audit_flag" jdbcType="INTEGER" property="auditFlag" />-->
<!--        <result column="audit_status" jdbcType="INTEGER" property="auditStatus" />-->
<!--        <result column="source_mode" jdbcType="INTEGER" property="sourceMode" />-->
<!--        <result column="mobile_md" jdbcType="VARCHAR" property="mobileMd" />-->
<!--        <result column="channel_id" jdbcType="BIGINT" property="channelId" />-->
<!--        <result column="ip" jdbcType="VARCHAR" property="ip" />-->
<!--        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />-->
<!--    </resultMap>-->
  <select id="queryByMobile" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from user_data
        where mobile = #{mobile}
    </select>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `id`, `mobile`, `status_flag`, `audit_flag`, `audit_status`, `mobile_md`, `name_md5`,
    `id_card_md5`, `channel_id`, `source_mode`, `ip`, `create_time`
  </sql>
  <update id="updateByPrimaryKeySelective" parameterType="com.rongchen.byh.app.entity.UserData">
    <!--@mbg.generated-->
    update `qiyecao12`.`user_data`
    <set>
      <if test="mobile != null">
        `mobile` = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="statusFlag != null">
        `status_flag` = #{statusFlag,jdbcType=INTEGER},
      </if>
      <if test="auditFlag != null">
        `audit_flag` = #{auditFlag,jdbcType=INTEGER},
      </if>
      <if test="auditStatus != null">
        `audit_status` = #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="mobileMd != null">
        `mobile_md` = #{mobileMd,jdbcType=VARCHAR},
      </if>
      <if test="nameMd5 != null">
        `name_md5` = #{nameMd5,jdbcType=VARCHAR},
      </if>
      <if test="idCardMd5 != null">
        `id_card_md5` = #{idCardMd5,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        `channel_id` = #{channelId,jdbcType=BIGINT},
      </if>
      <if test="sourceMode != null">
        `source_mode` = #{sourceMode,jdbcType=INTEGER},
      </if>
      <if test="ip != null">
        `ip` = #{ip,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        `create_time` = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.rongchen.byh.app.entity.UserData">
    <!--@mbg.generated-->
    update `qiyecao12`.`user_data`
    set `mobile` = #{mobile,jdbcType=VARCHAR},
      `status_flag` = #{statusFlag,jdbcType=INTEGER},
      `audit_flag` = #{auditFlag,jdbcType=INTEGER},
      `audit_status` = #{auditStatus,jdbcType=INTEGER},
      `mobile_md` = #{mobileMd,jdbcType=VARCHAR},
      `name_md5` = #{nameMd5,jdbcType=VARCHAR},
      `id_card_md5` = #{idCardMd5,jdbcType=VARCHAR},
      `channel_id` = #{channelId,jdbcType=BIGINT},
      `source_mode` = #{sourceMode,jdbcType=INTEGER},
      `ip` = #{ip,jdbcType=VARCHAR},
      `create_time` = #{createTime,jdbcType=TIMESTAMP}
    where `id` = #{id,jdbcType=BIGINT}
  </update>

    <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update `qiyecao12`.`user_data`
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
    where `id` in
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`mobile` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.mobile,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`status_flag` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.statusFlag,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="`audit_flag` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.auditFlag,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="`audit_status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.auditStatus,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="`mobile_md` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.mobileMd,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`name_md5` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.nameMd5,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`id_card_md5` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.idCardMd5,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`channel_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.channelId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="`source_mode` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.sourceMode,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="`ip` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.ip,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`create_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
    </trim>
  </update>

<!--    <sql id="Base_Column_List">-->
<!--        id,mobile,status_flag,-->
<!--        audit_flag,audit_status,mobile_md,-->
<!--        channel_id,ip,create_time,source_mode-->
<!--    </sql>-->


    <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update `qiyecao12`.`user_data`
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
    where `id` in
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`mobile` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.mobile != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.mobile,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`status_flag` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.statusFlag != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.statusFlag,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="`audit_flag` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.auditFlag != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.auditFlag,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="`audit_status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.auditStatus != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.auditStatus,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="`mobile_md` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.mobileMd != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.mobileMd,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`name_md5` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.nameMd5 != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.nameMd5,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`id_card_md5` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.idCardMd5 != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.idCardMd5,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`channel_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.channelId != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.channelId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="`source_mode` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.sourceMode != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.sourceMode,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="`ip` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.ip != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.ip,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`create_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
    </trim>
  </update>
    <select id="selectSaleRecordByMobile" resultType="java.lang.Integer">
        select staff_audit_record.id
        from user_data
                 left join user_staff on user_data.id = user_staff.user_id
                 left join staff_audit_record on user_staff.id = staff_audit_record.user_staff_id
        where user_data.mobile = #{mobile}
    </select>


</mapper>