<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.app.dao.RepaySaleApplyMapper">

    <resultMap id="BaseResultMap" type="com.rongchen.byh.app.entity.RepaySaleApply">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="saleScheduleId" column="sale_schedule_id" jdbcType="BIGINT"/>
            <result property="repayApplyNo" column="repay_apply_no" jdbcType="VARCHAR"/>
            <result property="userId" column="user_id" jdbcType="INTEGER"/>
            <result property="repayType" column="repay_type" jdbcType="INTEGER"/>
            <result property="repayStatus" column="repay_status" jdbcType="INTEGER"/>
            <result property="reason" column="reason" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="responseTime" column="response_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        sale_schedule_id,
        repay_apply_no,
        user_id,
        repay_type,
        repay_status,
        reason,
        create_time,
        update_time,
        response_time
    </sql>
    <select id="selectByRepaySaleScheduleIdAndUserId" resultType="com.rongchen.byh.app.entity.RepaySaleApply">
        select <include refid="Base_Column_List"/>
        from repay_sale_apply
        where repay_schedule_id = #{repayScheduleId} and user_id = #{userId} order by id desc limit 1
    </select>
    <select id="selectByRepayApplyNo" resultType="com.rongchen.byh.app.entity.RepaySaleApply">
        select *
        from repay_sale_apply
        where repay_apply_no = #{repayApplyNo}
    </select>
</mapper>
