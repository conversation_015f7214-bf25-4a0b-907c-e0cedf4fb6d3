package com.rongchen.byh.app.api.dto.credit;


import lombok.Data;

@Data
public class CreditQueryDto {


    /**
     * 参数名称: creditNo
     * 参数描述: 授信流水号
     * 参数类型: String
     * 最大长度: 32
     * 是否必填: Y
     */
    private String creditNo;

    /**
     * 参数名称: userId
     * 参数描述: 用户编号
     * 参数类型: String
     * 最大长度: 32
     * 是否必填: Y
     */
    private String userId;

    /**
     * 参数名称: reqSysCode
     * 参数描述: 请求方
     * 参数类型: String
     * 最大长度: 32
     * 是否必填: Y
     * 备注: 见接口定义概述
     */
    private String reqSysCode;

    /**
     * 参数名称: productCode
     * 参数描述: 产品编码
     * 参数类型: String
     * 最大长度: 8
     * 是否必填: Y
     */
    private String productCode;

    /**
     * 参数名称: fundCode
     * 参数描述: 资金方编码
     * 参数类型: String
     * 最大长度: 8
     * 是否必填: N
     */
    private String fundCode;
}
